# Soroban Learning Platform

A comprehensive online learning platform for teaching Soroban (Japanese Abacus) mental arithmetic, built with a decoupled architecture using Laravel API backend and Vue.js frontend.

## 🏗️ Architecture

This project follows a **decoupled architecture** with separate frontend and backend:

- **Backend**: Laravel 12 REST API (`Soroban-Backend/`)
- **Frontend**: Vue.js 3 + TypeScript SPA (`Soroban-front/`)

## 🚀 Quick Start

### Prerequisites
- PHP 8.2+
- Composer
- Node.js 18+
- MySQL 8.0+

### 1. Clone and Setup
```bash
git clone <repository-url>
cd soroban
```

### 2. Backend Setup
```bash
cd Soroban-Backend
composer install
cp .env.example .env
php artisan key:generate
# Configure database in .env
php artisan migrate
php artisan db:seed
```

### 3. Frontend Setup
```bash
cd ../Soroban-front
npm install
cp .env.example .env
# Configure API URL in .env
```

### 4. Start Development Servers

**Option A: Use convenience scripts**
```bash
# Windows Batch
start-dev.bat

# PowerShell
./start-dev.ps1
```

**Option B: Manual start**
```bash
# Terminal 1 - Backend
cd Soroban-Backend
php artisan serve

# Terminal 2 - Frontend  
cd Soroban-front
npm run dev
```

### 5. Access the Application
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:8000/api
- **API Documentation**: http://localhost:8000/api/documentation

## 📋 Features

### 🎓 Educational Platform
- **Course Management** with multiple levels and lessons
- **Interactive Quizzes** and comprehensive exams
- **Progress Tracking** with detailed analytics
- **Certificate Generation** upon course completion
- **Video Content** integration with playlists

### 👥 User Management
- **Role-based Access Control** (Super Admin, Admin, Teacher, Student)
- **User Profiles** with detailed information
- **Company Management** for institutional users
- **Email Verification** and password reset

### 💳 Business Features
- **Subscription Management** with flexible plans
- **Payment Processing** with transaction history
- **Discount System** with promotional codes
- **Revenue Analytics** and reporting

### 🌐 Community Features
- **WhatsApp Group Integration** for community building
- **Group Management** with member controls
- **Notification System** for updates and announcements
- **Competition Management** with leaderboards

## 🛠️ Technology Stack

### Backend (Laravel API)
- **Framework**: Laravel 12
- **Database**: MySQL
- **Authentication**: Laravel Sanctum
- **Documentation**: Swagger/OpenAPI
- **File Storage**: Laravel Filesystem
- **Queue System**: Database queues

### Frontend (Vue.js SPA)
- **Framework**: Vue.js 3 + TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **Icons**: Lucide Vue + FontAwesome
- **Routing**: Vue Router 4
- **State Management**: Composables

## 📁 Project Structure

```
soroban/
├── Soroban-Backend/          # Laravel API Backend
│   ├── app/                  # Application logic
│   ├── config/               # Configuration files
│   ├── database/             # Migrations, seeders, schema
│   ├── routes/               # API routes
│   └── docs/                 # API documentation
├── Soroban-front/            # Vue.js Frontend
│   ├── src/
│   │   ├── components/       # Reusable components
│   │   ├── screens/          # Page components
│   │   ├── services/         # API service layer
│   │   ├── composables/      # Vue composables
│   │   └── router/           # Route configuration
├── start-dev.bat             # Windows development script
├── start-dev.ps1             # PowerShell development script
└── README.md                 # This file
```

## 🔐 Authentication & Authorization

### Authentication Flow
1. User logs in via Vue.js frontend
2. Laravel API validates credentials
3. Sanctum token issued and stored
4. Token used for subsequent API requests
5. Role-based route protection in frontend

### User Roles
- **Super Admin**: Full system access, company management
- **Admin**: User management, course oversight, reports
- **Teacher**: Course creation, student management, grading
- **Student**: Course enrollment, progress tracking, certificates

## 🌐 API Integration

### Backend API Endpoints
- **Authentication**: `/api/login`, `/api/register`, `/api/logout`
- **User Management**: `/api/users`, `/api/user/profile`
- **Courses**: `/api/courses`, `/api/course-levels`, `/api/lessons`
- **Assessments**: `/api/quizzes`, `/api/exams`, `/api/student-exams`
- **Progress**: `/api/student-progress`, `/api/certificates`
- **Business**: `/api/subscriptions`, `/api/payments`, `/api/discounts`

### Frontend API Service
- **Centralized API client** with TypeScript interfaces
- **Automatic token management** and refresh
- **Error handling** with user-friendly messages
- **Request/response interceptors** for common operations

## 🧪 Development

### Backend Development
```bash
cd Soroban-Backend
php artisan serve          # Start development server
php artisan test           # Run tests
php artisan migrate:fresh  # Reset database
php artisan l5-swagger:generate  # Generate API docs
```

### Frontend Development
```bash
cd Soroban-front
npm run dev                # Start development server
npm run build              # Build for production
npm run type-check         # TypeScript checking
```

## 🚀 Deployment

### Backend Deployment
1. Configure production environment
2. Set up database and run migrations
3. Configure web server (Apache/Nginx)
4. Set up SSL certificates
5. Configure queue workers

### Frontend Deployment
1. Build production assets: `npm run build`
2. Deploy `dist/` folder to static hosting
3. Configure environment variables
4. Set up CDN for assets

### Recommended Hosting
- **Backend**: VPS, AWS EC2, DigitalOcean
- **Frontend**: Netlify, Vercel, AWS S3 + CloudFront
- **Database**: AWS RDS, DigitalOcean Managed Database

## 📊 Database Schema

The platform includes 21+ tables covering:
- User management and authentication
- Course structure and content
- Assessment and progress tracking
- Payment and subscription management
- Community and group features

See `Soroban-Backend/database/SCHEMA_ANALYSIS.md` for detailed schema documentation.

## 📚 Documentation

- **API Documentation**: Available at `/api/documentation` when backend is running
- **Backend README**: `Soroban-Backend/README.md`
- **Frontend README**: `Soroban-front/README.md`
- **Database Schema**: `Soroban-Backend/database/SCHEMA_ANALYSIS.md`
- **Swagger Guide**: `Soroban-Backend/docs/Swagger_Documentation_Guide.md`

## 🔧 Configuration

### Environment Variables

**Backend (.env)**
```env
APP_URL=http://localhost:8000
APP_FRONTEND_URL=http://localhost:5173
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_DATABASE=soroban_albark
SANCTUM_STATEFUL_DOMAINS=localhost:5173
```

**Frontend (.env)**
```env
VITE_API_BASE_URL=http://localhost:8000/api
VITE_BACKEND_URL=http://localhost:8000
```

## 📞 Support

For support and questions:
1. Check the API documentation at `/api/documentation`
2. Review the individual README files for each component
3. Contact the development team

## 📄 License

This project is proprietary software. All rights reserved.
