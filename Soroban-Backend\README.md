# Soroban Learning Platform - Backend API

A comprehensive REST API for the Soroban (Japanese Abacus) learning platform built with Laravel 12.

## 🚀 Features

- **Complete REST API** for educational platform
- **Laravel Sanctum Authentication** with Bearer tokens
- **Role-based Access Control** (SuperAdmin, Admin, Teacher, Student)
- **Comprehensive Course Management** with levels, lessons, quizzes, and exams
- **Student Progress Tracking** and certification system
- **Payment and Subscription Management**
- **WhatsApp Group Integration** for community features
- **File Upload and Management** for educational content
- **Email Verification** and password reset functionality
- **Swagger/OpenAPI Documentation** at `/api/documentation`

## 🛠️ Technology Stack

- **Framework**: Laravel 12
- **Database**: MySQL
- **Authentication**: Laravel Sanctum
- **Documentation**: L5-Swagger (OpenAPI 3.0)
- **File Storage**: Laravel Filesystem
- **Email**: Laravel Mail with queue support

## 📋 API Endpoints

### Authentication
- `POST /api/register` - User registration
- `POST /api/login` - User login
- `POST /api/logout` - User logout (requires auth)
- `GET /api/user` - Get authenticated user
- `POST /api/forgot-password` - Send password reset email
- `POST /api/reset-password` - Reset password with token

### Courses & Education
- `GET /api/courses` - List all courses (public)
- `GET /api/courses/{id}` - Get course details (public)
- `POST /api/courses` - Create course (auth required)
- `GET /api/course-levels` - List course levels
- `GET /api/lessons` - List lessons
- `GET /api/quizzes` - List quizzes
- `GET /api/exams` - List exams

### User Management
- `GET /api/users` - List users (admin only)
- `POST /api/users` - Create user (admin only)
- `PUT /api/users/{id}` - Update user (admin only)

### Student Features
- `GET /api/student/dashboard` - Student dashboard
- `GET /api/student/my-courses` - Student's enrolled courses
- `GET /api/student/my-progress` - Student's progress
- `GET /api/student/my-certificates` - Student's certificates

For complete API documentation, visit: `http://localhost:8000/api/documentation`

## 🚀 Quick Start

### Prerequisites
- PHP 8.2+
- Composer
- MySQL 8.0+
- Node.js (for documentation assets)

### Installation

1. **Clone and setup**
   ```bash
   git clone <repository-url>
   cd Soroban-Backend
   composer install
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

3. **Database Setup**
   ```bash
   # Update .env with your database credentials
   php artisan migrate
   php artisan db:seed
   ```

4. **Start the API Server**
   ```bash
   php artisan serve
   # API will be available at http://localhost:8000
   ```

5. **Generate API Documentation**
   ```bash
   php artisan l5-swagger:generate
   # Visit http://localhost:8000/api/documentation
   ```

## 🔧 Configuration

### CORS Settings
The API is configured to accept requests from:
- `http://localhost:5173` (Vue.js dev server)
- `http://localhost:3000` (React dev server)
- `http://localhost:8080` (Alternative Vue.js port)

### Authentication
- Uses Laravel Sanctum for API token authentication
- Supports both stateful (SPA) and stateless (mobile) authentication
- Role-based access control with roles: `superAdmin`, `admin`, `teacher`, `student`, `guest`

### Database Schema
The platform includes 21+ tables covering:
- User management and profiles
- Course structure (courses, levels, lessons)
- Assessment system (quizzes, exams)
- Progress tracking and certificates
- Payment and subscription management
- WhatsApp group integration

## 🧪 Testing

```bash
# Run tests
php artisan test

# Run specific test suite
php artisan test --testsuite=Feature
```

## 📚 Documentation

- **API Documentation**: `http://localhost:8000/api/documentation`
- **Database Schema**: See `database/SCHEMA_ANALYSIS.md`
- **Swagger Guide**: See `docs/Swagger_Documentation_Guide.md`

## 🔒 Security

- CSRF protection disabled for API routes
- Rate limiting: 60 requests per minute per user/IP
- Email verification required for new accounts
- Password reset with secure tokens
- Role-based authorization middleware

## 📞 Support

For support and questions, please refer to the API documentation or contact the development team.

## 📄 License

This project is proprietary software. All rights reserved.
