<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class GenerateSwaggerDocs extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'swagger:generate-docs';

    /**
     * The console command description.
     */
    protected $description = 'Generate comprehensive Swagger documentation for all API endpoints';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('📚 Generating comprehensive Swagger documentation...');
        
        // List of controllers and their endpoints
        $controllers = [
            'AuthController' => [
                'register' => 'POST /api/register - Register new user',
                'login' => 'POST /api/login - User login',
                'logout' => 'POST /api/logout - User logout',
                'forgotPassword' => 'POST /api/forgot-password - Send password reset email',
                'resetPassword' => 'POST /api/reset-password - Reset password',
                'sendVerificationEmail' => 'POST /api/email/verification-notification - Resend verification email',
                'verifyEmail' => 'GET /api/email/verify/{id}/{hash} - Verify email address',
                'checkEmailVerification' => 'GET /api/email/verify-status - Check verification status',
            ],
            'UserController' => [
                'index' => 'GET /api/users - Get all users',
                'show' => 'GET /api/users/{id} - Get specific user',
                'store' => 'POST /api/users - Create new user',
                'update' => 'PUT /api/users/{id} - Update user',
                'destroy' => 'DELETE /api/users/{id} - Delete user',
            ],
            'CourseController' => [
                'index' => 'GET /api/courses - Get all courses',
                'show' => 'GET /api/courses/{id} - Get specific course',
                'store' => 'POST /api/courses - Create new course',
                'update' => 'PUT /api/courses/{id} - Update course',
                'destroy' => 'DELETE /api/courses/{id} - Delete course',
            ],
            'CourseLevelController' => [
                'index' => 'GET /api/course-levels - Get all course levels',
                'show' => 'GET /api/course-levels/{id} - Get specific level',
                'store' => 'POST /api/course-levels - Create new level',
                'update' => 'PUT /api/course-levels/{id} - Update level',
                'destroy' => 'DELETE /api/course-levels/{id} - Delete level',
            ],
            'LessonController' => [
                'index' => 'GET /api/lessons - Get all lessons',
                'show' => 'GET /api/lessons/{id} - Get specific lesson',
                'store' => 'POST /api/lessons - Create new lesson',
                'update' => 'PUT /api/lessons/{id} - Update lesson',
                'destroy' => 'DELETE /api/lessons/{id} - Delete lesson',
            ],
            'SubscriptionController' => [
                'index' => 'GET /api/subscriptions - Get all subscriptions',
                'show' => 'GET /api/subscriptions/{id} - Get specific subscription',
                'store' => 'POST /api/subscriptions - Create new subscription',
                'update' => 'PUT /api/subscriptions/{id} - Update subscription',
                'destroy' => 'DELETE /api/subscriptions/{id} - Delete subscription',
            ],
            'GroupController' => [
                'index' => 'GET /api/groups - Get all WhatsApp groups',
                'show' => 'GET /api/groups/{id} - Get specific group',
                'store' => 'POST /api/groups - Create new group',
                'update' => 'PUT /api/groups/{id} - Update group',
                'destroy' => 'DELETE /api/groups/{id} - Delete group',
            ],
            'CertificateController' => [
                'index' => 'GET /api/certificates - Get all certificates',
                'show' => 'GET /api/certificates/{id} - Get specific certificate',
                'store' => 'POST /api/certificates - Create new certificate',
                'update' => 'PUT /api/certificates/{id} - Update certificate',
                'destroy' => 'DELETE /api/certificates/{id} - Delete certificate',
            ],
            'NotificationController' => [
                'index' => 'GET /api/notifications - Get all notifications',
                'show' => 'GET /api/notifications/{id} - Get specific notification',
                'store' => 'POST /api/notifications - Create new notification',
                'update' => 'PUT /api/notifications/{id} - Update notification',
                'destroy' => 'DELETE /api/notifications/{id} - Delete notification',
            ],
        ];

        $this->newLine();
        $this->info('📋 Available API Endpoints:');
        $this->newLine();

        foreach ($controllers as $controller => $endpoints) {
            $this->line("<fg=cyan>🎯 {$controller}:</>");
            foreach ($endpoints as $method => $description) {
                $this->line("  ✅ {$description}");
            }
            $this->newLine();
        }

        $this->info('🚀 To see all documented endpoints, visit:');
        $this->line('<fg=green>http://localhost:8000/api/documentation</fg=green>');
        
        $this->newLine();
        $this->info('💡 Tips for using Swagger UI:');
        $this->line('1. Click "Authorize" to add your Bearer token');
        $this->line('2. Test endpoints directly from the browser');
        $this->line('3. Copy curl commands for testing');
        $this->line('4. View detailed request/response schemas');
        
        $this->newLine();
        $this->info('🔧 To regenerate documentation after changes:');
        $this->line('<fg=yellow>php artisan l5-swagger:generate</fg=yellow>');

        return 0;
    }
}
