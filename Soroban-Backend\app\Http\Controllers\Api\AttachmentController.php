<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Attachment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;

class AttachmentController extends Controller
{
    /**
     * Display a listing of attachments
     */
    public function index(Request $request)
    {
        $query = Attachment::with(['uploader']);

        // Filter by file type
        if ($request->has('file_type')) {
            $query->where('file_type', $request->file_type);
        }

        // Filter by uploader
        if ($request->has('uploaded_by')) {
            $query->where('uploaded_by', $request->uploaded_by);
        }

        // Search by filename
        if ($request->has('search')) {
            $query->where('file_url', 'like', '%' . $request->search . '%');
        }

        $attachments = $query->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $attachments
        ]);
    }

    /**
     * Store a new attachment
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file_url' => 'required|string',
            'file_type' => 'required|in:pdf,video,image,zip,other',
            'file_size' => 'nullable|integer',
            'mime_type' => 'nullable|string|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $attachment = Attachment::create([
                'file_url' => $request->file_url,
                'file_type' => $request->file_type,
                'file_size' => $request->file_size,
                'mime_type' => $request->mime_type,
                'uploaded_by' => Auth::id(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Attachment created successfully',
                'data' => $attachment->load('uploader')
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Attachment creation failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display a specific attachment
     */
    public function show(Attachment $attachment)
    {
        return response()->json([
            'success' => true,
            'data' => $attachment->load('uploader')
        ]);
    }

    /**
     * Update an attachment
     */
    public function update(Request $request, Attachment $attachment)
    {
        $validator = Validator::make($request->all(), [
            'file_url' => 'sometimes|string',
            'file_type' => 'sometimes|in:pdf,video,image,zip,other',
            'file_size' => 'nullable|integer',
            'mime_type' => 'nullable|string|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $attachment->update($request->only([
                'file_url', 'file_type', 'file_size', 'mime_type'
            ]));

            return response()->json([
                'success' => true,
                'message' => 'Attachment updated successfully',
                'data' => $attachment->load('uploader')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Attachment update failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete an attachment
     */
    public function destroy(Attachment $attachment)
    {
        try {
            // Delete file from storage if it's a local file
            if (!filter_var($attachment->file_url, FILTER_VALIDATE_URL)) {
                Storage::delete($attachment->file_url);
            }

            $attachment->delete();

            return response()->json([
                'success' => true,
                'message' => 'Attachment deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Attachment deletion failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload a file
     */
    public function upload(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|max:51200', // 50MB max
            'file_type' => 'required|in:pdf,video,image,zip,other',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $file = $request->file('file');

            // Store file
            $path = $file->store('attachments', 'public');
            $url = Storage::url($path);

            // Create attachment record
            $attachment = Attachment::create([
                'file_url' => $url,
                'file_type' => $request->file_type,
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'uploaded_by' => Auth::id(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'File uploaded successfully',
                'data' => $attachment->load('uploader')
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'File upload failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
