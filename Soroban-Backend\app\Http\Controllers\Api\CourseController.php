<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Course;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class CourseController extends Controller
{
    /**
     * @OA\Get(
     *     path="/courses",
     *     summary="Get all courses",
     *     description="Retrieve a paginated list of all courses with their levels",
     *     tags={"Courses"},
     *     @OA\Parameter(
     *         name="search",
     *         in="query",
     *         description="Search courses by name or description",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Number of courses per page",
     *         required=false,
     *         @OA\Schema(type="integer", default=15)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Courses retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="object",
     *                 @OA\Property(property="data", type="array",
     *                     @OA\Items(
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="name", type="string", example="Soroban Fundamentals"),
     *                         @OA\Property(property="description", type="string", example="Learn the basics of Soroban calculation"),
     *                         @OA\Property(property="levels", type="array", @OA\Items(type="object"))
     *                     )
     *                 ),
     *                 @OA\Property(property="current_page", type="integer"),
     *                 @OA\Property(property="total", type="integer")
     *             )
     *         )
     *     )
     * )
     */
    public function index(Request $request)
    {
        $query = Course::with(['levels']);

        // Search by name or description
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $courses = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $courses
        ]);
    }

    public function show(Course $course)
    {
        return response()->json([
            'success' => true,
            'data' => $course->load(['levels.lessons', 'levels.exams'])
        ]);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $course = Course::create($request->only(['name', 'description']));

            return response()->json([
                'success' => true,
                'message' => 'Course created successfully',
                'data' => $course
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Course creation failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function update(Request $request, Course $course)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'description' => 'sometimes|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $course->update($request->only(['name', 'description']));

            return response()->json([
                'success' => true,
                'message' => 'Course updated successfully',
                'data' => $course->fresh()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Course update failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function destroy(Course $course)
    {
        try {
            $course->delete();

            return response()->json([
                'success' => true,
                'message' => 'Course deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Course deletion failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function courseReports(Request $request)
    {
        $stats = [
            'total_courses' => Course::count(),
            'courses_with_levels' => Course::has('levels')->count(),
            'total_levels' => \App\Models\CourseLevel::count(),
            'total_lessons' => \App\Models\Lesson::count(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }
}
