<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Exam;
use App\Models\CourseLevel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ExamController extends Controller
{
    /**
     * Display a listing of exams
     */
    public function index(Request $request)
    {
        $query = Exam::with(['level.course']);

        // Filter by level
        if ($request->has('level_id')) {
            $query->where('level_id', $request->level_id);
        }

        // Filter by exam type
        if ($request->has('exam_type')) {
            $query->where('exam_type', $request->exam_type);
        }

        // Search by title
        if ($request->has('search')) {
            $query->where('title', 'like', '%' . $request->search . '%');
        }

        $exams = $query->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $exams
        ]);
    }

    /**
     * Store a new exam with Google Forms integration
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'level_id' => 'required|exists:course_levels,id',
            'title' => 'required|string|max:255',
            'exam_type' => 'required|in:placement,final',
            'iframe_url' => 'required|url',
            'time_limit' => 'nullable|integer|min:1',
            'max_attempts' => 'nullable|integer|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Validate Google Forms URL
            if (!$this->isValidGoogleFormsUrl($request->iframe_url)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid Google Forms URL. Please provide a valid Google Forms embed URL.'
                ], 422);
            }

            $exam = Exam::create([
                'level_id' => $request->level_id,
                'title' => $request->title,
                'exam_type' => $request->exam_type,
                'iframe_url' => $request->iframe_url,
                'time_limit' => $request->time_limit,
                'max_attempts' => $request->max_attempts ?? 1,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Exam created successfully',
                'data' => $exam->load(['level.course'])
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Exam creation failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display a specific exam
     */
    public function show(Exam $exam)
    {
        return response()->json([
            'success' => true,
            'data' => $exam->load(['level.course'])
        ]);
    }

    /**
     * Update an exam
     */
    public function update(Request $request, Exam $exam)
    {
        $validator = Validator::make($request->all(), [
            'level_id' => 'sometimes|exists:course_levels,id',
            'title' => 'sometimes|string|max:255',
            'exam_type' => 'sometimes|in:placement,final',
            'iframe_url' => 'sometimes|url',
            'time_limit' => 'nullable|integer|min:1',
            'max_attempts' => 'nullable|integer|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Validate Google Forms URL if provided
            if ($request->has('iframe_url') && !$this->isValidGoogleFormsUrl($request->iframe_url)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid Google Forms URL. Please provide a valid Google Forms embed URL.'
                ], 422);
            }

            $exam->update($request->only([
                'level_id', 'title', 'exam_type', 'iframe_url', 'time_limit', 'max_attempts'
            ]));

            return response()->json([
                'success' => true,
                'message' => 'Exam updated successfully',
                'data' => $exam->load(['level.course'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Exam update failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete an exam
     */
    public function destroy(Exam $exam)
    {
        try {
            $exam->delete();

            return response()->json([
                'success' => true,
                'message' => 'Exam deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Exam deletion failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get exams by level
     */
    public function getByLevel(CourseLevel $level, Request $request)
    {
        $query = $level->exams();

        // Filter by exam type
        if ($request->has('exam_type')) {
            $query->where('exam_type', $request->exam_type);
        }

        $exams = $query->orderBy('exam_type')->get();

        return response()->json([
            'success' => true,
            'data' => $exams
        ]);
    }

    /**
     * Generate Google Forms embed HTML for exam
     */
    public function getExamEmbed(Exam $exam, Request $request)
    {
        $embedUrl = $this->generateGoogleFormsEmbedUrl($exam->iframe_url);

        $width = $request->get('width', 640);
        $height = $request->get('height', 800);

        $iframeHtml = sprintf(
            '<iframe src="%s" width="%d" height="%d" frameborder="0" marginheight="0" marginwidth="0">Loading…</iframe>',
            $embedUrl,
            $width,
            $height
        );

        return response()->json([
            'success' => true,
            'data' => [
                'exam_id' => $exam->id,
                'exam_title' => $exam->title,
                'exam_type' => $exam->exam_type,
                'embed_url' => $embedUrl,
                'iframe_html' => $iframeHtml,
                'time_limit' => $exam->time_limit,
                'max_attempts' => $exam->max_attempts
            ]
        ]);
    }

    /**
     * Validate Google Forms URL
     */
    private function isValidGoogleFormsUrl($url)
    {
        $patterns = [
            '/^https:\/\/docs\.google\.com\/forms\/d\/[a-zA-Z0-9-_]+\/viewform/',
            '/^https:\/\/forms\.gle\/[a-zA-Z0-9-_]+/',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $url)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Generate Google Forms embed URL
     */
    private function generateGoogleFormsEmbedUrl($originalUrl)
    {
        // Convert regular Google Forms URL to embed URL
        if (strpos($originalUrl, '/viewform') !== false) {
            return str_replace('/viewform', '/viewform?embedded=true', $originalUrl);
        }

        // If it's already an embed URL, return as is
        if (strpos($originalUrl, 'embedded=true') !== false) {
            return $originalUrl;
        }

        // Add embedded parameter
        $separator = strpos($originalUrl, '?') !== false ? '&' : '?';
        return $originalUrl . $separator . 'embedded=true';
    }
}
