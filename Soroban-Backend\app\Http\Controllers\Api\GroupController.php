<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Group;
use App\Models\GroupMember;
use App\Models\User;
use App\Models\CourseLevel;
use App\Models\Subscription;
use App\Models\Notification;
use App\Models\WhatsappLinkHistory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class GroupController extends Controller
{
    /**
     * Display groups with filtering
     */
    public function index(Request $request)
    {
        $query = Group::with(['level.course', 'teacher.profile', 'members.user.profile']);

        // Filter by level
        if ($request->has('level_id')) {
            $query->where('level_id', $request->level_id);
        }

        // Filter by teacher
        if ($request->has('teacher_id')) {
            $query->where('teacher_id', $request->teacher_id);
        }

        // Filter by active status
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        // Search by name
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by WhatsApp link status
        if ($request->has('has_whatsapp')) {
            if ($request->boolean('has_whatsapp')) {
                $query->whereNotNull('whatsapp_link');
            } else {
                $query->whereNull('whatsapp_link');
            }
        }

        $groups = $query->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 15));

        // Add member count to each group
        $groups->getCollection()->transform(function ($group) {
            $group->member_count = $group->members->count();
            $group->whatsapp_link_expired = $group->whatsapp_link_expiry &&
                Carbon::parse($group->whatsapp_link_expiry)->isPast();
            return $group;
        });

        return response()->json([
            'success' => true,
            'data' => $groups
        ]);
    }

    /**
     * Create a new group
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'level_id' => 'required|exists:course_levels,id',
            'teacher_id' => 'required|exists:users,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after:start_date',
            'whatsapp_link' => 'nullable|url',
            'whatsapp_link_expiry' => 'nullable|date|after:now',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Validate teacher role
            $teacher = User::where('id', $request->teacher_id)
                ->where('role', 'teacher')
                ->first();

            if (!$teacher) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid teacher ID or user is not a teacher'
                ], 422);
            }

            // Validate WhatsApp link format if provided
            if ($request->whatsapp_link && !$this->isValidWhatsAppLink($request->whatsapp_link)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid WhatsApp group link format'
                ], 422);
            }

            // Create group
            $group = Group::create([
                'level_id' => $request->level_id,
                'teacher_id' => $request->teacher_id,
                'name' => $request->name,
                'description' => $request->description,
                'is_active' => true,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'whatsapp_link' => $request->whatsapp_link,
                'whatsapp_link_expiry' => $request->whatsapp_link_expiry,
            ]);

            // Add teacher as group member
            GroupMember::create([
                'group_id' => $group->id,
                'user_id' => $request->teacher_id,
            ]);

            // Log WhatsApp link if provided
            if ($request->whatsapp_link) {
                $this->logWhatsAppLinkChange($group->id, $request->teacher_id, $request->whatsapp_link);
            }

            return response()->json([
                'success' => true,
                'message' => 'Group created successfully',
                'data' => $group->load(['level.course', 'teacher.profile', 'members.user.profile'])
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Group creation failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display specific group
     */
    public function show(Group $group)
    {
        $group->load([
            'level.course',
            'teacher.profile',
            'members.user.profile'
        ]);

        // Add additional info
        $group->member_count = $group->members->count();
        $group->whatsapp_link_expired = $group->whatsapp_link_expiry &&
            Carbon::parse($group->whatsapp_link_expiry)->isPast();

        // Get recent WhatsApp link history
        $group->whatsapp_history = WhatsappLinkHistory::where('group_id', $group->id)
            ->with('user:id,first_name,second_name')
            ->orderBy('updated_at', 'desc')
            ->limit(5)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $group
        ]);
    }

    /**
     * Update group
     */
    public function update(Request $request, Group $group)
    {
        $validator = Validator::make($request->all(), [
            'level_id' => 'sometimes|exists:course_levels,id',
            'teacher_id' => 'sometimes|exists:users,id',
            'name' => 'sometimes|string|max:255',
            'description' => 'sometimes|string',
            'is_active' => 'sometimes|boolean',
            'start_date' => 'sometimes|date',
            'end_date' => 'sometimes|date|after:start_date',
            'whatsapp_link' => 'nullable|url',
            'whatsapp_link_expiry' => 'nullable|date|after:now',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Validate teacher if being changed
            if ($request->has('teacher_id')) {
                $teacher = User::where('id', $request->teacher_id)
                    ->where('role', 'teacher')
                    ->first();

                if (!$teacher) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid teacher ID or user is not a teacher'
                    ], 422);
                }
            }

            // Validate WhatsApp link if provided
            if ($request->has('whatsapp_link') && $request->whatsapp_link &&
                !$this->isValidWhatsAppLink($request->whatsapp_link)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid WhatsApp group link format'
                ], 422);
            }

            // Check if WhatsApp link is being updated
            $oldWhatsAppLink = $group->whatsapp_link;
            $newWhatsAppLink = $request->whatsapp_link;

            $group->update($request->only([
                'level_id', 'teacher_id', 'name', 'description', 'is_active',
                'start_date', 'end_date', 'whatsapp_link', 'whatsapp_link_expiry'
            ]));

            // Log WhatsApp link change if updated
            if ($request->has('whatsapp_link') && $oldWhatsAppLink !== $newWhatsAppLink) {
                $this->logWhatsAppLinkChange($group->id, Auth::id(), $newWhatsAppLink);

                // Notify group members about link change
                if ($newWhatsAppLink) {
                    $this->notifyMembersAboutLinkChange($group);
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Group updated successfully',
                'data' => $group->load(['level.course', 'teacher.profile', 'members.user.profile'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Group update failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete group
     */
    public function destroy(Group $group)
    {
        try {
            // Check if group has active members
            $memberCount = $group->members()->count();

            if ($memberCount > 1) { // More than just the teacher
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete group with active members. Remove members first.'
                ], 409);
            }

            $group->delete();

            return response()->json([
                'success' => true,
                'message' => 'Group deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Group deletion failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update WhatsApp group link
     */
    public function updateWhatsAppLink(Request $request, Group $group)
    {
        $validator = Validator::make($request->all(), [
            'whatsapp_link' => 'required|url',
            'whatsapp_link_expiry' => 'nullable|date|after:now',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Check if user can manage WhatsApp links
            $user = Auth::user();
            if (!$user->can_manage_whatsapp_links && $user->id !== $group->teacher_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to manage WhatsApp links'
                ], 403);
            }

            // Validate WhatsApp link format
            if (!$this->isValidWhatsAppLink($request->whatsapp_link)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid WhatsApp group link format'
                ], 422);
            }

            // Update group
            $group->update([
                'whatsapp_link' => $request->whatsapp_link,
                'whatsapp_link_expiry' => $request->whatsapp_link_expiry,
            ]);

            // Log the change
            $this->logWhatsAppLinkChange($group->id, $user->id, $request->whatsapp_link);

            // Notify group members
            $this->notifyMembersAboutLinkChange($group);

            return response()->json([
                'success' => true,
                'message' => 'WhatsApp link updated successfully',
                'data' => $group->fresh()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'WhatsApp link update failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get teacher's groups
     */
    public function getTeacherGroups(User $teacher, Request $request)
    {
        if ($teacher->role !== 'teacher') {
            return response()->json([
                'success' => false,
                'message' => 'User is not a teacher'
            ], 422);
        }

        $query = Group::with(['level.course', 'members.user.profile'])
            ->where('teacher_id', $teacher->id);

        // Filter by active status
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        $groups = $query->orderBy('created_at', 'desc')->get();

        // Add member count to each group
        $groups->transform(function ($group) {
            $group->member_count = $group->members->count();
            $group->whatsapp_link_expired = $group->whatsapp_link_expiry &&
                Carbon::parse($group->whatsapp_link_expiry)->isPast();
            return $group;
        });

        return response()->json([
            'success' => true,
            'data' => [
                'teacher' => $teacher->load('profile'),
                'groups' => $groups,
                'total_groups' => $groups->count(),
                'active_groups' => $groups->where('is_active', true)->count()
            ]
        ]);
    }

    /**
     * Get authenticated user's groups
     */
    public function getMyGroups(Request $request)
    {
        $user = Auth::user();

        $query = GroupMember::with(['group.level.course', 'group.teacher.profile'])
            ->where('user_id', $user->id);

        // Filter by active groups
        if ($request->has('is_active')) {
            $query->whereHas('group', function($q) use ($request) {
                $q->where('is_active', $request->boolean('is_active'));
            });
        }

        $memberships = $query->orderBy('joined_at', 'desc')->get();

        $groups = $memberships->map(function ($membership) {
            $group = $membership->group;
            $group->joined_at = $membership->joined_at;
            $group->member_count = $group->members()->count();
            $group->whatsapp_link_expired = $group->whatsapp_link_expiry &&
                Carbon::parse($group->whatsapp_link_expiry)->isPast();
            return $group;
        });

        return response()->json([
            'success' => true,
            'data' => [
                'groups' => $groups,
                'total_groups' => $groups->count()
            ]
        ]);
    }

    /**
     * Get group statistics
     */
    public function getGroupStats(Request $request)
    {
        $stats = [
            'total_groups' => Group::count(),
            'active_groups' => Group::where('is_active', true)->count(),
            'groups_with_whatsapp' => Group::whereNotNull('whatsapp_link')->count(),
            'expired_whatsapp_links' => Group::whereNotNull('whatsapp_link_expiry')
                ->where('whatsapp_link_expiry', '<', now())
                ->count(),
            'total_members' => GroupMember::count(),
            'groups_by_level' => Group::with('level.course')
                ->selectRaw('level_id, COUNT(*) as count')
                ->groupBy('level_id')
                ->get()
                ->map(function($group) {
                    return [
                        'level' => $group->level->title ?? 'Unknown',
                        'course' => $group->level->course->name ?? 'Unknown',
                        'count' => $group->count
                    ];
                }),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Validate WhatsApp group link format
     */
    private function isValidWhatsAppLink($link)
    {
        $patterns = [
            '/^https:\/\/chat\.whatsapp\.com\/[a-zA-Z0-9]+$/',
            '/^https:\/\/wa\.me\/[a-zA-Z0-9]+$/',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $link)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Log WhatsApp link changes
     */
    private function logWhatsAppLinkChange($groupId, $userId, $whatsappLink)
    {
        try {
            WhatsappLinkHistory::create([
                'group_id' => $groupId,
                'user_id' => $userId,
                'whatsapp_link' => $whatsappLink,
                'updated_at' => now(),
            ]);
        } catch (\Exception $e) {
            // Log error but don't fail the main operation
            Log::error('WhatsApp link history logging failed: ' . $e->getMessage());
        }
    }

    /**
     * Notify group members about WhatsApp link changes
     */
    private function notifyMembersAboutLinkChange($group)
    {
        try {
            $members = $group->members()->with('user')->get();

            foreach ($members as $member) {
                if ($member->user_id !== $group->teacher_id) { // Don't notify the teacher who made the change
                    Notification::create([
                        'user_id' => $member->user_id,
                        'title' => 'WhatsApp Group Link Updated',
                        'message' => "The WhatsApp group link for '{$group->name}' has been updated. Check the group page for the new link.",
                        'type' => 'whatsapp_invite',
                    ]);
                }
            }
        } catch (\Exception $e) {
            // Log error but don't fail the main operation
            Log::error('WhatsApp link notification failed: ' . $e->getMessage());
        }
    }
}
