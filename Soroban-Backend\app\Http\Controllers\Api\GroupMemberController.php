<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\GroupMember;
use App\Models\Group;
use App\Models\User;
use App\Models\Subscription;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class GroupMemberController extends Controller
{
    /**
     * Display group members
     */
    public function index(Request $request)
    {
        $query = GroupMember::with(['group.level.course', 'user.profile']);

        // Filter by group
        if ($request->has('group_id')) {
            $query->where('group_id', $request->group_id);
        }

        // Filter by user
        if ($request->has('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // Search by user name
        if ($request->has('search')) {
            $search = $request->search;
            $query->whereHas('user', function($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('second_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $members = $query->orderBy('joined_at', 'desc')
            ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $members
        ]);
    }

    /**
     * Add member to group
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'group_id' => 'required|exists:groups,id',
            'user_id' => 'required|exists:users,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $group = Group::find($request->group_id);
            $user = User::find($request->user_id);

            // Check if group is active
            if (!$group->is_active) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot add members to inactive group'
                ], 422);
            }

            // Check if user is already a member
            $existingMember = GroupMember::where('group_id', $request->group_id)
                ->where('user_id', $request->user_id)
                ->first();

            if ($existingMember) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is already a member of this group'
                ], 409);
            }

            // Validate user eligibility for the group
            $eligibility = $this->checkMemberEligibility($user, $group);
            if (!$eligibility['eligible']) {
                return response()->json([
                    'success' => false,
                    'message' => $eligibility['reason']
                ], 422);
            }

            // Add member to group
            $member = GroupMember::create([
                'group_id' => $request->group_id,
                'user_id' => $request->user_id,
                'joined_at' => now(),
            ]);

            // Send notification to the new member
            $this->sendMembershipNotification($member, 'added');

            return response()->json([
                'success' => true,
                'message' => 'Member added to group successfully',
                'data' => $member->load(['group.level.course', 'user.profile'])
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to add member to group',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display specific group member
     */
    public function show(GroupMember $groupMember)
    {
        return response()->json([
            'success' => true,
            'data' => $groupMember->load([
                'group.level.course',
                'group.teacher.profile',
                'user.profile'
            ])
        ]);
    }

    /**
     * Update group member (limited updates)
     */
    public function update(Request $request, GroupMember $groupMember)
    {
        // Currently, only joined_at can be updated
        $validator = Validator::make($request->all(), [
            'joined_at' => 'sometimes|date',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $groupMember->update($request->only(['joined_at']));

            return response()->json([
                'success' => true,
                'message' => 'Group member updated successfully',
                'data' => $groupMember->load(['group.level.course', 'user.profile'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Group member update failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove member from group
     */
    public function destroy(GroupMember $groupMember)
    {
        try {
            // Check if trying to remove the teacher
            if ($groupMember->user_id === $groupMember->group->teacher_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot remove the teacher from the group'
                ], 422);
            }

            // Send notification before removing
            $this->sendMembershipNotification($groupMember, 'removed');

            $groupMember->delete();

            return response()->json([
                'success' => true,
                'message' => 'Member removed from group successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to remove member from group',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Add member to group (alternative endpoint)
     */
    public function addMember(Request $request, Group $group)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = User::find($request->user_id);

            // Check if group is active
            if (!$group->is_active) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot add members to inactive group'
                ], 422);
            }

            // Check if user is already a member
            $existingMember = GroupMember::where('group_id', $group->id)
                ->where('user_id', $request->user_id)
                ->first();

            if ($existingMember) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is already a member of this group'
                ], 409);
            }

            // Validate user eligibility
            $eligibility = $this->checkMemberEligibility($user, $group);
            if (!$eligibility['eligible']) {
                return response()->json([
                    'success' => false,
                    'message' => $eligibility['reason']
                ], 422);
            }

            // Add member
            $member = GroupMember::create([
                'group_id' => $group->id,
                'user_id' => $request->user_id,
                'joined_at' => now(),
            ]);

            // Send notification
            $this->sendMembershipNotification($member, 'added');

            return response()->json([
                'success' => true,
                'message' => 'Member added to group successfully',
                'data' => $member->load(['group.level.course', 'user.profile'])
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to add member to group',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove member from group (alternative endpoint)
     */
    public function removeMember(Group $group, User $user)
    {
        try {
            // Check if trying to remove the teacher
            if ($user->id === $group->teacher_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot remove the teacher from the group'
                ], 422);
            }

            // Find the membership
            $member = GroupMember::where('group_id', $group->id)
                ->where('user_id', $user->id)
                ->first();

            if (!$member) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not a member of this group'
                ], 404);
            }

            // Send notification before removing
            $this->sendMembershipNotification($member, 'removed');

            $member->delete();

            return response()->json([
                'success' => true,
                'message' => 'Member removed from group successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to remove member from group',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk add members to group
     */
    public function bulkAddMembers(Request $request, Group $group)
    {
        $validator = Validator::make($request->all(), [
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Check if group is active
            if (!$group->is_active) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot add members to inactive group'
                ], 422);
            }

            $addedMembers = [];
            $skippedUsers = [];
            $errors = [];

            foreach ($request->user_ids as $userId) {
                try {
                    $user = User::find($userId);

                    // Check if already a member
                    $existingMember = GroupMember::where('group_id', $group->id)
                        ->where('user_id', $userId)
                        ->first();

                    if ($existingMember) {
                        $skippedUsers[] = [
                            'user_id' => $userId,
                            'reason' => 'Already a member'
                        ];
                        continue;
                    }

                    // Check eligibility
                    $eligibility = $this->checkMemberEligibility($user, $group);
                    if (!$eligibility['eligible']) {
                        $skippedUsers[] = [
                            'user_id' => $userId,
                            'reason' => $eligibility['reason']
                        ];
                        continue;
                    }

                    // Add member
                    $member = GroupMember::create([
                        'group_id' => $group->id,
                        'user_id' => $userId,
                        'joined_at' => now(),
                    ]);

                    $addedMembers[] = $member;

                    // Send notification
                    $this->sendMembershipNotification($member, 'added');

                } catch (\Exception $e) {
                    $errors[] = [
                        'user_id' => $userId,
                        'error' => $e->getMessage()
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Bulk member addition completed',
                'data' => [
                    'added_members' => count($addedMembers),
                    'skipped_users' => count($skippedUsers),
                    'errors' => count($errors),
                    'details' => [
                        'added' => $addedMembers,
                        'skipped' => $skippedUsers,
                        'errors' => $errors
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Bulk member addition failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get group members with filtering
     */
    public function getGroupMembers(Group $group, Request $request)
    {
        $query = GroupMember::with(['user.profile'])
            ->where('group_id', $group->id);

        // Search by user name
        if ($request->has('search')) {
            $search = $request->search;
            $query->whereHas('user', function($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('second_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Filter by user role
        if ($request->has('role')) {
            $query->whereHas('user', function($q) use ($request) {
                $q->where('role', $request->role);
            });
        }

        $members = $query->orderBy('joined_at', 'desc')->get();

        return response()->json([
            'success' => true,
            'data' => [
                'group' => $group->load(['level.course', 'teacher.profile']),
                'members' => $members,
                'member_count' => $members->count(),
                'students_count' => $members->filter(function($member) {
                    return $member->user->role === 'student';
                })->count()
            ]
        ]);
    }

    /**
     * Check if user is eligible to join the group
     */
    private function checkMemberEligibility($user, $group)
    {
        // Teachers and admins can always join
        if (in_array($user->role, ['superAdmin', 'admin', 'teacher'])) {
            return ['eligible' => true, 'reason' => 'Admin/Teacher access'];
        }

        // Students must have active subscription for the group's level
        if ($user->role === 'student') {
            $hasSubscription = Subscription::where('student_id', $user->id)
                ->where('level_id', $group->level_id)
                ->where('renewal_status', 'active')
                ->where('start_date', '<=', now())
                ->where('end_date', '>=', now())
                ->exists();

            if (!$hasSubscription) {
                return [
                    'eligible' => false,
                    'reason' => 'Student does not have an active subscription for this level'
                ];
            }

            return ['eligible' => true, 'reason' => 'Active subscription verified'];
        }

        // Guests cannot join groups
        if ($user->role === 'guest') {
            return [
                'eligible' => false,
                'reason' => 'Guest users cannot join groups'
            ];
        }

        return ['eligible' => true, 'reason' => 'User eligible'];
    }

    /**
     * Send membership notification
     */
    private function sendMembershipNotification($member, $action)
    {
        try {
            $group = $member->group;
            $user = $member->user;

            $title = $action === 'added' ? 'Added to Group' : 'Removed from Group';
            $message = $action === 'added'
                ? "You have been added to the group '{$group->name}' for {$group->level->course->name} - {$group->level->title}."
                : "You have been removed from the group '{$group->name}'.";

            Notification::create([
                'user_id' => $user->id,
                'title' => $title,
                'message' => $message,
                'type' => 'whatsapp_invite',
            ]);

        } catch (\Exception $e) {
            // Log error but don't fail the main operation
            Log::error('Group membership notification failed: ' . $e->getMessage());
        }
    }
}
