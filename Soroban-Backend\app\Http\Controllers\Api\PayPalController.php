<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\PayPalService;
use App\Models\Payment;
use App\Models\Subscription;
use App\Models\CourseLevel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class PayPalController extends Controller
{
    protected $paypalService;

    public function __construct(PayPalService $paypalService)
    {
        $this->paypalService = $paypalService;
    }

    /**
     * Create PayPal payment order
     */
    public function createOrder(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'course_level_id' => 'required|exists:course_levels,id',
            'subscription_id' => 'required|exists:subscriptions,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = Auth::user();
            $courseLevel = CourseLevel::with('course')->findOrFail($request->course_level_id);
            $subscription = Subscription::findOrFail($request->subscription_id);

            // Create unique order ID
            $orderId = 'SOROBAN_' . time() . '_' . $user->id . '_' . $courseLevel->id;

            $orderData = [
                'order_id' => $orderId,
                'amount' => $courseLevel->price,
                'currency' => config('paypal.currency', 'USD'),
                'description' => "Subscription to {$courseLevel->course->name} - {$courseLevel->title}",
                'custom_id' => json_encode([
                    'user_id' => $user->id,
                    'course_level_id' => $courseLevel->id,
                    'subscription_id' => $subscription->id,
                ]),
                'return_url' => url('/api/paypal/success'),
                'cancel_url' => url('/api/paypal/cancel'),
            ];

            $result = $this->paypalService->createOrder($orderData);

            if ($result['success']) {
                // Create pending payment record
                $payment = Payment::create([
                    'student_id' => $user->id,
                    'level_id' => $courseLevel->id,
                    'subscription_id' => $subscription->id,
                    'amount' => $courseLevel->price,
                    'transaction_code' => $result['order_id'],
                    'is_confirmed' => false,
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'PayPal order created successfully',
                    'data' => [
                        'order_id' => $result['order_id'],
                        'approval_url' => $result['approval_url'],
                        'payment_id' => $payment->id,
                    ]
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Failed to create PayPal order',
                'error' => $result['error']
            ], 400);

        } catch (\Exception $e) {
            Log::error('PayPal Create Order Exception', [
                'user_id' => Auth::id(),
                'course_level_id' => $request->course_level_id,
                'message' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Payment processing failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Capture PayPal payment
     */
    public function captureOrder(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'order_id' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $result = $this->paypalService->captureOrder($request->order_id);

            if ($result['success'] && $result['status'] === 'COMPLETED') {
                // Find and update payment record
                $payment = Payment::where('transaction_code', $request->order_id)->first();

                if ($payment) {
                    DB::transaction(function () use ($payment, $result) {
                        // Update payment as confirmed
                        $payment->update([
                            'is_confirmed' => true,
                            'transaction_pic' => json_encode($result['data']), // Store PayPal response
                        ]);

                        // Update subscription status
                        if ($payment->subscription) {
                            $payment->subscription->update([
                                'renewal_status' => 'active',
                                'start_date' => now(),
                                'end_date' => now()->addMonth(), // Default 1 month subscription
                            ]);
                        }
                    });

                    return response()->json([
                        'success' => true,
                        'message' => 'Payment captured successfully',
                        'data' => [
                            'payment_id' => $payment->id,
                            'capture_id' => $result['capture_id'],
                            'status' => 'completed',
                        ]
                    ]);
                }

                return response()->json([
                    'success' => false,
                    'message' => 'Payment record not found',
                ], 404);
            }

            return response()->json([
                'success' => false,
                'message' => 'Payment capture failed',
                'error' => $result['error']
            ], 400);

        } catch (\Exception $e) {
            Log::error('PayPal Capture Order Exception', [
                'order_id' => $request->order_id,
                'message' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Payment capture failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle PayPal success redirect
     */
    public function handleSuccess(Request $request)
    {
        $orderId = $request->query('token');
        $payerId = $request->query('PayerID');

        if (!$orderId) {
            return redirect(config('app.frontend_url') . '/payment/error?message=Missing order ID');
        }

        // Auto-capture the payment
        $captureResult = $this->paypalService->captureOrder($orderId);

        if ($captureResult['success'] && $captureResult['status'] === 'COMPLETED') {
            // Update payment record
            $payment = Payment::where('transaction_code', $orderId)->first();
            if ($payment) {
                $payment->update(['is_confirmed' => true]);
                
                // Update subscription
                if ($payment->subscription) {
                    $payment->subscription->update([
                        'renewal_status' => 'active',
                        'start_date' => now(),
                        'end_date' => now()->addMonth(),
                    ]);
                }
            }

            return redirect(config('app.frontend_url') . '/payment/success?order_id=' . $orderId);
        }

        return redirect(config('app.frontend_url') . '/payment/error?message=Payment capture failed');
    }

    /**
     * Handle PayPal cancel redirect
     */
    public function handleCancel(Request $request)
    {
        $orderId = $request->query('token');
        
        // Mark payment as cancelled if exists
        if ($orderId) {
            $payment = Payment::where('transaction_code', $orderId)->first();
            if ($payment && !$payment->is_confirmed) {
                $payment->delete(); // Remove pending payment
            }
        }

        return redirect(config('app.frontend_url') . '/payment/cancelled');
    }

    /**
     * Handle PayPal webhooks
     */
    public function handleWebhook(Request $request)
    {
        try {
            $headers = $request->headers->all();
            $body = $request->getContent();

            // Verify webhook signature
            $verification = $this->paypalService->verifyWebhook($headers, $body);

            if (!$verification['success'] || !$verification['verified']) {
                Log::warning('PayPal Webhook Verification Failed', [
                    'headers' => $headers,
                    'verification' => $verification
                ]);
                return response()->json(['error' => 'Webhook verification failed'], 400);
            }

            $event = json_decode($body, true);
            $eventType = $event['event_type'];

            Log::info('PayPal Webhook Received', [
                'event_type' => $eventType,
                'resource_id' => $event['resource']['id'] ?? null
            ]);

            // Handle different webhook events
            switch ($eventType) {
                case 'CHECKOUT.ORDER.COMPLETED':
                case 'PAYMENT.CAPTURE.COMPLETED':
                    $this->handlePaymentCompleted($event);
                    break;

                case 'PAYMENT.CAPTURE.DENIED':
                    $this->handlePaymentDenied($event);
                    break;

                case 'PAYMENT.CAPTURE.REFUNDED':
                    $this->handlePaymentRefunded($event);
                    break;
            }

            return response()->json(['status' => 'success']);

        } catch (\Exception $e) {
            Log::error('PayPal Webhook Exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json(['error' => 'Webhook processing failed'], 500);
        }
    }

    /**
     * Get PayPal JavaScript SDK configuration
     */
    public function getSDKConfig()
    {
        return response()->json([
            'success' => true,
            'data' => [
                'client_id' => config('paypal.client_id'),
                'currency' => config('paypal.currency'),
                'environment' => config('paypal.environment'),
                'sdk_url' => $this->paypalService->getJavaScriptSDKUrl(),
            ]
        ]);
    }

    /**
     * Handle payment completed webhook
     */
    private function handlePaymentCompleted($event)
    {
        $resource = $event['resource'];
        $customId = $resource['custom_id'] ?? null;

        if ($customId) {
            $customData = json_decode($customId, true);
            $payment = Payment::where('student_id', $customData['user_id'])
                ->where('level_id', $customData['course_level_id'])
                ->where('subscription_id', $customData['subscription_id'])
                ->where('is_confirmed', false)
                ->first();

            if ($payment) {
                $payment->update(['is_confirmed' => true]);
                
                if ($payment->subscription) {
                    $payment->subscription->update(['renewal_status' => 'active']);
                }
            }
        }
    }

    /**
     * Handle payment denied webhook
     */
    private function handlePaymentDenied($event)
    {
        // Handle payment denial logic
        Log::warning('PayPal Payment Denied', ['event' => $event]);
    }

    /**
     * Handle payment refunded webhook
     */
    private function handlePaymentRefunded($event)
    {
        // Handle refund logic
        Log::info('PayPal Payment Refunded', ['event' => $event]);
    }
}
