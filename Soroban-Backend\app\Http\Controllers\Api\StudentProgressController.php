<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\StudentProgress;
use App\Models\User;
use App\Models\Lesson;
use App\Models\CourseLevel;
use App\Models\Subscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class StudentProgressController extends Controller
{
    /**
     * Display student progress records
     */
    public function index(Request $request)
    {
        $query = StudentProgress::with(['student.profile', 'lesson.courseLevel.course', 'level.course']);

        // Filter by student
        if ($request->has('student_id')) {
            $query->where('student_id', $request->student_id);
        }

        // Filter by lesson
        if ($request->has('lesson_id')) {
            $query->where('lesson_id', $request->lesson_id);
        }

        // Filter by level
        if ($request->has('level_id')) {
            $query->where('level_id', $request->level_id);
        }

        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Filter by completion percentage range
        if ($request->has('min_completion')) {
            $query->where('completion_percentage', '>=', $request->min_completion);
        }
        if ($request->has('max_completion')) {
            $query->where('completion_percentage', '<=', $request->max_completion);
        }

        $progress = $query->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $progress
        ]);
    }

    /**
     * Create or update student progress
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'student_id' => 'required|exists:users,id',
            'lesson_id' => 'nullable|exists:lessons,id',
            'level_id' => 'nullable|exists:course_levels,id',
            'status' => 'required|in:not_started,in_progress,completed',
            'completion_percentage' => 'required|numeric|min:0|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Validate that student exists and has role 'student'
            $student = User::where('id', $request->student_id)
                ->where('role', 'student')
                ->first();

            if (!$student) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid student ID or user is not a student'
                ], 422);
            }

            // Validate lesson and level relationship
            if ($request->lesson_id && $request->level_id) {
                $lesson = Lesson::where('id', $request->lesson_id)
                    ->where('course_level_id', $request->level_id)
                    ->first();

                if (!$lesson) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Lesson does not belong to the specified level'
                    ], 422);
                }
            }

            // Check if student has active subscription for this level
            if ($request->level_id) {
                $hasSubscription = Subscription::where('student_id', $request->student_id)
                    ->where('level_id', $request->level_id)
                    ->where('renewal_status', 'active')
                    ->where('start_date', '<=', now())
                    ->where('end_date', '>=', now())
                    ->exists();

                if (!$hasSubscription) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Student does not have an active subscription for this level'
                    ], 403);
                }
            }

            // Create or update progress record
            $progressData = [
                'student_id' => $request->student_id,
                'lesson_id' => $request->lesson_id,
                'level_id' => $request->level_id,
                'status' => $request->status,
                'completion_percentage' => $request->completion_percentage,
                'completed_at' => $request->status === 'completed' ? now() : null,
            ];

            // Check if progress record already exists
            $existingProgress = StudentProgress::where('student_id', $request->student_id)
                ->where(function($query) use ($request) {
                    if ($request->lesson_id) {
                        $query->where('lesson_id', $request->lesson_id);
                    }
                    if ($request->level_id && !$request->lesson_id) {
                        $query->where('level_id', $request->level_id)
                              ->whereNull('lesson_id');
                    }
                })
                ->first();

            if ($existingProgress) {
                $existingProgress->update($progressData);
                $progress = $existingProgress;
                $message = 'Student progress updated successfully';
            } else {
                $progress = StudentProgress::create($progressData);
                $message = 'Student progress created successfully';
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'data' => $progress->load(['student.profile', 'lesson.courseLevel.course', 'level.course'])
            ], $existingProgress ? 200 : 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Progress tracking failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display specific progress record
     */
    public function show(StudentProgress $studentProgress)
    {
        return response()->json([
            'success' => true,
            'data' => $studentProgress->load([
                'student.profile',
                'lesson.courseLevel.course',
                'level.course'
            ])
        ]);
    }

    /**
     * Update student progress
     */
    public function update(Request $request, StudentProgress $studentProgress)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'sometimes|in:not_started,in_progress,completed',
            'completion_percentage' => 'sometimes|numeric|min:0|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $updateData = $request->only(['status', 'completion_percentage']);

            // Set completed_at timestamp if status is completed
            if ($request->status === 'completed') {
                $updateData['completed_at'] = now();
            } elseif ($request->status && $request->status !== 'completed') {
                $updateData['completed_at'] = null;
            }

            $studentProgress->update($updateData);

            return response()->json([
                'success' => true,
                'message' => 'Student progress updated successfully',
                'data' => $studentProgress->load([
                    'student.profile',
                    'lesson.courseLevel.course',
                    'level.course'
                ])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Progress update failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete progress record
     */
    public function destroy(StudentProgress $studentProgress)
    {
        try {
            $studentProgress->delete();

            return response()->json([
                'success' => true,
                'message' => 'Student progress deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Progress deletion failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update lesson progress for authenticated student
     */
    public function updateLessonProgress(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'lesson_id' => 'required|exists:lessons,id',
            'completion_percentage' => 'required|numeric|min:0|max:100',
            'status' => 'sometimes|in:not_started,in_progress,completed',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = Auth::user();

            if ($user->role !== 'student') {
                return response()->json([
                    'success' => false,
                    'message' => 'Only students can update lesson progress'
                ], 403);
            }

            $lesson = Lesson::with('courseLevel')->find($request->lesson_id);

            // Check if student has subscription for this lesson's level
            $hasSubscription = Subscription::where('student_id', $user->id)
                ->where('level_id', $lesson->course_level_id)
                ->where('renewal_status', 'active')
                ->where('start_date', '<=', now())
                ->where('end_date', '>=', now())
                ->exists();

            if (!$hasSubscription) {
                return response()->json([
                    'success' => false,
                    'message' => 'You need an active subscription to access this lesson'
                ], 403);
            }

            // Determine status based on completion percentage
            $status = $request->status;
            if (!$status) {
                if ($request->completion_percentage == 0) {
                    $status = 'not_started';
                } elseif ($request->completion_percentage == 100) {
                    $status = 'completed';
                } else {
                    $status = 'in_progress';
                }
            }

            // Update or create progress
            $progress = StudentProgress::updateOrCreate(
                [
                    'student_id' => $user->id,
                    'lesson_id' => $request->lesson_id,
                ],
                [
                    'level_id' => $lesson->course_level_id,
                    'status' => $status,
                    'completion_percentage' => $request->completion_percentage,
                    'completed_at' => $status === 'completed' ? now() : null,
                ]
            );

            return response()->json([
                'success' => true,
                'message' => 'Lesson progress updated successfully',
                'data' => $progress->load(['lesson.courseLevel.course'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Progress update failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get authenticated student's progress
     */
    public function getMyProgress(Request $request)
    {
        $user = Auth::user();

        if ($user->role !== 'student') {
            return response()->json([
                'success' => false,
                'message' => 'Only students can view their progress'
            ], 403);
        }

        $query = StudentProgress::with(['lesson.courseLevel.course', 'level.course'])
            ->where('student_id', $user->id);

        // Filter by level if provided
        if ($request->has('level_id')) {
            $query->where('level_id', $request->level_id);
        }

        // Filter by status if provided
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        $progress = $query->orderBy('created_at', 'desc')->get();

        // Calculate overall statistics
        $stats = [
            'total_lessons' => $progress->whereNotNull('lesson_id')->count(),
            'completed_lessons' => $progress->where('status', 'completed')->whereNotNull('lesson_id')->count(),
            'in_progress_lessons' => $progress->where('status', 'in_progress')->whereNotNull('lesson_id')->count(),
            'average_completion' => $progress->whereNotNull('lesson_id')->avg('completion_percentage') ?? 0,
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'progress' => $progress,
                'statistics' => $stats
            ]
        ]);
    }

    /**
     * Get student progress by student ID (for teachers/admins)
     */
    public function getStudentProgress(User $student, Request $request)
    {
        if ($student->role !== 'student') {
            return response()->json([
                'success' => false,
                'message' => 'User is not a student'
            ], 422);
        }

        $query = StudentProgress::with(['lesson.courseLevel.course', 'level.course'])
            ->where('student_id', $student->id);

        // Filter by level if provided
        if ($request->has('level_id')) {
            $query->where('level_id', $request->level_id);
        }

        $progress = $query->orderBy('created_at', 'desc')->get();

        // Calculate statistics
        $stats = [
            'total_lessons' => $progress->whereNotNull('lesson_id')->count(),
            'completed_lessons' => $progress->where('status', 'completed')->whereNotNull('lesson_id')->count(),
            'in_progress_lessons' => $progress->where('status', 'in_progress')->whereNotNull('lesson_id')->count(),
            'average_completion' => $progress->whereNotNull('lesson_id')->avg('completion_percentage') ?? 0,
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'student' => $student->load('profile'),
                'progress' => $progress,
                'statistics' => $stats
            ]
        ]);
    }

    /**
     * Get level completion statistics
     */
    public function getLevelStats(CourseLevel $level, Request $request)
    {
        $stats = StudentProgress::where('level_id', $level->id)
            ->selectRaw('
                COUNT(*) as total_students,
                COUNT(CASE WHEN status = "completed" THEN 1 END) as completed_students,
                COUNT(CASE WHEN status = "in_progress" THEN 1 END) as in_progress_students,
                COUNT(CASE WHEN status = "not_started" THEN 1 END) as not_started_students,
                AVG(completion_percentage) as average_completion
            ')
            ->first();

        return response()->json([
            'success' => true,
            'data' => [
                'level' => $level->load('course'),
                'statistics' => $stats
            ]
        ]);
    }
}
