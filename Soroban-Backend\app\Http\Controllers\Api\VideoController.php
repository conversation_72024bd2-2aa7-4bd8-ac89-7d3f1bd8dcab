<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Lesson;
use App\Models\Subscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class VideoController extends Controller
{
    /**
     * Get all video lessons with optional filters
     */
    public function getVideoLessons(Request $request)
    {
        $query = Lesson::with(['courseLevel.course', 'videoAttachment', 'pdfAttachment', 'quiz'])
            ->whereNotNull('youtube_playlist_id');

        // Apply filters
        if ($request->has('course_id')) {
            $query->whereHas('courseLevel', function($q) use ($request) {
                $q->where('course_id', $request->course_id);
            });
        }

        if ($request->has('course_level_id')) {
            $query->where('course_level_id', $request->course_level_id);
        }

        if ($request->has('requires_payment')) {
            $query->where('requires_payment', $request->boolean('requires_payment'));
        }

        $lessons = $query->orderBy('course_level_id')
            ->orderBy('sequence')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $lessons
        ]);
    }

    /**
     * Get single video lesson
     */
    public function getVideoLesson(Lesson $lesson)
    {
        $lesson->load(['courseLevel.course', 'videoAttachment', 'pdfAttachment', 'quiz']);

        return response()->json([
            'success' => true,
            'data' => $lesson
        ]);
    }

    /**
     * Check if user has access to a video lesson
     */
    public function checkVideoAccess(Lesson $lesson)
    {
        $user = Auth::user();

        $hasAccess = $this->checkLessonAccess($user, $lesson);

        return response()->json([
            'success' => true,
            'data' => [
                'has_access' => $hasAccess,
                'requires_subscription' => $lesson->requires_payment && !$hasAccess,
                'message' => $hasAccess ? null : 'You need an active subscription to access this content'
            ]
        ]);
    }

    /**
     * Get YouTube playlist embed URL for a lesson
     */
    public function getPlaylistEmbed(Lesson $lesson, Request $request)
    {
        $user = Auth::user();
        
        // Check if user has access to this lesson
        if (!$this->checkLessonAccess($user, $lesson)) {
            return response()->json([
                'success' => false,
                'message' => 'You need an active subscription to access this content',
                'requires_subscription' => true
            ], 403);
        }

        if (!$lesson->youtube_playlist_id) {
            return response()->json([
                'success' => false,
                'message' => 'No YouTube playlist available for this lesson'
            ], 404);
        }

        // Generate embed URL
        $embedUrl = $this->generatePlaylistEmbedUrl($lesson->youtube_playlist_id, $request);

        return response()->json([
            'success' => true,
            'data' => [
                'lesson_id' => $lesson->id,
                'lesson_title' => $lesson->title,
                'playlist_id' => $lesson->youtube_playlist_id,
                'embed_url' => $embedUrl,
                'iframe_html' => $this->generateIframeHtml($embedUrl),
                'has_access' => true
            ]
        ]);
    }

    /**
     * Get all accessible video lessons for a user
     */
    public function getUserVideoLessons(Request $request)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication required'
            ], 401);
        }

        // Get user's active subscriptions
        $activeSubscriptions = Subscription::where('student_id', $user->id)
            ->where('renewal_status', 'active')
            ->where('start_date', '<=', now())
            ->where('end_date', '>=', now())
            ->pluck('level_id');

        // Get lessons with YouTube playlists
        $lessons = Lesson::with(['courseLevel.course'])
            ->whereNotNull('youtube_playlist_id')
            ->where(function($query) use ($activeSubscriptions, $user) {
                // Free lessons or lessons user has subscription for
                $query->where('requires_payment', false)
                      ->orWhereIn('course_level_id', $activeSubscriptions)
                      ->orWhere(function($q) use ($user) {
                          // Admin/teacher access
                          if (in_array($user->role, ['superAdmin', 'admin', 'teacher'])) {
                              $q->whereRaw('1=1'); // Always true for admins/teachers
                          }
                      });
            })
            ->orderBy('course_level_id')
            ->orderBy('sequence')
            ->get();

        // Add access info and embed URLs
        $lessons->each(function($lesson) use ($user) {
            $lesson->has_access = $this->checkLessonAccess($user, $lesson);
            if ($lesson->has_access) {
                $lesson->embed_url = $this->generatePlaylistEmbedUrl($lesson->youtube_playlist_id);
            }
        });

        return response()->json([
            'success' => true,
            'data' => $lessons
        ]);
    }

    /**
     * Generate YouTube playlist embed URL
     */
    private function generatePlaylistEmbedUrl($playlistId, $request = null)
    {
        $baseUrl = 'https://www.youtube.com/embed/videoseries';
        
        $params = [
            'list' => $playlistId,
            'rel' => '0', // Don't show related videos
            'modestbranding' => '1', // Modest branding
            'controls' => '1', // Show controls
            'showinfo' => '0', // Don't show video info
        ];

        // Add optional parameters from request
        if ($request) {
            if ($request->has('autoplay')) {
                $params['autoplay'] = $request->boolean('autoplay') ? '1' : '0';
            }
            if ($request->has('start_time')) {
                $params['start'] = $request->start_time;
            }
        }

        return $baseUrl . '?' . http_build_query($params);
    }

    /**
     * Generate iframe HTML for embedding
     */
    private function generateIframeHtml($embedUrl, $width = 560, $height = 315)
    {
        return sprintf(
            '<iframe width="%d" height="%d" src="%s" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>',
            $width,
            $height,
            $embedUrl
        );
    }

    /**
     * Check if user has access to a lesson
     */
    private function checkLessonAccess($user, $lesson)
    {
        if (!$user) {
            return false;
        }

        // Admins and teachers have full access
        if (in_array($user->role, ['superAdmin', 'admin', 'teacher'])) {
            return true;
        }

        // If lesson doesn't require payment, it's free
        if (!$lesson->requires_payment) {
            return true;
        }

        // Check if user has active subscription for this level
        $hasSubscription = Subscription::where('student_id', $user->id)
            ->where('level_id', $lesson->course_level_id)
            ->where('renewal_status', 'active')
            ->where('start_date', '<=', now())
            ->where('end_date', '>=', now())
            ->exists();

        return $hasSubscription;
    }

    /**
     * Get video statistics for admin dashboard
     */
    public function getVideoStats(Request $request)
    {
        $user = Auth::user();
        
        if (!in_array($user->role, ['superAdmin', 'admin'])) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access'
            ], 403);
        }

        $stats = [
            'total_video_lessons' => Lesson::whereNotNull('youtube_playlist_id')->count(),
            'free_video_lessons' => Lesson::whereNotNull('youtube_playlist_id')
                ->where('requires_payment', false)->count(),
            'paid_video_lessons' => Lesson::whereNotNull('youtube_playlist_id')
                ->where('requires_payment', true)->count(),
            'lessons_with_attachments' => Lesson::whereNotNull('youtube_playlist_id')
                ->where(function($query) {
                    $query->whereNotNull('video_attachment_id')
                          ->orWhereNotNull('pdf_attachment_id');
                })->count(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }
}
