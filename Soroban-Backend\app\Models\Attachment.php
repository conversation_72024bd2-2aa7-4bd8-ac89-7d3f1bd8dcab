<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Attachment extends Model
{
    use HasFactory;

    protected $fillable = [
        'file_url',
        'file_type',
        'file_size',
        'mime_type',
        'uploaded_by',
    ];

    protected $casts = [
        'file_size' => 'integer',
    ];

    // Relationships
    public function uploader()
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    public function videoLessons()
    {
        return $this->hasMany(Lesson::class, 'video_attachment_id');
    }

    public function pdfLessons()
    {
        return $this->hasMany(Lesson::class, 'pdf_attachment_id');
    }
}
