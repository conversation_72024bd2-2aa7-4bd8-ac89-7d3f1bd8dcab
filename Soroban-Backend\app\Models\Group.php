<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Group extends Model
{
    use HasFactory;

    protected $fillable = [
        'level_id',
        'teacher_id',
        'name',
        'description',
        'is_active',
        'start_date',
        'end_date',
        'whatsapp_link',
        'whatsapp_link_expiry',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'start_date' => 'date',
        'end_date' => 'date',
        'whatsapp_link_expiry' => 'date',
    ];

    // Relationships
    public function level()
    {
        return $this->belongsTo(CourseLevel::class, 'level_id');
    }

    public function teacher()
    {
        return $this->belongsTo(User::class, 'teacher_id');
    }

    public function members()
    {
        return $this->hasMany(GroupMember::class);
    }

    public function whatsappLinkHistory()
    {
        return $this->hasMany(WhatsappLinkHistory::class);
    }
}
