<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Lesson extends Model
{
    use HasFactory;

    protected $fillable = [
        'course_level_id',
        'day_number',
        'sequence',
        'title',
        'video_attachment_id',
        'pdf_attachment_id',
        'quiz_id',
        'youtube_playlist_url',
        'youtube_playlist_id',
        'requires_payment',
    ];

    protected $casts = [
        'day_number' => 'integer',
        'sequence' => 'integer',
        'requires_payment' => 'boolean',
    ];

    // Relationships
    public function courseLevel()
    {
        return $this->belongsTo(CourseLevel::class);
    }

    public function videoAttachment()
    {
        return $this->belongsTo(Attachment::class, 'video_attachment_id');
    }

    public function pdfAttachment()
    {
        return $this->belongsTo(Attachment::class, 'pdf_attachment_id');
    }

    public function quiz()
    {
        return $this->belongsTo(Quiz::class);
    }

    public function studentProgress()
    {
        return $this->hasMany(StudentProgress::class);
    }
}
