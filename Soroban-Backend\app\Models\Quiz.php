<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Quiz extends Model
{
    use HasFactory;

    protected $fillable = [
        'lesson_id',
        'title',
        'iframe_url',
        'time_limit',
        'max_attempts',
    ];

    protected $casts = [
        'time_limit' => 'integer',
        'max_attempts' => 'integer',
    ];

    // Relationships
    public function lesson()
    {
        return $this->belongsTo(Lesson::class);
    }

    public function studentExams()
    {
        return $this->hasMany(StudentExam::class);
    }
}
