<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StudentExam extends Model
{
    use HasFactory;

    protected $fillable = [
        'student_id',
        'exam_type',
        'exam_id',
        'quiz_id',
        'submitted',
        'grade',
        'attempt_number',
    ];

    protected $casts = [
        'submitted' => 'boolean',
        'grade' => 'float',
        'attempt_number' => 'integer',
    ];

    // Relationships
    public function student()
    {
        return $this->belongsTo(User::class, 'student_id');
    }

    public function exam()
    {
        return $this->belongsTo(Exam::class);
    }

    public function quiz()
    {
        return $this->belongsTo(Quiz::class);
    }
}
