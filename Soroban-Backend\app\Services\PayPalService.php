<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class PayPalService
{
    private $clientId;
    private $clientSecret;
    private $baseUrl;
    private $environment;

    public function __construct()
    {
        $this->clientId = config('paypal.client_id');
        $this->clientSecret = config('paypal.client_secret');
        $this->environment = config('paypal.environment', 'sandbox');
        
        // Set base URL based on environment
        $this->baseUrl = $this->environment === 'live' 
            ? 'https://api-m.paypal.com' 
            : 'https://api-m.sandbox.paypal.com';
    }

    /**
     * Create PayPal payment order
     */
    public function createOrder($orderData)
    {
        try {
            $accessToken = $this->getAccessToken();
            
            $payload = [
                'intent' => 'CAPTURE',
                'purchase_units' => [
                    [
                        'reference_id' => $orderData['order_id'],
                        'description' => $orderData['description'],
                        'amount' => [
                            'currency_code' => $orderData['currency'] ?? 'USD',
                            'value' => number_format($orderData['amount'], 2, '.', '')
                        ],
                        'custom_id' => $orderData['custom_id'] ?? null,
                    ]
                ],
                'application_context' => [
                    'return_url' => $orderData['return_url'],
                    'cancel_url' => $orderData['cancel_url'],
                    'brand_name' => config('app.name', 'Soroban'),
                    'landing_page' => 'LOGIN',
                    'user_action' => 'PAY_NOW',
                    'payment_method' => [
                        'payer_selected' => 'PAYPAL',
                        'payee_preferred' => 'IMMEDIATE_PAYMENT_REQUIRED'
                    ]
                ]
            ];

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'PayPal-Request-Id' => uniqid(),
            ])->post($this->baseUrl . '/v2/checkout/orders', $payload);

            if ($response->successful()) {
                $data = $response->json();
                
                return [
                    'success' => true,
                    'order_id' => $data['id'],
                    'approval_url' => $this->getApprovalUrl($data['links']),
                    'data' => $data,
                ];
            }

            Log::error('PayPal Create Order Error', [
                'status' => $response->status(),
                'response' => $response->body(),
                'payload' => $payload
            ]);

            return [
                'success' => false,
                'error' => 'Failed to create PayPal order',
                'details' => $response->json(),
            ];

        } catch (Exception $e) {
            Log::error('PayPal Create Order Exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => 'PayPal service unavailable',
                'details' => $e->getMessage(),
            ];
        }
    }

    /**
     * Capture PayPal payment
     */
    public function captureOrder($orderId)
    {
        try {
            $accessToken = $this->getAccessToken();

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'PayPal-Request-Id' => uniqid(),
            ])->post($this->baseUrl . '/v2/checkout/orders/' . $orderId . '/capture');

            if ($response->successful()) {
                $data = $response->json();
                
                return [
                    'success' => true,
                    'status' => $data['status'],
                    'capture_id' => $data['purchase_units'][0]['payments']['captures'][0]['id'] ?? null,
                    'amount' => $data['purchase_units'][0]['payments']['captures'][0]['amount'] ?? null,
                    'data' => $data,
                ];
            }

            Log::error('PayPal Capture Order Error', [
                'order_id' => $orderId,
                'status' => $response->status(),
                'response' => $response->body()
            ]);

            return [
                'success' => false,
                'error' => 'Failed to capture PayPal payment',
                'details' => $response->json(),
            ];

        } catch (Exception $e) {
            Log::error('PayPal Capture Order Exception', [
                'order_id' => $orderId,
                'message' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'PayPal capture service unavailable',
                'details' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get order details
     */
    public function getOrderDetails($orderId)
    {
        try {
            $accessToken = $this->getAccessToken();

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
                'Accept' => 'application/json',
            ])->get($this->baseUrl . '/v2/checkout/orders/' . $orderId);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            }

            return [
                'success' => false,
                'error' => 'Failed to get order details',
                'details' => $response->json(),
            ];

        } catch (Exception $e) {
            Log::error('PayPal Get Order Exception', [
                'order_id' => $orderId,
                'message' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'PayPal order service unavailable',
                'details' => $e->getMessage(),
            ];
        }
    }

    /**
     * Process refund
     */
    public function refundCapture($captureId, $amount = null, $note = null)
    {
        try {
            $accessToken = $this->getAccessToken();

            $payload = [];
            if ($amount) {
                $payload['amount'] = [
                    'currency_code' => 'USD',
                    'value' => number_format($amount, 2, '.', '')
                ];
            }
            if ($note) {
                $payload['note_to_payer'] = $note;
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'PayPal-Request-Id' => uniqid(),
            ])->post($this->baseUrl . '/v2/payments/captures/' . $captureId . '/refund', $payload);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            }

            return [
                'success' => false,
                'error' => 'Failed to process refund',
                'details' => $response->json(),
            ];

        } catch (Exception $e) {
            Log::error('PayPal Refund Exception', [
                'capture_id' => $captureId,
                'amount' => $amount,
                'message' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'PayPal refund service unavailable',
                'details' => $e->getMessage(),
            ];
        }
    }

    /**
     * Verify webhook signature
     */
    public function verifyWebhook($headers, $body)
    {
        try {
            $accessToken = $this->getAccessToken();

            $payload = [
                'auth_algo' => $headers['PAYPAL-AUTH-ALGO'] ?? '',
                'cert_id' => $headers['PAYPAL-CERT-ID'] ?? '',
                'transmission_id' => $headers['PAYPAL-TRANSMISSION-ID'] ?? '',
                'transmission_sig' => $headers['PAYPAL-TRANSMISSION-SIG'] ?? '',
                'transmission_time' => $headers['PAYPAL-TRANSMISSION-TIME'] ?? '',
                'webhook_id' => config('paypal.webhook_id'),
                'webhook_event' => json_decode($body, true),
            ];

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ])->post($this->baseUrl . '/v1/notifications/verify-webhook-signature', $payload);

            if ($response->successful()) {
                $data = $response->json();
                return [
                    'success' => true,
                    'verified' => $data['verification_status'] === 'SUCCESS',
                ];
            }

            return [
                'success' => false,
                'verified' => false,
                'error' => 'Webhook verification failed',
            ];

        } catch (Exception $e) {
            Log::error('PayPal Webhook Verification Exception', [
                'message' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'verified' => false,
                'error' => 'Webhook verification unavailable',
            ];
        }
    }

    /**
     * Get PayPal access token
     */
    private function getAccessToken()
    {
        try {
            $response = Http::withBasicAuth($this->clientId, $this->clientSecret)
                ->asForm()
                ->post($this->baseUrl . '/v1/oauth2/token', [
                    'grant_type' => 'client_credentials'
                ]);

            if ($response->successful()) {
                $data = $response->json();
                return $data['access_token'];
            }

            throw new Exception('Failed to get PayPal access token: ' . $response->body());

        } catch (Exception $e) {
            Log::error('PayPal Access Token Exception', [
                'message' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Extract approval URL from PayPal links
     */
    private function getApprovalUrl($links)
    {
        foreach ($links as $link) {
            if ($link['rel'] === 'approve') {
                return $link['href'];
            }
        }
        return null;
    }

    /**
     * Get PayPal JavaScript SDK URL
     */
    public function getJavaScriptSDKUrl($currency = 'USD')
    {
        $params = [
            'client-id' => $this->clientId,
            'currency' => $currency,
            'intent' => 'capture',
            'components' => 'buttons,funding-eligibility'
        ];

        return 'https://www.paypal.com/sdk/js?' . http_build_query($params);
    }
}
