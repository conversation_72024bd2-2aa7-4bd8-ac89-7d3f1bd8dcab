<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class PaymentGatewayService
{
    private $clientId;
    private $secretKey;
    private $baseUrl;
    private $environment;

    public function __construct()
    {
        $this->clientId = config('payment.client_id');
        $this->secretKey = config('payment.secret_key');
        $this->baseUrl = config('payment.base_url');
        $this->environment = config('payment.environment', 'sandbox');
    }

    /**
     * Create a payment session
     */
    public function createPaymentSession($paymentData)
    {
        try {
            $payload = [
                'client_id' => $this->clientId,
                'amount' => $paymentData['amount'],
                'currency' => $paymentData['currency'] ?? 'USD',
                'order_id' => $paymentData['order_id'],
                'customer' => [
                    'name' => $paymentData['customer_name'],
                    'email' => $paymentData['customer_email'],
                    'phone' => $paymentData['customer_phone'] ?? null,
                ],
                'description' => $paymentData['description'],
                'return_url' => $paymentData['return_url'],
                'cancel_url' => $paymentData['cancel_url'],
                'webhook_url' => $paymentData['webhook_url'],
                'metadata' => $paymentData['metadata'] ?? [],
            ];

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->getAccessToken(),
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ])->post($this->baseUrl . '/payments/create', $payload);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            }

            Log::error('Payment Gateway Error', [
                'status' => $response->status(),
                'response' => $response->body(),
                'payload' => $payload
            ]);

            return [
                'success' => false,
                'error' => $response->json()['message'] ?? 'Payment creation failed',
                'details' => $response->json(),
            ];

        } catch (Exception $e) {
            Log::error('Payment Gateway Exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => 'Payment service unavailable',
                'details' => $e->getMessage(),
            ];
        }
    }

    /**
     * Verify payment status
     */
    public function verifyPayment($paymentId)
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->getAccessToken(),
                'Accept' => 'application/json',
            ])->get($this->baseUrl . '/payments/' . $paymentId);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            }

            return [
                'success' => false,
                'error' => 'Payment verification failed',
                'details' => $response->json(),
            ];

        } catch (Exception $e) {
            Log::error('Payment Verification Exception', [
                'payment_id' => $paymentId,
                'message' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Payment verification unavailable',
                'details' => $e->getMessage(),
            ];
        }
    }

    /**
     * Process refund
     */
    public function processRefund($paymentId, $amount = null, $reason = null)
    {
        try {
            $payload = [
                'payment_id' => $paymentId,
                'amount' => $amount, // null for full refund
                'reason' => $reason ?? 'Customer request',
            ];

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->getAccessToken(),
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ])->post($this->baseUrl . '/payments/refund', $payload);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            }

            return [
                'success' => false,
                'error' => 'Refund processing failed',
                'details' => $response->json(),
            ];

        } catch (Exception $e) {
            Log::error('Refund Processing Exception', [
                'payment_id' => $paymentId,
                'amount' => $amount,
                'message' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Refund service unavailable',
                'details' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get payment methods
     */
    public function getPaymentMethods($country = null, $currency = null)
    {
        try {
            $params = [];
            if ($country) $params['country'] = $country;
            if ($currency) $params['currency'] = $currency;

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->getAccessToken(),
                'Accept' => 'application/json',
            ])->get($this->baseUrl . '/payment-methods', $params);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            }

            return [
                'success' => false,
                'error' => 'Failed to fetch payment methods',
                'details' => $response->json(),
            ];

        } catch (Exception $e) {
            Log::error('Payment Methods Exception', [
                'message' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Payment methods service unavailable',
                'details' => $e->getMessage(),
            ];
        }
    }

    /**
     * Handle webhook verification
     */
    public function verifyWebhook($payload, $signature)
    {
        try {
            $expectedSignature = hash_hmac('sha256', $payload, $this->secretKey);
            
            if (hash_equals($expectedSignature, $signature)) {
                return [
                    'success' => true,
                    'verified' => true,
                ];
            }

            return [
                'success' => false,
                'verified' => false,
                'error' => 'Invalid webhook signature',
            ];

        } catch (Exception $e) {
            Log::error('Webhook Verification Exception', [
                'message' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'verified' => false,
                'error' => 'Webhook verification failed',
            ];
        }
    }

    /**
     * Get access token for API authentication
     */
    private function getAccessToken()
    {
        try {
            $response = Http::post($this->baseUrl . '/auth/token', [
                'client_id' => $this->clientId,
                'client_secret' => $this->secretKey,
                'grant_type' => 'client_credentials',
            ]);

            if ($response->successful()) {
                $data = $response->json();
                return $data['access_token'];
            }

            throw new Exception('Failed to get access token');

        } catch (Exception $e) {
            Log::error('Access Token Exception', [
                'message' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Format amount for payment gateway (usually in cents)
     */
    public function formatAmount($amount, $currency = 'USD')
    {
        // Most payment gateways expect amounts in cents
        $multipliers = [
            'USD' => 100,
            'EUR' => 100,
            'GBP' => 100,
            'SAR' => 100,
            'AED' => 100,
            'EGP' => 100,
        ];

        $multiplier = $multipliers[$currency] ?? 100;
        return intval($amount * $multiplier);
    }

    /**
     * Format amount from payment gateway (convert from cents)
     */
    public function parseAmount($amount, $currency = 'USD')
    {
        $divisors = [
            'USD' => 100,
            'EUR' => 100,
            'GBP' => 100,
            'SAR' => 100,
            'AED' => 100,
            'EGP' => 100,
        ];

        $divisor = $divisors[$currency] ?? 100;
        return $amount / $divisor;
    }
}
