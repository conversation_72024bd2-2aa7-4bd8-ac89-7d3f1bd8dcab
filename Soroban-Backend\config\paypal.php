<?php

return [
    /*
    |--------------------------------------------------------------------------
    | PayPal Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for PayPal payment integration
    |
    */

    'client_id' => env('PAYPAL_CLIENT_ID'),
    'client_secret' => env('PAYPAL_CLIENT_SECRET'),
    'environment' => env('PAYPAL_ENVIRONMENT', 'sandbox'), // 'sandbox' or 'live'
    'webhook_id' => env('PAYPAL_WEBHOOK_ID'),

    /*
    |--------------------------------------------------------------------------
    | PayPal URLs
    |--------------------------------------------------------------------------
    */

    'urls' => [
        'sandbox' => [
            'api' => 'https://api-m.sandbox.paypal.com',
            'web' => 'https://www.sandbox.paypal.com',
        ],
        'live' => [
            'api' => 'https://api-m.paypal.com',
            'web' => 'https://www.paypal.com',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Default Currency
    |--------------------------------------------------------------------------
    */

    'currency' => env('PAYPAL_CURRENCY', 'USD'),

    /*
    |--------------------------------------------------------------------------
    | Application Context
    |--------------------------------------------------------------------------
    */

    'application_context' => [
        'brand_name' => env('APP_NAME', 'Soroban'),
        'landing_page' => 'LOGIN',
        'user_action' => 'PAY_NOW',
        'shipping_preference' => 'NO_SHIPPING',
    ],

    /*
    |--------------------------------------------------------------------------
    | Webhook Events
    |--------------------------------------------------------------------------
    |
    | PayPal webhook events to listen for
    |
    */

    'webhook_events' => [
        'CHECKOUT.ORDER.APPROVED',
        'CHECKOUT.ORDER.COMPLETED',
        'PAYMENT.CAPTURE.COMPLETED',
        'PAYMENT.CAPTURE.DENIED',
        'PAYMENT.CAPTURE.REFUNDED',
    ],
];
