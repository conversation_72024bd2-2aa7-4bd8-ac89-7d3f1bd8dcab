<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use App\Models\User;
use App\Models\UserProfile;
use Illuminate\Support\Facades\Hash;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔧 Creating test user...\n";

try {
    // Check if test user already exists
    $existingUser = User::where('email', '<EMAIL>')->first();
    
    if ($existingUser) {
        echo "✅ Test user already exists: <EMAIL>\n";
        echo "User ID: {$existingUser->id}\n";
        echo "Role: {$existingUser->role}\n";
    } else {
        // Create test user
        $user = User::create([
            'first_name' => 'Test',
            'second_name' => 'User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'age' => 25,
            'role' => 'student',
            'age_group' => 'adults',
            'user_type' => 'regular',
            'account_status' => 'active',
            'can_manage_whatsapp_links' => false,
        ]);

        // Create user profile
        UserProfile::create([
            'user_id' => $user->id,
            'phone' => '+**********',
            'address' => 'Test Address',
            'bio' => 'Test user for login testing',
        ]);

        echo "✅ Test user created successfully!\n";
        echo "Email: <EMAIL>\n";
        echo "Password: password\n";
        echo "Role: {$user->role}\n";
    }

    echo "\n🧪 Now you can test login with:\n";
    echo "Email: <EMAIL>\n";
    echo "Password: password\n";

} catch (Exception $e) {
    echo "❌ Error creating test user: " . $e->getMessage() . "\n";
}

?>
