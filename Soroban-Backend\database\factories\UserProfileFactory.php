<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\UserProfile>
 */
class UserProfileFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $birthDate = $this->faker->dateTimeBetween('-60 years', '-6 years');

        return [
            'phone' => $this->faker->phoneNumber(),
            'parent_phone' => $this->faker->optional(0.3)->phoneNumber(),
            'birth_date' => $birthDate,
            'address' => $this->faker->address(),
            'bio' => $this->faker->optional(0.7)->paragraph(),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the profile is for a child (needs parent phone).
     */
    public function forChild(): static
    {
        return $this->state(fn (array $attributes) => [
            'birth_date' => $this->faker->dateTimeBetween('-17 years', '-6 years'),
            'parent_phone' => $this->faker->phoneNumber(),
            'bio' => $this->faker->optional(0.5)->sentence(),
        ]);
    }

    /**
     * Indicate that the profile is for an adult.
     */
    public function forAdult(): static
    {
        return $this->state(fn (array $attributes) => [
            'birth_date' => $this->faker->dateTimeBetween('-60 years', '-18 years'),
            'parent_phone' => null,
            'bio' => $this->faker->optional(0.8)->paragraph(),
        ]);
    }
}
