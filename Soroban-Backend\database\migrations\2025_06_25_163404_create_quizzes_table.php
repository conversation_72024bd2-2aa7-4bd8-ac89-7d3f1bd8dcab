<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('quizzes', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('lesson_id');
            $table->string('title')->nullable();
            $table->text('iframe_url')->comment('in case make it in google_forms and set it in the exam field');
            $table->integer('time_limit')->nullable();
            $table->integer('max_attempts')->default(1);
            $table->timestamps();
            
            // Note: Foreign key will be added after lessons table is created
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('quizzes');
    }
};
