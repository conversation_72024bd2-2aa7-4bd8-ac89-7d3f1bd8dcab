<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('student_exams', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('student_id');
            $table->enum('exam_type', ['placement', 'lesson_quiz', 'final']);
            $table->unsignedBigInteger('exam_id')->nullable();
            $table->unsignedBigInteger('quiz_id')->nullable();
            $table->boolean('submitted')->default(false);
            $table->float('grade')->nullable();
            $table->integer('attempt_number')->default(1);
            $table->timestamps();
            
            $table->foreign('student_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('exam_id')->references('id')->on('exams')->onDelete('set null');
            $table->foreign('quiz_id')->references('id')->on('quizzes')->onDelete('set null');
            $table->index('student_id');

            // Note: Check constraint removed for SQLite compatibility
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('student_exams');
    }
};
