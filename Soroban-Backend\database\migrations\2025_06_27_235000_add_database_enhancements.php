<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add missing indexes for better performance
        $this->addPerformanceIndexes();
        
        // Add validation constraints
        $this->addValidationConstraints();
        
        // Add audit fields to important tables
        $this->addAuditFields();
        
        // Add soft deletes to critical tables
        $this->addSoftDeletes();
        
        // Add table comments
        $this->addTableComments();
    }

    /**
     * Add performance indexes
     */
    private function addPerformanceIndexes(): void
    {
        // Subscriptions indexes
        if (Schema::hasTable('subscriptions')) {
            Schema::table('subscriptions', function (Blueprint $table) {
                if (!$this->indexExists('subscriptions', 'idx_subscriptions_student_level')) {
                    $table->index(['student_id', 'level_id'], 'idx_subscriptions_student_level');
                }
                if (!$this->indexExists('subscriptions', 'idx_subscriptions_status')) {
                    $table->index('renewal_status', 'idx_subscriptions_status');
                }
            });
        }

        // Payments indexes
        if (Schema::hasTable('payments')) {
            Schema::table('payments', function (Blueprint $table) {
                if (!$this->indexExists('payments', 'idx_payments_student_id')) {
                    $table->index('student_id', 'idx_payments_student_id');
                }
                if (!$this->indexExists('payments', 'idx_payments_confirmed')) {
                    $table->index('is_confirmed', 'idx_payments_confirmed');
                }
            });
        }

        // Student progress indexes
        if (Schema::hasTable('student_progress')) {
            Schema::table('student_progress', function (Blueprint $table) {
                if (!$this->indexExists('student_progress', 'idx_student_progress_student_lesson')) {
                    $table->index(['student_id', 'lesson_id'], 'idx_student_progress_student_lesson');
                }
                if (!$this->indexExists('student_progress', 'idx_student_progress_status')) {
                    $table->index('status', 'idx_student_progress_status');
                }
            });
        }

        // Competitions indexes
        if (Schema::hasTable('competitions')) {
            Schema::table('competitions', function (Blueprint $table) {
                if (!$this->indexExists('competitions', 'idx_competitions_status')) {
                    $table->index('status', 'idx_competitions_status');
                }
                if (!$this->indexExists('competitions', 'idx_competitions_dates')) {
                    $table->index(['start_date', 'end_date'], 'idx_competitions_dates');
                }
            });
        }

        // Notifications indexes
        if (Schema::hasTable('notifications')) {
            Schema::table('notifications', function (Blueprint $table) {
                if (!$this->indexExists('notifications', 'idx_notifications_user_read')) {
                    $table->index(['user_id', 'is_read'], 'idx_notifications_user_read');
                }
                if (!$this->indexExists('notifications', 'idx_notifications_type')) {
                    $table->index('type', 'idx_notifications_type');
                }
            });
        }

        // Student exams indexes
        if (Schema::hasTable('student_exams')) {
            Schema::table('student_exams', function (Blueprint $table) {
                if (!$this->indexExists('student_exams', 'idx_student_exams_type')) {
                    $table->index('exam_type', 'idx_student_exams_type');
                }
                if (!$this->indexExists('student_exams', 'idx_student_exams_submitted')) {
                    $table->index('submitted', 'idx_student_exams_submitted');
                }
            });
        }
    }

    /**
     * Add validation constraints
     */
    private function addValidationConstraints(): void
    {
        // Payments table constraints
        if (Schema::hasTable('payments')) {
            try {
                DB::statement('ALTER TABLE payments ADD CONSTRAINT chk_positive_amount CHECK (amount > 0)');
            } catch (Exception $e) {
                // Constraint might already exist
            }
        }

        // Student progress constraints
        if (Schema::hasTable('student_progress')) {
            try {
                DB::statement('ALTER TABLE student_progress ADD CONSTRAINT chk_valid_percentage CHECK (completion_percentage >= 0 AND completion_percentage <= 100)');
            } catch (Exception $e) {
                // Constraint might already exist
            }
        }

        // Discounts table constraints
        if (Schema::hasTable('discounts')) {
            try {
                DB::statement('ALTER TABLE discounts ADD CONSTRAINT chk_valid_discount CHECK (discount_percentage > 0 AND discount_percentage <= 100)');
            } catch (Exception $e) {
                // Constraint might already exist
            }
            
            try {
                DB::statement('ALTER TABLE discounts ADD CONSTRAINT chk_valid_dates CHECK (start_date <= end_date)');
            } catch (Exception $e) {
                // Constraint might already exist
            }
        }

        // Subscriptions table constraints
        if (Schema::hasTable('subscriptions')) {
            try {
                DB::statement('ALTER TABLE subscriptions ADD CONSTRAINT chk_subscription_dates CHECK (start_date <= end_date)');
            } catch (Exception $e) {
                // Constraint might already exist
            }
            
            try {
                DB::statement('ALTER TABLE subscriptions ADD CONSTRAINT chk_subscription_amount CHECK (amount > 0)');
            } catch (Exception $e) {
                // Constraint might already exist
            }
        }

        // Groups table constraints
        if (Schema::hasTable('groups')) {
            try {
                DB::statement('ALTER TABLE groups ADD CONSTRAINT chk_group_dates CHECK (start_date <= end_date OR end_date IS NULL)');
            } catch (Exception $e) {
                // Constraint might already exist
            }
        }

        // Competitions table constraints
        if (Schema::hasTable('competitions')) {
            try {
                DB::statement('ALTER TABLE competitions ADD CONSTRAINT chk_competition_dates CHECK (start_date <= end_date)');
            } catch (Exception $e) {
                // Constraint might already exist
            }
        }
    }

    /**
     * Add audit fields to important tables
     */
    private function addAuditFields(): void
    {
        $auditTables = ['courses', 'course_levels', 'lessons', 'exams', 'groups', 'competitions'];
        
        foreach ($auditTables as $tableName) {
            if (Schema::hasTable($tableName)) {
                Schema::table($tableName, function (Blueprint $table) use ($tableName) {
                    if (!Schema::hasColumn($tableName, 'created_by')) {
                        $table->unsignedBigInteger('created_by')->nullable()->after('id');
                        $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
                    }
                    if (!Schema::hasColumn($tableName, 'updated_by')) {
                        $table->unsignedBigInteger('updated_by')->nullable()->after('created_by');
                        $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');
                    }
                });
            }
        }
    }

    /**
     * Add soft deletes to critical tables
     */
    private function addSoftDeletes(): void
    {
        $softDeleteTables = ['users', 'courses', 'course_levels', 'lessons', 'companies', 'groups'];
        
        foreach ($softDeleteTables as $tableName) {
            if (Schema::hasTable($tableName)) {
                Schema::table($tableName, function (Blueprint $table) use ($tableName) {
                    if (!Schema::hasColumn($tableName, 'deleted_at')) {
                        $table->softDeletes();
                    }
                });
            }
        }
    }

    /**
     * Add table comments for documentation
     */
    private function addTableComments(): void
    {
        $tableComments = [
            'users' => 'System users including students, teachers, administrators, and guests',
            'user_profiles' => 'Extended user profile information and contact details',
            'courses' => 'Main course catalog for the educational platform',
            'course_levels' => 'Three-tier level system for each course (one, two, three)',
            'lessons' => 'Individual lessons within course levels with attachments and quizzes',
            'quizzes' => 'Lesson-based assessments with time limits and attempt tracking',
            'exams' => 'Level-based examinations (placement and final exams)',
            'student_exams' => 'Student exam submissions and grading records',
            'student_progress' => 'Detailed progress tracking for students across lessons and levels',
            'companies' => 'Corporate clients for bulk training and partnerships',
            'discounts' => 'Promotional discounts for courses and levels',
            'subscriptions' => 'Student enrollments in course levels with payment tracking',
            'payments' => 'Payment records and transaction management',
            'groups' => 'Study groups with WhatsApp integration for communication',
            'group_members' => 'Group membership tracking for students and teachers',
            'certificates' => 'Achievement certificates issued upon level completion',
            'competitions' => 'Educational competitions and contests for student engagement',
            'competition_participants' => 'Competition registration and participation tracking',
            'notifications' => 'Multi-type notification system for platform communications',
            'whatsapp_link_history' => 'Historical tracking of WhatsApp group link changes',
            'attachments' => 'File management system for course materials and resources'
        ];

        foreach ($tableComments as $tableName => $comment) {
            if (Schema::hasTable($tableName)) {
                try {
                    DB::statement("ALTER TABLE {$tableName} COMMENT = '{$comment}'");
                } catch (Exception $e) {
                    // Comment might not be supported or already exists
                }
            }
        }
    }

    /**
     * Check if index exists
     */
    private function indexExists(string $table, string $indexName): bool
    {
        $indexes = DB::select("SHOW INDEX FROM {$table} WHERE Key_name = ?", [$indexName]);
        return !empty($indexes);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove audit fields
        $auditTables = ['courses', 'course_levels', 'lessons', 'exams', 'groups', 'competitions'];
        foreach ($auditTables as $tableName) {
            if (Schema::hasTable($tableName)) {
                Schema::table($tableName, function (Blueprint $table) use ($tableName) {
                    if (Schema::hasColumn($tableName, 'created_by')) {
                        $table->dropForeign(['created_by']);
                        $table->dropColumn('created_by');
                    }
                    if (Schema::hasColumn($tableName, 'updated_by')) {
                        $table->dropForeign(['updated_by']);
                        $table->dropColumn('updated_by');
                    }
                });
            }
        }

        // Remove soft deletes
        $softDeleteTables = ['users', 'courses', 'course_levels', 'lessons', 'companies', 'groups'];
        foreach ($softDeleteTables as $tableName) {
            if (Schema::hasTable($tableName)) {
                Schema::table($tableName, function (Blueprint $table) use ($tableName) {
                    if (Schema::hasColumn($tableName, 'deleted_at')) {
                        $table->dropSoftDeletes();
                    }
                });
            }
        }

        // Remove constraints (MySQL specific)
        $constraints = [
            'payments' => ['chk_positive_amount'],
            'student_progress' => ['chk_valid_percentage'],
            'discounts' => ['chk_valid_discount', 'chk_valid_dates'],
            'subscriptions' => ['chk_subscription_dates', 'chk_subscription_amount'],
            'groups' => ['chk_group_dates'],
            'competitions' => ['chk_competition_dates']
        ];

        foreach ($constraints as $tableName => $tableConstraints) {
            if (Schema::hasTable($tableName)) {
                foreach ($tableConstraints as $constraint) {
                    try {
                        DB::statement("ALTER TABLE {$tableName} DROP CONSTRAINT IF EXISTS {$constraint}");
                    } catch (Exception $e) {
                        // Constraint might not exist
                    }
                }
            }
        }

        // Note: Indexes and comments are typically kept even when rolling back
        // as they don't affect data integrity and provide performance benefits
    }
};
