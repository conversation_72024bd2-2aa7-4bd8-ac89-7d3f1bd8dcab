<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\UserProfile;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class SuperAdminSeeder extends Seeder
{
    public function run(): void
    {
        // Create Super Admin User
        $superAdmin = User::create([
            'first_name' => 'Super',
            'second_name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('superadmin123'),
            'role' => 'superAdmin',
            'age_group' => 'adults',
            'age' => 30,
            'user_type' => 'regular',
            'account_status' => 'active',
            'can_manage_whatsapp_links' => true,
        ]);

        // Create Super Admin Profile
        UserProfile::create([
            'user_id' => $superAdmin->id,
            'phone' => '+**********',
            'birth_date' => '1994-01-01',
            'address' => 'Soroban Educational Center, Main Office',
            'bio' => 'Super Administrator of Soroban-AlBarq Educational Platform. Responsible for overall system management and administration.',
        ]);

        // Create Additional Admin User
        $admin = User::create([
            'first_name' => 'Admin',
            'second_name' => 'User',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'role' => 'admin',
            'age_group' => 'adults',
            'age' => 28,
            'user_type' => 'regular',
            'account_status' => 'active',
            'can_manage_whatsapp_links' => true,
        ]);

        // Create Admin Profile
        UserProfile::create([
            'user_id' => $admin->id,
            'phone' => '+**********',
            'birth_date' => '1996-01-01',
            'address' => 'Soroban Educational Center, Admin Office',
            'bio' => 'Platform Administrator responsible for day-to-day operations and user management.',
        ]);

        // Create Sample Teacher
        $teacher = User::create([
            'first_name' => 'Teacher',
            'second_name' => 'Demo',
            'email' => '<EMAIL>',
            'password' => Hash::make('teacher123'),
            'role' => 'teacher',
            'age_group' => 'adults',
            'age' => 35,
            'user_type' => 'regular',
            'account_status' => 'active',
            'can_manage_whatsapp_links' => false,
        ]);

        // Create Teacher Profile
        UserProfile::create([
            'user_id' => $teacher->id,
            'phone' => '+**********',
            'birth_date' => '1989-01-01',
            'address' => 'Soroban Educational Center, Teaching Department',
            'bio' => 'Experienced Soroban instructor with 10+ years of teaching experience.',
        ]);

        // Create Sample Student
        $student = User::create([
            'first_name' => 'Student',
            'second_name' => 'Demo',
            'email' => '<EMAIL>',
            'password' => Hash::make('student123'),
            'role' => 'student',
            'age_group' => 'kids',
            'age' => 12,
            'user_type' => 'regular',
            'account_status' => 'active',
        ]);

        // Create Student Profile
        UserProfile::create([
            'user_id' => $student->id,
            'phone' => '+**********',
            'parent_phone' => '+**********',
            'birth_date' => '2012-01-01',
            'address' => '123 Student Street, Student City',
            'bio' => 'Enthusiastic young learner interested in mastering Soroban techniques.',
        ]);

        $this->command->info('Super Admin and sample users created successfully!');
        $this->command->info('Super Admin: <EMAIL> / superadmin123');
        $this->command->info('Admin: <EMAIL> / admin123');
        $this->command->info('Teacher: <EMAIL> / teacher123');
        $this->command->info('Student: <EMAIL> / student123');
    }
}