<?php
namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    public function run()
    {
        User::factory()->create([
            'first_name' => 'Admin',
            'second_name' => 'User',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'), // Set custom password here
            'role' => 'superAdmin',
            'age_group' => 'adults',
            'age' => 30,
        ]);
    }
}
