# 📱 WhatsApp Group Integration API Documentation

## 🚀 Overview

The Soroban-AlBarq platform includes comprehensive WhatsApp group integration for enhanced student-teacher communication and community building.

## 🔐 Authentication

All endpoints require Bearer token authentication:
```
Authorization: Bearer {your_token}
```

## 📋 Group Management Endpoints

### 1. Get All Groups
```http
GET /api/groups
```

**Query Parameters:**
- `level_id` (optional) - Filter by course level
- `teacher_id` (optional) - Filter by teacher
- `is_active` (optional) - Filter by active status
- `search` (optional) - Search by name/description
- `has_whatsapp` (optional) - Filter by WhatsApp link presence
- `per_page` (optional) - Items per page (default: 15)

**Response:**
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": 1,
        "name": "Beginner Soroban Group",
        "description": "Group for beginner level students",
        "level_id": 1,
        "teacher_id": 2,
        "is_active": true,
        "start_date": "2025-01-01",
        "end_date": "2025-06-30",
        "whatsapp_link": "https://chat.whatsapp.com/ABC123",
        "whatsapp_link_expiry": "2025-02-01",
        "member_count": 15,
        "whatsapp_link_expired": false,
        "level": {
          "id": 1,
          "title": "Beginner Level",
          "course": {
            "id": 1,
            "name": "Soroban Fundamentals"
          }
        },
        "teacher": {
          "id": 2,
          "first_name": "Ahmed",
          "second_name": "Hassan",
          "profile": {...}
        }
      }
    ],
    "current_page": 1,
    "total": 25
  }
}
```

### 2. Create Group
```http
POST /api/groups
```

**Request Body:**
```json
{
  "level_id": 1,
  "teacher_id": 2,
  "name": "Advanced Soroban Group",
  "description": "Group for advanced level students",
  "start_date": "2025-01-15",
  "end_date": "2025-07-15",
  "whatsapp_link": "https://chat.whatsapp.com/XYZ789",
  "whatsapp_link_expiry": "2025-03-01"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Group created successfully",
  "data": {
    "id": 3,
    "name": "Advanced Soroban Group",
    // ... full group object
  }
}
```

### 3. Get Group Details
```http
GET /api/groups/{id}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "Beginner Soroban Group",
    // ... full group details
    "whatsapp_history": [
      {
        "id": 1,
        "whatsapp_link": "https://chat.whatsapp.com/ABC123",
        "updated_at": "2025-01-01T10:00:00Z",
        "user": {
          "id": 2,
          "first_name": "Ahmed",
          "second_name": "Hassan"
        }
      }
    ]
  }
}
```

### 4. Update Group
```http
PUT /api/groups/{id}
```

**Request Body:**
```json
{
  "name": "Updated Group Name",
  "description": "Updated description",
  "is_active": true,
  "whatsapp_link": "https://chat.whatsapp.com/NEW123",
  "whatsapp_link_expiry": "2025-04-01"
}
```

### 5. Delete Group
```http
DELETE /api/groups/{id}
```

**Response:**
```json
{
  "success": true,
  "message": "Group deleted successfully"
}
```

## 🔗 WhatsApp Link Management

### 1. Update WhatsApp Link
```http
POST /api/groups/{id}/update-whatsapp-link
```

**Request Body:**
```json
{
  "whatsapp_link": "https://chat.whatsapp.com/NEWLINK123",
  "whatsapp_link_expiry": "2025-05-01"
}
```

**Features:**
- ✅ **Link Validation** - Validates WhatsApp link format
- ✅ **Permission Check** - Only teachers and admins can update
- ✅ **History Logging** - Tracks all link changes
- ✅ **Member Notification** - Notifies all group members

## 👥 Group Member Management

### 1. Get Group Members
```http
GET /api/groups/{id}/members
```

**Query Parameters:**
- `search` (optional) - Search by member name
- `role` (optional) - Filter by user role

**Response:**
```json
{
  "success": true,
  "data": {
    "group": {
      "id": 1,
      "name": "Beginner Soroban Group",
      // ... group details
    },
    "members": [
      {
        "id": 1,
        "user_id": 5,
        "group_id": 1,
        "joined_at": "2025-01-01T10:00:00Z",
        "user": {
          "id": 5,
          "first_name": "Sara",
          "second_name": "Ahmed",
          "role": "student",
          "profile": {...}
        }
      }
    ],
    "member_count": 15,
    "students_count": 14
  }
}
```

### 2. Add Member to Group
```http
POST /api/groups/{id}/add-member
```

**Request Body:**
```json
{
  "user_id": 10
}
```

**Features:**
- ✅ **Eligibility Check** - Validates subscription for students
- ✅ **Duplicate Prevention** - Prevents adding existing members
- ✅ **Automatic Notification** - Sends notification to new member

### 3. Remove Member from Group
```http
DELETE /api/groups/{id}/remove-member/{user_id}
```

**Features:**
- ✅ **Teacher Protection** - Cannot remove group teacher
- ✅ **Notification** - Notifies removed member

### 4. Bulk Add Members
```http
POST /api/groups/{id}/bulk-add-members
```

**Request Body:**
```json
{
  "user_ids": [10, 11, 12, 13, 14]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Bulk member addition completed",
  "data": {
    "added_members": 3,
    "skipped_users": 1,
    "errors": 1,
    "details": {
      "added": [...],
      "skipped": [
        {
          "user_id": 11,
          "reason": "Already a member"
        }
      ],
      "errors": [
        {
          "user_id": 14,
          "error": "No active subscription"
        }
      ]
    }
  }
}
```

## 📊 Statistics and Analytics

### 1. Group Statistics
```http
GET /api/groups/stats
```

**Response:**
```json
{
  "success": true,
  "data": {
    "total_groups": 25,
    "active_groups": 20,
    "groups_with_whatsapp": 18,
    "expired_whatsapp_links": 3,
    "total_members": 150,
    "groups_by_level": [
      {
        "level": "Beginner Level",
        "course": "Soroban Fundamentals",
        "count": 8
      }
    ]
  }
}
```

### 2. My Groups (Student/Teacher)
```http
GET /api/groups/my-groups
```

**Query Parameters:**
- `is_active` (optional) - Filter by active status

## 🔒 Permission System

### **Role-Based Access:**

| Action | Super Admin | Admin | Teacher | Student |
|--------|-------------|-------|---------|---------|
| Create Group | ✅ | ✅ | ✅ | ❌ |
| Update Group | ✅ | ✅ | Own Groups | ❌ |
| Delete Group | ✅ | ✅ | Own Groups | ❌ |
| Manage WhatsApp Links | ✅ | ✅ | Own Groups | ❌ |
| Add/Remove Members | ✅ | ✅ | Own Groups | ❌ |
| View Groups | ✅ | ✅ | ✅ | Own Groups |

### **Subscription-Based Access:**
- ✅ Students must have **active subscription** for the group's level
- ✅ Automatic eligibility checking during member addition
- ✅ Real-time subscription validation

## 🔔 Notification System

### **Automatic Notifications:**
1. **Member Added** - "You have been added to group '{group_name}'"
2. **Member Removed** - "You have been removed from group '{group_name}'"
3. **WhatsApp Link Updated** - "WhatsApp group link has been updated"

### **Notification Types:**
- `whatsapp_invite` - WhatsApp-related notifications
- `general` - General group notifications

## 🛡️ Security Features

### **WhatsApp Link Validation:**
- ✅ Validates proper WhatsApp link format
- ✅ Supports `chat.whatsapp.com` and `wa.me` formats
- ✅ Prevents invalid or malicious links

### **Access Control:**
- ✅ Role-based permissions
- ✅ Subscription verification
- ✅ Teacher ownership validation

### **Audit Trail:**
- ✅ Complete WhatsApp link change history
- ✅ Member addition/removal tracking
- ✅ User action logging

## 🚀 Integration Examples

### **React Frontend Integration:**
```javascript
// Fetch groups
const fetchGroups = async () => {
  const response = await fetch('/api/groups', {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
  return response.json();
};

// Update WhatsApp link
const updateWhatsAppLink = async (groupId, link, expiry) => {
  const response = await fetch(`/api/groups/${groupId}/update-whatsapp-link`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      whatsapp_link: link,
      whatsapp_link_expiry: expiry
    })
  });
  return response.json();
};
```

## 📱 Mobile App Integration

The API is fully compatible with mobile applications and provides:
- ✅ **Deep linking** support for WhatsApp groups
- ✅ **Push notifications** for group updates
- ✅ **Offline caching** capabilities
- ✅ **Real-time synchronization**

## 🎯 Best Practices

1. **Link Management:**
   - Set expiry dates for WhatsApp links
   - Regularly update expired links
   - Monitor link usage and member activity

2. **Member Management:**
   - Verify student subscriptions before adding
   - Use bulk operations for efficiency
   - Maintain group size limits

3. **Security:**
   - Validate all WhatsApp links
   - Implement proper role-based access
   - Monitor group activities

4. **User Experience:**
   - Provide clear notifications
   - Show link expiry status
   - Enable easy group navigation
