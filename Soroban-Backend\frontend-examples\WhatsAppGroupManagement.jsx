import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  Card<PERSON><PERSON>er, 
  CardContent, 
  Button, 
  Input, 
  Badge, 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
  Alert,
  AlertDescription
} from '@/components/ui';
import { 
  Users, 
  MessageCircle, 
  Plus, 
  Settings, 
  Calendar,
  ExternalLink,
  Clock,
  UserPlus,
  UserMinus,
  History
} from 'lucide-react';

const WhatsAppGroupManagement = () => {
  const [groups, setGroups] = useState([]);
  const [selectedGroup, setSelectedGroup] = useState(null);
  const [loading, setLoading] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showMembersDialog, setShowMembersDialog] = useState(false);

  // Fetch groups
  const fetchGroups = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/groups', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });
      const data = await response.json();
      if (data.success) {
        setGroups(data.data.data);
      }
    } catch (error) {
      console.error('Error fetching groups:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchGroups();
  }, []);

  // Create new group
  const createGroup = async (groupData) => {
    try {
      const response = await fetch('/api/groups', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(groupData)
      });
      const data = await response.json();
      if (data.success) {
        setGroups([data.data, ...groups]);
        setShowCreateDialog(false);
      }
    } catch (error) {
      console.error('Error creating group:', error);
    }
  };

  // Update WhatsApp link
  const updateWhatsAppLink = async (groupId, whatsappLink, expiry) => {
    try {
      const response = await fetch(`/api/groups/${groupId}/update-whatsapp-link`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          whatsapp_link: whatsappLink,
          whatsapp_link_expiry: expiry
        })
      });
      const data = await response.json();
      if (data.success) {
        fetchGroups(); // Refresh groups
      }
    } catch (error) {
      console.error('Error updating WhatsApp link:', error);
    }
  };

  // Add member to group
  const addMember = async (groupId, userId) => {
    try {
      const response = await fetch(`/api/groups/${groupId}/add-member`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ user_id: userId })
      });
      const data = await response.json();
      if (data.success) {
        // Refresh group members
        fetchGroupMembers(groupId);
      }
    } catch (error) {
      console.error('Error adding member:', error);
    }
  };

  // Fetch group members
  const fetchGroupMembers = async (groupId) => {
    try {
      const response = await fetch(`/api/groups/${groupId}/members`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const data = await response.json();
      if (data.success) {
        setSelectedGroup(prev => ({
          ...prev,
          members: data.data.members
        }));
      }
    } catch (error) {
      console.error('Error fetching members:', error);
    }
  };

  const GroupCard = ({ group }) => (
    <Card className="mb-4 hover:shadow-lg transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="text-lg font-semibold">{group.name}</h3>
            <p className="text-sm text-gray-600">
              {group.level?.course?.name} - {group.level?.title}
            </p>
            <p className="text-sm text-gray-500">
              Teacher: {group.teacher?.first_name} {group.teacher?.second_name}
            </p>
          </div>
          <div className="flex flex-col items-end gap-2">
            <Badge variant={group.is_active ? "success" : "secondary"}>
              {group.is_active ? "Active" : "Inactive"}
            </Badge>
            {group.whatsapp_link && (
              <Badge variant={group.whatsapp_link_expired ? "destructive" : "default"}>
                <MessageCircle className="w-3 h-3 mr-1" />
                WhatsApp
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex justify-between items-center mb-3">
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <span className="flex items-center gap-1">
              <Users className="w-4 h-4" />
              {group.member_count} members
            </span>
            <span className="flex items-center gap-1">
              <Calendar className="w-4 h-4" />
              {new Date(group.start_date).toLocaleDateString()}
            </span>
          </div>
        </div>

        {group.description && (
          <p className="text-sm text-gray-700 mb-3">{group.description}</p>
        )}

        <div className="flex gap-2 flex-wrap">
          <Button
            size="sm"
            variant="outline"
            onClick={() => {
              setSelectedGroup(group);
              setShowMembersDialog(true);
            }}
          >
            <Users className="w-4 h-4 mr-1" />
            Manage Members
          </Button>

          {group.whatsapp_link && (
            <Button
              size="sm"
              variant="outline"
              onClick={() => window.open(group.whatsapp_link, '_blank')}
            >
              <ExternalLink className="w-4 h-4 mr-1" />
              Join WhatsApp
            </Button>
          )}

          <WhatsAppLinkDialog 
            group={group} 
            onUpdate={updateWhatsAppLink}
          />
        </div>

        {group.whatsapp_link_expired && (
          <Alert className="mt-3">
            <Clock className="h-4 w-4" />
            <AlertDescription>
              WhatsApp link has expired. Please update with a new link.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );

  const WhatsAppLinkDialog = ({ group, onUpdate }) => {
    const [whatsappLink, setWhatsappLink] = useState(group.whatsapp_link || '');
    const [expiry, setExpiry] = useState(group.whatsapp_link_expiry || '');
    const [open, setOpen] = useState(false);

    const handleSubmit = (e) => {
      e.preventDefault();
      onUpdate(group.id, whatsappLink, expiry);
      setOpen(false);
    };

    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          <Button size="sm" variant="outline">
            <Settings className="w-4 h-4 mr-1" />
            WhatsApp Link
          </Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Update WhatsApp Group Link</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">
                WhatsApp Group Link
              </label>
              <Input
                type="url"
                value={whatsappLink}
                onChange={(e) => setWhatsappLink(e.target.value)}
                placeholder="https://chat.whatsapp.com/..."
                required
              />
              <p className="text-xs text-gray-500 mt-1">
                Enter the WhatsApp group invitation link
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">
                Link Expiry Date (Optional)
              </label>
              <Input
                type="date"
                value={expiry}
                onChange={(e) => setExpiry(e.target.value)}
                min={new Date().toISOString().split('T')[0]}
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button type="button" variant="outline" onClick={() => setOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">
                Update Link
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    );
  };

  const CreateGroupDialog = () => {
    const [formData, setFormData] = useState({
      level_id: '',
      teacher_id: '',
      name: '',
      description: '',
      start_date: '',
      end_date: '',
      whatsapp_link: '',
      whatsapp_link_expiry: ''
    });

    const handleSubmit = (e) => {
      e.preventDefault();
      createGroup(formData);
    };

    return (
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Create New Group</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">Group Name</label>
              <Input
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Description</label>
              <Input
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Start Date</label>
              <Input
                type="date"
                value={formData.start_date}
                onChange={(e) => setFormData({...formData, start_date: e.target.value})}
                required
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button type="button" variant="outline" onClick={() => setShowCreateDialog(false)}>
                Cancel
              </Button>
              <Button type="submit">Create Group</Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    );
  };

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">WhatsApp Groups</h1>
          <p className="text-gray-600">Manage course groups and WhatsApp integration</p>
        </div>
        <Button onClick={() => setShowCreateDialog(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Create Group
        </Button>
      </div>

      <Tabs defaultValue="all" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="all">All Groups</TabsTrigger>
          <TabsTrigger value="active">Active Groups</TabsTrigger>
          <TabsTrigger value="my-groups">My Groups</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-6">
          {loading ? (
            <div className="text-center py-8">Loading groups...</div>
          ) : (
            <div className="grid gap-4">
              {groups.map(group => (
                <GroupCard key={group.id} group={group} />
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="active" className="mt-6">
          <div className="grid gap-4">
            {groups.filter(g => g.is_active).map(group => (
              <GroupCard key={group.id} group={group} />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="my-groups" className="mt-6">
          <div className="text-center py-8 text-gray-500">
            My Groups functionality will be implemented here
          </div>
        </TabsContent>
      </Tabs>

      <CreateGroupDialog />
    </div>
  );
};

export default WhatsAppGroupManagement;
