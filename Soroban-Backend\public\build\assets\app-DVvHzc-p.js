var F="top",K="bottom",q="right",j="left",Rn="auto",de=[F,K,q,j],Ut="start",re="end",co="clippingParents",ni="viewport",Zt="popper",lo="reference",Or=de.reduce(function(e,t){return e.concat([t+"-"+Ut,t+"-"+re])},[]),ri=[].concat(de,[Rn]).reduce(function(e,t){return e.concat([t,t+"-"+Ut,t+"-"+re])},[]),uo="beforeRead",fo="read",ho="afterRead",po="beforeMain",_o="main",mo="afterMain",go="beforeWrite",Eo="write",bo="afterWrite",vo=[uo,fo,ho,po,_o,mo,go,Eo,bo];function lt(e){return e?(e.nodeName||"").toLowerCase():null}function z(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function Wt(e){var t=z(e).Element;return e instanceof t||e instanceof Element}function G(e){var t=z(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function ii(e){if(typeof ShadowRoot>"u")return!1;var t=z(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}function Qc(e){var t=e.state;Object.keys(t.elements).forEach(function(n){var r=t.styles[n]||{},i=t.attributes[n]||{},s=t.elements[n];!G(s)||!lt(s)||(Object.assign(s.style,r),Object.keys(i).forEach(function(o){var a=i[o];a===!1?s.removeAttribute(o):s.setAttribute(o,a===!0?"":a)}))})}function Zc(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(r){var i=t.elements[r],s=t.attributes[r]||{},o=Object.keys(t.styles.hasOwnProperty(r)?t.styles[r]:n[r]),a=o.reduce(function(c,u){return c[u]="",c},{});!G(i)||!lt(i)||(Object.assign(i.style,a),Object.keys(s).forEach(function(c){i.removeAttribute(c)}))})}}const si={name:"applyStyles",enabled:!0,phase:"write",fn:Qc,effect:Zc,requires:["computeStyles"]};function at(e){return e.split("-")[0]}var kt=Math.max,yn=Math.min,ie=Math.round;function Cr(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function yo(){return!/^((?!chrome|android).)*safari/i.test(Cr())}function se(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!1);var r=e.getBoundingClientRect(),i=1,s=1;t&&G(e)&&(i=e.offsetWidth>0&&ie(r.width)/e.offsetWidth||1,s=e.offsetHeight>0&&ie(r.height)/e.offsetHeight||1);var o=Wt(e)?z(e):window,a=o.visualViewport,c=!yo()&&n,u=(r.left+(c&&a?a.offsetLeft:0))/i,l=(r.top+(c&&a?a.offsetTop:0))/s,h=r.width/i,_=r.height/s;return{width:h,height:_,top:l,right:u+h,bottom:l+_,left:u,x:u,y:l}}function oi(e){var t=se(e),n=e.offsetWidth,r=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-r)<=1&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function Ao(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&ii(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function dt(e){return z(e).getComputedStyle(e)}function tl(e){return["table","td","th"].indexOf(lt(e))>=0}function wt(e){return((Wt(e)?e.ownerDocument:e.document)||window.document).documentElement}function Ln(e){return lt(e)==="html"?e:e.assignedSlot||e.parentNode||(ii(e)?e.host:null)||wt(e)}function Zi(e){return!G(e)||dt(e).position==="fixed"?null:e.offsetParent}function el(e){var t=/firefox/i.test(Cr()),n=/Trident/i.test(Cr());if(n&&G(e)){var r=dt(e);if(r.position==="fixed")return null}var i=Ln(e);for(ii(i)&&(i=i.host);G(i)&&["html","body"].indexOf(lt(i))<0;){var s=dt(i);if(s.transform!=="none"||s.perspective!=="none"||s.contain==="paint"||["transform","perspective"].indexOf(s.willChange)!==-1||t&&s.willChange==="filter"||t&&s.filter&&s.filter!=="none")return i;i=i.parentNode}return null}function je(e){for(var t=z(e),n=Zi(e);n&&tl(n)&&dt(n).position==="static";)n=Zi(n);return n&&(lt(n)==="html"||lt(n)==="body"&&dt(n).position==="static")?t:n||el(e)||t}function ai(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Le(e,t,n){return kt(e,yn(t,n))}function nl(e,t,n){var r=Le(e,t,n);return r>n?n:r}function wo(){return{top:0,right:0,bottom:0,left:0}}function To(e){return Object.assign({},wo(),e)}function So(e,t){return t.reduce(function(n,r){return n[r]=e,n},{})}var rl=function(t,n){return t=typeof t=="function"?t(Object.assign({},n.rects,{placement:n.placement})):t,To(typeof t!="number"?t:So(t,de))};function il(e){var t,n=e.state,r=e.name,i=e.options,s=n.elements.arrow,o=n.modifiersData.popperOffsets,a=at(n.placement),c=ai(a),u=[j,q].indexOf(a)>=0,l=u?"height":"width";if(!(!s||!o)){var h=rl(i.padding,n),_=oi(s),E=c==="y"?F:j,m=c==="y"?K:q,g=n.rects.reference[l]+n.rects.reference[c]-o[c]-n.rects.popper[l],d=o[c]-n.rects.reference[c],b=je(s),v=b?c==="y"?b.clientHeight||0:b.clientWidth||0:0,A=g/2-d/2,y=h[E],T=v-_[l]-h[m],S=v/2-_[l]/2+A,C=Le(y,S,T),N=c;n.modifiersData[r]=(t={},t[N]=C,t.centerOffset=C-S,t)}}function sl(e){var t=e.state,n=e.options,r=n.element,i=r===void 0?"[data-popper-arrow]":r;i!=null&&(typeof i=="string"&&(i=t.elements.popper.querySelector(i),!i)||Ao(t.elements.popper,i)&&(t.elements.arrow=i))}const Oo={name:"arrow",enabled:!0,phase:"main",fn:il,effect:sl,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function oe(e){return e.split("-")[1]}var ol={top:"auto",right:"auto",bottom:"auto",left:"auto"};function al(e,t){var n=e.x,r=e.y,i=t.devicePixelRatio||1;return{x:ie(n*i)/i||0,y:ie(r*i)/i||0}}function ts(e){var t,n=e.popper,r=e.popperRect,i=e.placement,s=e.variation,o=e.offsets,a=e.position,c=e.gpuAcceleration,u=e.adaptive,l=e.roundOffsets,h=e.isFixed,_=o.x,E=_===void 0?0:_,m=o.y,g=m===void 0?0:m,d=typeof l=="function"?l({x:E,y:g}):{x:E,y:g};E=d.x,g=d.y;var b=o.hasOwnProperty("x"),v=o.hasOwnProperty("y"),A=j,y=F,T=window;if(u){var S=je(n),C="clientHeight",N="clientWidth";if(S===z(n)&&(S=wt(n),dt(S).position!=="static"&&a==="absolute"&&(C="scrollHeight",N="scrollWidth")),S=S,i===F||(i===j||i===q)&&s===re){y=K;var D=h&&S===T&&T.visualViewport?T.visualViewport.height:S[C];g-=D-r.height,g*=c?1:-1}if(i===j||(i===F||i===K)&&s===re){A=q;var L=h&&S===T&&T.visualViewport?T.visualViewport.width:S[N];E-=L-r.width,E*=c?1:-1}}var P=Object.assign({position:a},u&&ol),Z=l===!0?al({x:E,y:g},z(n)):{x:E,y:g};if(E=Z.x,g=Z.y,c){var k;return Object.assign({},P,(k={},k[y]=v?"0":"",k[A]=b?"0":"",k.transform=(T.devicePixelRatio||1)<=1?"translate("+E+"px, "+g+"px)":"translate3d("+E+"px, "+g+"px, 0)",k))}return Object.assign({},P,(t={},t[y]=v?g+"px":"",t[A]=b?E+"px":"",t.transform="",t))}function cl(e){var t=e.state,n=e.options,r=n.gpuAcceleration,i=r===void 0?!0:r,s=n.adaptive,o=s===void 0?!0:s,a=n.roundOffsets,c=a===void 0?!0:a,u={placement:at(t.placement),variation:oe(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:i,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,ts(Object.assign({},u,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:o,roundOffsets:c})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,ts(Object.assign({},u,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:c})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})}const ci={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:cl,data:{}};var tn={passive:!0};function ll(e){var t=e.state,n=e.instance,r=e.options,i=r.scroll,s=i===void 0?!0:i,o=r.resize,a=o===void 0?!0:o,c=z(t.elements.popper),u=[].concat(t.scrollParents.reference,t.scrollParents.popper);return s&&u.forEach(function(l){l.addEventListener("scroll",n.update,tn)}),a&&c.addEventListener("resize",n.update,tn),function(){s&&u.forEach(function(l){l.removeEventListener("scroll",n.update,tn)}),a&&c.removeEventListener("resize",n.update,tn)}}const li={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:ll,data:{}};var ul={left:"right",right:"left",bottom:"top",top:"bottom"};function hn(e){return e.replace(/left|right|bottom|top/g,function(t){return ul[t]})}var fl={start:"end",end:"start"};function es(e){return e.replace(/start|end/g,function(t){return fl[t]})}function ui(e){var t=z(e),n=t.pageXOffset,r=t.pageYOffset;return{scrollLeft:n,scrollTop:r}}function fi(e){return se(wt(e)).left+ui(e).scrollLeft}function dl(e,t){var n=z(e),r=wt(e),i=n.visualViewport,s=r.clientWidth,o=r.clientHeight,a=0,c=0;if(i){s=i.width,o=i.height;var u=yo();(u||!u&&t==="fixed")&&(a=i.offsetLeft,c=i.offsetTop)}return{width:s,height:o,x:a+fi(e),y:c}}function hl(e){var t,n=wt(e),r=ui(e),i=(t=e.ownerDocument)==null?void 0:t.body,s=kt(n.scrollWidth,n.clientWidth,i?i.scrollWidth:0,i?i.clientWidth:0),o=kt(n.scrollHeight,n.clientHeight,i?i.scrollHeight:0,i?i.clientHeight:0),a=-r.scrollLeft+fi(e),c=-r.scrollTop;return dt(i||n).direction==="rtl"&&(a+=kt(n.clientWidth,i?i.clientWidth:0)-s),{width:s,height:o,x:a,y:c}}function di(e){var t=dt(e),n=t.overflow,r=t.overflowX,i=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+i+r)}function Co(e){return["html","body","#document"].indexOf(lt(e))>=0?e.ownerDocument.body:G(e)&&di(e)?e:Co(Ln(e))}function $e(e,t){var n;t===void 0&&(t=[]);var r=Co(e),i=r===((n=e.ownerDocument)==null?void 0:n.body),s=z(r),o=i?[s].concat(s.visualViewport||[],di(r)?r:[]):r,a=t.concat(o);return i?a:a.concat($e(Ln(o)))}function xr(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function pl(e,t){var n=se(e,!1,t==="fixed");return n.top=n.top+e.clientTop,n.left=n.left+e.clientLeft,n.bottom=n.top+e.clientHeight,n.right=n.left+e.clientWidth,n.width=e.clientWidth,n.height=e.clientHeight,n.x=n.left,n.y=n.top,n}function ns(e,t,n){return t===ni?xr(dl(e,n)):Wt(t)?pl(t,n):xr(hl(wt(e)))}function _l(e){var t=$e(Ln(e)),n=["absolute","fixed"].indexOf(dt(e).position)>=0,r=n&&G(e)?je(e):e;return Wt(r)?t.filter(function(i){return Wt(i)&&Ao(i,r)&&lt(i)!=="body"}):[]}function ml(e,t,n,r){var i=t==="clippingParents"?_l(e):[].concat(t),s=[].concat(i,[n]),o=s[0],a=s.reduce(function(c,u){var l=ns(e,u,r);return c.top=kt(l.top,c.top),c.right=yn(l.right,c.right),c.bottom=yn(l.bottom,c.bottom),c.left=kt(l.left,c.left),c},ns(e,o,r));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}function xo(e){var t=e.reference,n=e.element,r=e.placement,i=r?at(r):null,s=r?oe(r):null,o=t.x+t.width/2-n.width/2,a=t.y+t.height/2-n.height/2,c;switch(i){case F:c={x:o,y:t.y-n.height};break;case K:c={x:o,y:t.y+t.height};break;case q:c={x:t.x+t.width,y:a};break;case j:c={x:t.x-n.width,y:a};break;default:c={x:t.x,y:t.y}}var u=i?ai(i):null;if(u!=null){var l=u==="y"?"height":"width";switch(s){case Ut:c[u]=c[u]-(t[l]/2-n[l]/2);break;case re:c[u]=c[u]+(t[l]/2-n[l]/2);break}}return c}function ae(e,t){t===void 0&&(t={});var n=t,r=n.placement,i=r===void 0?e.placement:r,s=n.strategy,o=s===void 0?e.strategy:s,a=n.boundary,c=a===void 0?co:a,u=n.rootBoundary,l=u===void 0?ni:u,h=n.elementContext,_=h===void 0?Zt:h,E=n.altBoundary,m=E===void 0?!1:E,g=n.padding,d=g===void 0?0:g,b=To(typeof d!="number"?d:So(d,de)),v=_===Zt?lo:Zt,A=e.rects.popper,y=e.elements[m?v:_],T=ml(Wt(y)?y:y.contextElement||wt(e.elements.popper),c,l,o),S=se(e.elements.reference),C=xo({reference:S,element:A,placement:i}),N=xr(Object.assign({},A,C)),D=_===Zt?N:S,L={top:T.top-D.top+b.top,bottom:D.bottom-T.bottom+b.bottom,left:T.left-D.left+b.left,right:D.right-T.right+b.right},P=e.modifiersData.offset;if(_===Zt&&P){var Z=P[i];Object.keys(L).forEach(function(k){var Ct=[q,K].indexOf(k)>=0?1:-1,xt=[F,K].indexOf(k)>=0?"y":"x";L[k]+=Z[xt]*Ct})}return L}function gl(e,t){t===void 0&&(t={});var n=t,r=n.placement,i=n.boundary,s=n.rootBoundary,o=n.padding,a=n.flipVariations,c=n.allowedAutoPlacements,u=c===void 0?ri:c,l=oe(r),h=l?a?Or:Or.filter(function(m){return oe(m)===l}):de,_=h.filter(function(m){return u.indexOf(m)>=0});_.length===0&&(_=h);var E=_.reduce(function(m,g){return m[g]=ae(e,{placement:g,boundary:i,rootBoundary:s,padding:o})[at(g)],m},{});return Object.keys(E).sort(function(m,g){return E[m]-E[g]})}function El(e){if(at(e)===Rn)return[];var t=hn(e);return[es(e),t,es(t)]}function bl(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var i=n.mainAxis,s=i===void 0?!0:i,o=n.altAxis,a=o===void 0?!0:o,c=n.fallbackPlacements,u=n.padding,l=n.boundary,h=n.rootBoundary,_=n.altBoundary,E=n.flipVariations,m=E===void 0?!0:E,g=n.allowedAutoPlacements,d=t.options.placement,b=at(d),v=b===d,A=c||(v||!m?[hn(d)]:El(d)),y=[d].concat(A).reduce(function(Xt,_t){return Xt.concat(at(_t)===Rn?gl(t,{placement:_t,boundary:l,rootBoundary:h,padding:u,flipVariations:m,allowedAutoPlacements:g}):_t)},[]),T=t.rects.reference,S=t.rects.popper,C=new Map,N=!0,D=y[0],L=0;L<y.length;L++){var P=y[L],Z=at(P),k=oe(P)===Ut,Ct=[F,K].indexOf(Z)>=0,xt=Ct?"width":"height",W=ae(t,{placement:P,boundary:l,rootBoundary:h,altBoundary:_,padding:u}),tt=Ct?k?q:j:k?K:F;T[xt]>S[xt]&&(tt=hn(tt));var Ge=hn(tt),Nt=[];if(s&&Nt.push(W[Z]<=0),a&&Nt.push(W[tt]<=0,W[Ge]<=0),Nt.every(function(Xt){return Xt})){D=P,N=!1;break}C.set(P,Nt)}if(N)for(var Xe=m?3:1,Zn=function(_t){var Te=y.find(function(Qe){var Dt=C.get(Qe);if(Dt)return Dt.slice(0,_t).every(function(tr){return tr})});if(Te)return D=Te,"break"},we=Xe;we>0;we--){var Je=Zn(we);if(Je==="break")break}t.placement!==D&&(t.modifiersData[r]._skip=!0,t.placement=D,t.reset=!0)}}const No={name:"flip",enabled:!0,phase:"main",fn:bl,requiresIfExists:["offset"],data:{_skip:!1}};function rs(e,t,n){return n===void 0&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function is(e){return[F,q,K,j].some(function(t){return e[t]>=0})}function vl(e){var t=e.state,n=e.name,r=t.rects.reference,i=t.rects.popper,s=t.modifiersData.preventOverflow,o=ae(t,{elementContext:"reference"}),a=ae(t,{altBoundary:!0}),c=rs(o,r),u=rs(a,i,s),l=is(c),h=is(u);t.modifiersData[n]={referenceClippingOffsets:c,popperEscapeOffsets:u,isReferenceHidden:l,hasPopperEscaped:h},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":l,"data-popper-escaped":h})}const Do={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:vl};function yl(e,t,n){var r=at(e),i=[j,F].indexOf(r)>=0?-1:1,s=typeof n=="function"?n(Object.assign({},t,{placement:e})):n,o=s[0],a=s[1];return o=o||0,a=(a||0)*i,[j,q].indexOf(r)>=0?{x:a,y:o}:{x:o,y:a}}function Al(e){var t=e.state,n=e.options,r=e.name,i=n.offset,s=i===void 0?[0,0]:i,o=ri.reduce(function(l,h){return l[h]=yl(h,t.rects,s),l},{}),a=o[t.placement],c=a.x,u=a.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=c,t.modifiersData.popperOffsets.y+=u),t.modifiersData[r]=o}const Ro={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:Al};function wl(e){var t=e.state,n=e.name;t.modifiersData[n]=xo({reference:t.rects.reference,element:t.rects.popper,placement:t.placement})}const hi={name:"popperOffsets",enabled:!0,phase:"read",fn:wl,data:{}};function Tl(e){return e==="x"?"y":"x"}function Sl(e){var t=e.state,n=e.options,r=e.name,i=n.mainAxis,s=i===void 0?!0:i,o=n.altAxis,a=o===void 0?!1:o,c=n.boundary,u=n.rootBoundary,l=n.altBoundary,h=n.padding,_=n.tether,E=_===void 0?!0:_,m=n.tetherOffset,g=m===void 0?0:m,d=ae(t,{boundary:c,rootBoundary:u,padding:h,altBoundary:l}),b=at(t.placement),v=oe(t.placement),A=!v,y=ai(b),T=Tl(y),S=t.modifiersData.popperOffsets,C=t.rects.reference,N=t.rects.popper,D=typeof g=="function"?g(Object.assign({},t.rects,{placement:t.placement})):g,L=typeof D=="number"?{mainAxis:D,altAxis:D}:Object.assign({mainAxis:0,altAxis:0},D),P=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,Z={x:0,y:0};if(S){if(s){var k,Ct=y==="y"?F:j,xt=y==="y"?K:q,W=y==="y"?"height":"width",tt=S[y],Ge=tt+d[Ct],Nt=tt-d[xt],Xe=E?-N[W]/2:0,Zn=v===Ut?C[W]:N[W],we=v===Ut?-N[W]:-C[W],Je=t.elements.arrow,Xt=E&&Je?oi(Je):{width:0,height:0},_t=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:wo(),Te=_t[Ct],Qe=_t[xt],Dt=Le(0,C[W],Xt[W]),tr=A?C[W]/2-Xe-Dt-Te-L.mainAxis:Zn-Dt-Te-L.mainAxis,qc=A?-C[W]/2+Xe+Dt+Qe+L.mainAxis:we+Dt+Qe+L.mainAxis,er=t.elements.arrow&&je(t.elements.arrow),zc=er?y==="y"?er.clientTop||0:er.clientLeft||0:0,Wi=(k=P==null?void 0:P[y])!=null?k:0,Yc=tt+tr-Wi-zc,Gc=tt+qc-Wi,Ki=Le(E?yn(Ge,Yc):Ge,tt,E?kt(Nt,Gc):Nt);S[y]=Ki,Z[y]=Ki-tt}if(a){var qi,Xc=y==="x"?F:j,Jc=y==="x"?K:q,Rt=S[T],Ze=T==="y"?"height":"width",zi=Rt+d[Xc],Yi=Rt-d[Jc],nr=[F,j].indexOf(b)!==-1,Gi=(qi=P==null?void 0:P[T])!=null?qi:0,Xi=nr?zi:Rt-C[Ze]-N[Ze]-Gi+L.altAxis,Ji=nr?Rt+C[Ze]+N[Ze]-Gi-L.altAxis:Yi,Qi=E&&nr?nl(Xi,Rt,Ji):Le(E?Xi:zi,Rt,E?Ji:Yi);S[T]=Qi,Z[T]=Qi-Rt}t.modifiersData[r]=Z}}const Lo={name:"preventOverflow",enabled:!0,phase:"main",fn:Sl,requiresIfExists:["offset"]};function Ol(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function Cl(e){return e===z(e)||!G(e)?ui(e):Ol(e)}function xl(e){var t=e.getBoundingClientRect(),n=ie(t.width)/e.offsetWidth||1,r=ie(t.height)/e.offsetHeight||1;return n!==1||r!==1}function Nl(e,t,n){n===void 0&&(n=!1);var r=G(t),i=G(t)&&xl(t),s=wt(t),o=se(e,i,n),a={scrollLeft:0,scrollTop:0},c={x:0,y:0};return(r||!r&&!n)&&((lt(t)!=="body"||di(s))&&(a=Cl(t)),G(t)?(c=se(t,!0),c.x+=t.clientLeft,c.y+=t.clientTop):s&&(c.x=fi(s))),{x:o.left+a.scrollLeft-c.x,y:o.top+a.scrollTop-c.y,width:o.width,height:o.height}}function Dl(e){var t=new Map,n=new Set,r=[];e.forEach(function(s){t.set(s.name,s)});function i(s){n.add(s.name);var o=[].concat(s.requires||[],s.requiresIfExists||[]);o.forEach(function(a){if(!n.has(a)){var c=t.get(a);c&&i(c)}}),r.push(s)}return e.forEach(function(s){n.has(s.name)||i(s)}),r}function Rl(e){var t=Dl(e);return vo.reduce(function(n,r){return n.concat(t.filter(function(i){return i.phase===r}))},[])}function Ll(e){var t;return function(){return t||(t=new Promise(function(n){Promise.resolve().then(function(){t=void 0,n(e())})})),t}}function $l(e){var t=e.reduce(function(n,r){var i=n[r.name];return n[r.name]=i?Object.assign({},i,r,{options:Object.assign({},i.options,r.options),data:Object.assign({},i.data,r.data)}):r,n},{});return Object.keys(t).map(function(n){return t[n]})}var ss={placement:"bottom",modifiers:[],strategy:"absolute"};function os(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(r){return!(r&&typeof r.getBoundingClientRect=="function")})}function $n(e){e===void 0&&(e={});var t=e,n=t.defaultModifiers,r=n===void 0?[]:n,i=t.defaultOptions,s=i===void 0?ss:i;return function(a,c,u){u===void 0&&(u=s);var l={placement:"bottom",orderedModifiers:[],options:Object.assign({},ss,s),modifiersData:{},elements:{reference:a,popper:c},attributes:{},styles:{}},h=[],_=!1,E={state:l,setOptions:function(b){var v=typeof b=="function"?b(l.options):b;g(),l.options=Object.assign({},s,l.options,v),l.scrollParents={reference:Wt(a)?$e(a):a.contextElement?$e(a.contextElement):[],popper:$e(c)};var A=Rl($l([].concat(r,l.options.modifiers)));return l.orderedModifiers=A.filter(function(y){return y.enabled}),m(),E.update()},forceUpdate:function(){if(!_){var b=l.elements,v=b.reference,A=b.popper;if(os(v,A)){l.rects={reference:Nl(v,je(A),l.options.strategy==="fixed"),popper:oi(A)},l.reset=!1,l.placement=l.options.placement,l.orderedModifiers.forEach(function(L){return l.modifiersData[L.name]=Object.assign({},L.data)});for(var y=0;y<l.orderedModifiers.length;y++){if(l.reset===!0){l.reset=!1,y=-1;continue}var T=l.orderedModifiers[y],S=T.fn,C=T.options,N=C===void 0?{}:C,D=T.name;typeof S=="function"&&(l=S({state:l,options:N,name:D,instance:E})||l)}}}},update:Ll(function(){return new Promise(function(d){E.forceUpdate(),d(l)})}),destroy:function(){g(),_=!0}};if(!os(a,c))return E;E.setOptions(u).then(function(d){!_&&u.onFirstUpdate&&u.onFirstUpdate(d)});function m(){l.orderedModifiers.forEach(function(d){var b=d.name,v=d.options,A=v===void 0?{}:v,y=d.effect;if(typeof y=="function"){var T=y({state:l,name:b,instance:E,options:A}),S=function(){};h.push(T||S)}})}function g(){h.forEach(function(d){return d()}),h=[]}return E}}var Il=$n(),Pl=[li,hi,ci,si],Ml=$n({defaultModifiers:Pl}),kl=[li,hi,ci,si,Ro,No,Lo,Oo,Do],pi=$n({defaultModifiers:kl});const $o=Object.freeze(Object.defineProperty({__proto__:null,afterMain:mo,afterRead:ho,afterWrite:bo,applyStyles:si,arrow:Oo,auto:Rn,basePlacements:de,beforeMain:po,beforeRead:uo,beforeWrite:go,bottom:K,clippingParents:co,computeStyles:ci,createPopper:pi,createPopperBase:Il,createPopperLite:Ml,detectOverflow:ae,end:re,eventListeners:li,flip:No,hide:Do,left:j,main:_o,modifierPhases:vo,offset:Ro,placements:ri,popper:Zt,popperGenerator:$n,popperOffsets:hi,preventOverflow:Lo,read:fo,reference:lo,right:q,start:Ut,top:F,variationPlacements:Or,viewport:ni,write:Eo},Symbol.toStringTag,{value:"Module"}));/*!
  * Bootstrap v5.3.6 (https://getbootstrap.com/)
  * Copyright 2011-2025 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
  */const mt=new Map,rr={set(e,t,n){mt.has(e)||mt.set(e,new Map);const r=mt.get(e);if(!r.has(t)&&r.size!==0){console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(r.keys())[0]}.`);return}r.set(t,n)},get(e,t){return mt.has(e)&&mt.get(e).get(t)||null},remove(e,t){if(!mt.has(e))return;const n=mt.get(e);n.delete(t),n.size===0&&mt.delete(e)}},Fl=1e6,jl=1e3,Nr="transitionend",Io=e=>(e&&window.CSS&&window.CSS.escape&&(e=e.replace(/#([^\s"#']+)/g,(t,n)=>`#${CSS.escape(n)}`)),e),Bl=e=>e==null?`${e}`:Object.prototype.toString.call(e).match(/\s([a-z]+)/i)[1].toLowerCase(),Hl=e=>{do e+=Math.floor(Math.random()*Fl);while(document.getElementById(e));return e},Vl=e=>{if(!e)return 0;let{transitionDuration:t,transitionDelay:n}=window.getComputedStyle(e);const r=Number.parseFloat(t),i=Number.parseFloat(n);return!r&&!i?0:(t=t.split(",")[0],n=n.split(",")[0],(Number.parseFloat(t)+Number.parseFloat(n))*jl)},Po=e=>{e.dispatchEvent(new Event(Nr))},ut=e=>!e||typeof e!="object"?!1:(typeof e.jquery<"u"&&(e=e[0]),typeof e.nodeType<"u"),Et=e=>ut(e)?e.jquery?e[0]:e:typeof e=="string"&&e.length>0?document.querySelector(Io(e)):null,he=e=>{if(!ut(e)||e.getClientRects().length===0)return!1;const t=getComputedStyle(e).getPropertyValue("visibility")==="visible",n=e.closest("details:not([open])");if(!n)return t;if(n!==e){const r=e.closest("summary");if(r&&r.parentNode!==n||r===null)return!1}return t},bt=e=>!e||e.nodeType!==Node.ELEMENT_NODE||e.classList.contains("disabled")?!0:typeof e.disabled<"u"?e.disabled:e.hasAttribute("disabled")&&e.getAttribute("disabled")!=="false",Mo=e=>{if(!document.documentElement.attachShadow)return null;if(typeof e.getRootNode=="function"){const t=e.getRootNode();return t instanceof ShadowRoot?t:null}return e instanceof ShadowRoot?e:e.parentNode?Mo(e.parentNode):null},An=()=>{},Be=e=>{e.offsetHeight},ko=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,ir=[],Ul=e=>{document.readyState==="loading"?(ir.length||document.addEventListener("DOMContentLoaded",()=>{for(const t of ir)t()}),ir.push(e)):e()},X=()=>document.documentElement.dir==="rtl",Q=e=>{Ul(()=>{const t=ko();if(t){const n=e.NAME,r=t.fn[n];t.fn[n]=e.jQueryInterface,t.fn[n].Constructor=e,t.fn[n].noConflict=()=>(t.fn[n]=r,e.jQueryInterface)}})},H=(e,t=[],n=e)=>typeof e=="function"?e.call(...t):n,Fo=(e,t,n=!0)=>{if(!n){H(e);return}const i=Vl(t)+5;let s=!1;const o=({target:a})=>{a===t&&(s=!0,t.removeEventListener(Nr,o),H(e))};t.addEventListener(Nr,o),setTimeout(()=>{s||Po(t)},i)},_i=(e,t,n,r)=>{const i=e.length;let s=e.indexOf(t);return s===-1?!n&&r?e[i-1]:e[0]:(s+=n?1:-1,r&&(s=(s+i)%i),e[Math.max(0,Math.min(s,i-1))])},Wl=/[^.]*(?=\..*)\.|.*/,Kl=/\..*/,ql=/::\d+$/,sr={};let as=1;const jo={mouseenter:"mouseover",mouseleave:"mouseout"},zl=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function Bo(e,t){return t&&`${t}::${as++}`||e.uidEvent||as++}function Ho(e){const t=Bo(e);return e.uidEvent=t,sr[t]=sr[t]||{},sr[t]}function Yl(e,t){return function n(r){return mi(r,{delegateTarget:e}),n.oneOff&&p.off(e,r.type,t),t.apply(e,[r])}}function Gl(e,t,n){return function r(i){const s=e.querySelectorAll(t);for(let{target:o}=i;o&&o!==this;o=o.parentNode)for(const a of s)if(a===o)return mi(i,{delegateTarget:o}),r.oneOff&&p.off(e,i.type,t,n),n.apply(o,[i])}}function Vo(e,t,n=null){return Object.values(e).find(r=>r.callable===t&&r.delegationSelector===n)}function Uo(e,t,n){const r=typeof t=="string",i=r?n:t||n;let s=Wo(e);return zl.has(s)||(s=e),[r,i,s]}function cs(e,t,n,r,i){if(typeof t!="string"||!e)return;let[s,o,a]=Uo(t,n,r);t in jo&&(o=(m=>function(g){if(!g.relatedTarget||g.relatedTarget!==g.delegateTarget&&!g.delegateTarget.contains(g.relatedTarget))return m.call(this,g)})(o));const c=Ho(e),u=c[a]||(c[a]={}),l=Vo(u,o,s?n:null);if(l){l.oneOff=l.oneOff&&i;return}const h=Bo(o,t.replace(Wl,"")),_=s?Gl(e,n,o):Yl(e,o);_.delegationSelector=s?n:null,_.callable=o,_.oneOff=i,_.uidEvent=h,u[h]=_,e.addEventListener(a,_,s)}function Dr(e,t,n,r,i){const s=Vo(t[n],r,i);s&&(e.removeEventListener(n,s,!!i),delete t[n][s.uidEvent])}function Xl(e,t,n,r){const i=t[n]||{};for(const[s,o]of Object.entries(i))s.includes(r)&&Dr(e,t,n,o.callable,o.delegationSelector)}function Wo(e){return e=e.replace(Kl,""),jo[e]||e}const p={on(e,t,n,r){cs(e,t,n,r,!1)},one(e,t,n,r){cs(e,t,n,r,!0)},off(e,t,n,r){if(typeof t!="string"||!e)return;const[i,s,o]=Uo(t,n,r),a=o!==t,c=Ho(e),u=c[o]||{},l=t.startsWith(".");if(typeof s<"u"){if(!Object.keys(u).length)return;Dr(e,c,o,s,i?n:null);return}if(l)for(const h of Object.keys(c))Xl(e,c,h,t.slice(1));for(const[h,_]of Object.entries(u)){const E=h.replace(ql,"");(!a||t.includes(E))&&Dr(e,c,o,_.callable,_.delegationSelector)}},trigger(e,t,n){if(typeof t!="string"||!e)return null;const r=ko(),i=Wo(t),s=t!==i;let o=null,a=!0,c=!0,u=!1;s&&r&&(o=r.Event(t,n),r(e).trigger(o),a=!o.isPropagationStopped(),c=!o.isImmediatePropagationStopped(),u=o.isDefaultPrevented());const l=mi(new Event(t,{bubbles:a,cancelable:!0}),n);return u&&l.preventDefault(),c&&e.dispatchEvent(l),l.defaultPrevented&&o&&o.preventDefault(),l}};function mi(e,t={}){for(const[n,r]of Object.entries(t))try{e[n]=r}catch{Object.defineProperty(e,n,{configurable:!0,get(){return r}})}return e}function ls(e){if(e==="true")return!0;if(e==="false")return!1;if(e===Number(e).toString())return Number(e);if(e===""||e==="null")return null;if(typeof e!="string")return e;try{return JSON.parse(decodeURIComponent(e))}catch{return e}}function or(e){return e.replace(/[A-Z]/g,t=>`-${t.toLowerCase()}`)}const ft={setDataAttribute(e,t,n){e.setAttribute(`data-bs-${or(t)}`,n)},removeDataAttribute(e,t){e.removeAttribute(`data-bs-${or(t)}`)},getDataAttributes(e){if(!e)return{};const t={},n=Object.keys(e.dataset).filter(r=>r.startsWith("bs")&&!r.startsWith("bsConfig"));for(const r of n){let i=r.replace(/^bs/,"");i=i.charAt(0).toLowerCase()+i.slice(1),t[i]=ls(e.dataset[r])}return t},getDataAttribute(e,t){return ls(e.getAttribute(`data-bs-${or(t)}`))}};class He{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(t){return t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t}_mergeConfigObj(t,n){const r=ut(n)?ft.getDataAttribute(n,"config"):{};return{...this.constructor.Default,...typeof r=="object"?r:{},...ut(n)?ft.getDataAttributes(n):{},...typeof t=="object"?t:{}}}_typeCheckConfig(t,n=this.constructor.DefaultType){for(const[r,i]of Object.entries(n)){const s=t[r],o=ut(s)?"element":Bl(s);if(!new RegExp(i).test(o))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${r}" provided type "${o}" but expected type "${i}".`)}}}const Jl="5.3.6";class nt extends He{constructor(t,n){super(),t=Et(t),t&&(this._element=t,this._config=this._getConfig(n),rr.set(this._element,this.constructor.DATA_KEY,this))}dispose(){rr.remove(this._element,this.constructor.DATA_KEY),p.off(this._element,this.constructor.EVENT_KEY);for(const t of Object.getOwnPropertyNames(this))this[t]=null}_queueCallback(t,n,r=!0){Fo(t,n,r)}_getConfig(t){return t=this._mergeConfigObj(t,this._element),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}static getInstance(t){return rr.get(Et(t),this.DATA_KEY)}static getOrCreateInstance(t,n={}){return this.getInstance(t)||new this(t,typeof n=="object"?n:null)}static get VERSION(){return Jl}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(t){return`${t}${this.EVENT_KEY}`}}const ar=e=>{let t=e.getAttribute("data-bs-target");if(!t||t==="#"){let n=e.getAttribute("href");if(!n||!n.includes("#")&&!n.startsWith("."))return null;n.includes("#")&&!n.startsWith("#")&&(n=`#${n.split("#")[1]}`),t=n&&n!=="#"?n.trim():null}return t?t.split(",").map(n=>Io(n)).join(","):null},w={find(e,t=document.documentElement){return[].concat(...Element.prototype.querySelectorAll.call(t,e))},findOne(e,t=document.documentElement){return Element.prototype.querySelector.call(t,e)},children(e,t){return[].concat(...e.children).filter(n=>n.matches(t))},parents(e,t){const n=[];let r=e.parentNode.closest(t);for(;r;)n.push(r),r=r.parentNode.closest(t);return n},prev(e,t){let n=e.previousElementSibling;for(;n;){if(n.matches(t))return[n];n=n.previousElementSibling}return[]},next(e,t){let n=e.nextElementSibling;for(;n;){if(n.matches(t))return[n];n=n.nextElementSibling}return[]},focusableChildren(e){const t=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map(n=>`${n}:not([tabindex^="-"])`).join(",");return this.find(t,e).filter(n=>!bt(n)&&he(n))},getSelectorFromElement(e){const t=ar(e);return t&&w.findOne(t)?t:null},getElementFromSelector(e){const t=ar(e);return t?w.findOne(t):null},getMultipleElementsFromSelector(e){const t=ar(e);return t?w.find(t):[]}},In=(e,t="hide")=>{const n=`click.dismiss${e.EVENT_KEY}`,r=e.NAME;p.on(document,n,`[data-bs-dismiss="${r}"]`,function(i){if(["A","AREA"].includes(this.tagName)&&i.preventDefault(),bt(this))return;const s=w.getElementFromSelector(this)||this.closest(`.${r}`);e.getOrCreateInstance(s)[t]()})},Ql="alert",Zl="bs.alert",Ko=`.${Zl}`,tu=`close${Ko}`,eu=`closed${Ko}`,nu="fade",ru="show";class Pn extends nt{static get NAME(){return Ql}close(){if(p.trigger(this._element,tu).defaultPrevented)return;this._element.classList.remove(ru);const n=this._element.classList.contains(nu);this._queueCallback(()=>this._destroyElement(),this._element,n)}_destroyElement(){this._element.remove(),p.trigger(this._element,eu),this.dispose()}static jQueryInterface(t){return this.each(function(){const n=Pn.getOrCreateInstance(this);if(typeof t=="string"){if(n[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);n[t](this)}})}}In(Pn,"close");Q(Pn);const iu="button",su="bs.button",ou=`.${su}`,au=".data-api",cu="active",us='[data-bs-toggle="button"]',lu=`click${ou}${au}`;class Mn extends nt{static get NAME(){return iu}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle(cu))}static jQueryInterface(t){return this.each(function(){const n=Mn.getOrCreateInstance(this);t==="toggle"&&n[t]()})}}p.on(document,lu,us,e=>{e.preventDefault();const t=e.target.closest(us);Mn.getOrCreateInstance(t).toggle()});Q(Mn);const uu="swipe",pe=".bs.swipe",fu=`touchstart${pe}`,du=`touchmove${pe}`,hu=`touchend${pe}`,pu=`pointerdown${pe}`,_u=`pointerup${pe}`,mu="touch",gu="pen",Eu="pointer-event",bu=40,vu={endCallback:null,leftCallback:null,rightCallback:null},yu={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class wn extends He{constructor(t,n){super(),this._element=t,!(!t||!wn.isSupported())&&(this._config=this._getConfig(n),this._deltaX=0,this._supportPointerEvents=!!window.PointerEvent,this._initEvents())}static get Default(){return vu}static get DefaultType(){return yu}static get NAME(){return uu}dispose(){p.off(this._element,pe)}_start(t){if(!this._supportPointerEvents){this._deltaX=t.touches[0].clientX;return}this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX)}_end(t){this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX-this._deltaX),this._handleSwipe(),H(this._config.endCallback)}_move(t){this._deltaX=t.touches&&t.touches.length>1?0:t.touches[0].clientX-this._deltaX}_handleSwipe(){const t=Math.abs(this._deltaX);if(t<=bu)return;const n=t/this._deltaX;this._deltaX=0,n&&H(n>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(p.on(this._element,pu,t=>this._start(t)),p.on(this._element,_u,t=>this._end(t)),this._element.classList.add(Eu)):(p.on(this._element,fu,t=>this._start(t)),p.on(this._element,du,t=>this._move(t)),p.on(this._element,hu,t=>this._end(t)))}_eventIsPointerPenTouch(t){return this._supportPointerEvents&&(t.pointerType===gu||t.pointerType===mu)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}const Au="carousel",wu="bs.carousel",Tt=`.${wu}`,qo=".data-api",Tu="ArrowLeft",Su="ArrowRight",Ou=500,Se="next",Jt="prev",te="left",pn="right",Cu=`slide${Tt}`,cr=`slid${Tt}`,xu=`keydown${Tt}`,Nu=`mouseenter${Tt}`,Du=`mouseleave${Tt}`,Ru=`dragstart${Tt}`,Lu=`load${Tt}${qo}`,$u=`click${Tt}${qo}`,zo="carousel",en="active",Iu="slide",Pu="carousel-item-end",Mu="carousel-item-start",ku="carousel-item-next",Fu="carousel-item-prev",Yo=".active",Go=".carousel-item",ju=Yo+Go,Bu=".carousel-item img",Hu=".carousel-indicators",Vu="[data-bs-slide], [data-bs-slide-to]",Uu='[data-bs-ride="carousel"]',Wu={[Tu]:pn,[Su]:te},Ku={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},qu={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class Ve extends nt{constructor(t,n){super(t,n),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=w.findOne(Hu,this._element),this._addEventListeners(),this._config.ride===zo&&this.cycle()}static get Default(){return Ku}static get DefaultType(){return qu}static get NAME(){return Au}next(){this._slide(Se)}nextWhenVisible(){!document.hidden&&he(this._element)&&this.next()}prev(){this._slide(Jt)}pause(){this._isSliding&&Po(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval(()=>this.nextWhenVisible(),this._config.interval)}_maybeEnableCycle(){if(this._config.ride){if(this._isSliding){p.one(this._element,cr,()=>this.cycle());return}this.cycle()}}to(t){const n=this._getItems();if(t>n.length-1||t<0)return;if(this._isSliding){p.one(this._element,cr,()=>this.to(t));return}const r=this._getItemIndex(this._getActive());if(r===t)return;const i=t>r?Se:Jt;this._slide(i,n[t])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(t){return t.defaultInterval=t.interval,t}_addEventListeners(){this._config.keyboard&&p.on(this._element,xu,t=>this._keydown(t)),this._config.pause==="hover"&&(p.on(this._element,Nu,()=>this.pause()),p.on(this._element,Du,()=>this._maybeEnableCycle())),this._config.touch&&wn.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const r of w.find(Bu,this._element))p.on(r,Ru,i=>i.preventDefault());const n={leftCallback:()=>this._slide(this._directionToOrder(te)),rightCallback:()=>this._slide(this._directionToOrder(pn)),endCallback:()=>{this._config.pause==="hover"&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout(()=>this._maybeEnableCycle(),Ou+this._config.interval))}};this._swipeHelper=new wn(this._element,n)}_keydown(t){if(/input|textarea/i.test(t.target.tagName))return;const n=Wu[t.key];n&&(t.preventDefault(),this._slide(this._directionToOrder(n)))}_getItemIndex(t){return this._getItems().indexOf(t)}_setActiveIndicatorElement(t){if(!this._indicatorsElement)return;const n=w.findOne(Yo,this._indicatorsElement);n.classList.remove(en),n.removeAttribute("aria-current");const r=w.findOne(`[data-bs-slide-to="${t}"]`,this._indicatorsElement);r&&(r.classList.add(en),r.setAttribute("aria-current","true"))}_updateInterval(){const t=this._activeElement||this._getActive();if(!t)return;const n=Number.parseInt(t.getAttribute("data-bs-interval"),10);this._config.interval=n||this._config.defaultInterval}_slide(t,n=null){if(this._isSliding)return;const r=this._getActive(),i=t===Se,s=n||_i(this._getItems(),r,i,this._config.wrap);if(s===r)return;const o=this._getItemIndex(s),a=E=>p.trigger(this._element,E,{relatedTarget:s,direction:this._orderToDirection(t),from:this._getItemIndex(r),to:o});if(a(Cu).defaultPrevented||!r||!s)return;const u=!!this._interval;this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(o),this._activeElement=s;const l=i?Mu:Pu,h=i?ku:Fu;s.classList.add(h),Be(s),r.classList.add(l),s.classList.add(l);const _=()=>{s.classList.remove(l,h),s.classList.add(en),r.classList.remove(en,h,l),this._isSliding=!1,a(cr)};this._queueCallback(_,r,this._isAnimated()),u&&this.cycle()}_isAnimated(){return this._element.classList.contains(Iu)}_getActive(){return w.findOne(ju,this._element)}_getItems(){return w.find(Go,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(t){return X()?t===te?Jt:Se:t===te?Se:Jt}_orderToDirection(t){return X()?t===Jt?te:pn:t===Jt?pn:te}static jQueryInterface(t){return this.each(function(){const n=Ve.getOrCreateInstance(this,t);if(typeof t=="number"){n.to(t);return}if(typeof t=="string"){if(n[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);n[t]()}})}}p.on(document,$u,Vu,function(e){const t=w.getElementFromSelector(this);if(!t||!t.classList.contains(zo))return;e.preventDefault();const n=Ve.getOrCreateInstance(t),r=this.getAttribute("data-bs-slide-to");if(r){n.to(r),n._maybeEnableCycle();return}if(ft.getDataAttribute(this,"slide")==="next"){n.next(),n._maybeEnableCycle();return}n.prev(),n._maybeEnableCycle()});p.on(window,Lu,()=>{const e=w.find(Uu);for(const t of e)Ve.getOrCreateInstance(t)});Q(Ve);const zu="collapse",Yu="bs.collapse",Ue=`.${Yu}`,Gu=".data-api",Xu=`show${Ue}`,Ju=`shown${Ue}`,Qu=`hide${Ue}`,Zu=`hidden${Ue}`,tf=`click${Ue}${Gu}`,lr="show",ne="collapse",nn="collapsing",ef="collapsed",nf=`:scope .${ne} .${ne}`,rf="collapse-horizontal",sf="width",of="height",af=".collapse.show, .collapse.collapsing",Rr='[data-bs-toggle="collapse"]',cf={parent:null,toggle:!0},lf={parent:"(null|element)",toggle:"boolean"};class Me extends nt{constructor(t,n){super(t,n),this._isTransitioning=!1,this._triggerArray=[];const r=w.find(Rr);for(const i of r){const s=w.getSelectorFromElement(i),o=w.find(s).filter(a=>a===this._element);s!==null&&o.length&&this._triggerArray.push(i)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return cf}static get DefaultType(){return lf}static get NAME(){return zu}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let t=[];if(this._config.parent&&(t=this._getFirstLevelChildren(af).filter(a=>a!==this._element).map(a=>Me.getOrCreateInstance(a,{toggle:!1}))),t.length&&t[0]._isTransitioning||p.trigger(this._element,Xu).defaultPrevented)return;for(const a of t)a.hide();const r=this._getDimension();this._element.classList.remove(ne),this._element.classList.add(nn),this._element.style[r]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const i=()=>{this._isTransitioning=!1,this._element.classList.remove(nn),this._element.classList.add(ne,lr),this._element.style[r]="",p.trigger(this._element,Ju)},o=`scroll${r[0].toUpperCase()+r.slice(1)}`;this._queueCallback(i,this._element,!0),this._element.style[r]=`${this._element[o]}px`}hide(){if(this._isTransitioning||!this._isShown()||p.trigger(this._element,Qu).defaultPrevented)return;const n=this._getDimension();this._element.style[n]=`${this._element.getBoundingClientRect()[n]}px`,Be(this._element),this._element.classList.add(nn),this._element.classList.remove(ne,lr);for(const i of this._triggerArray){const s=w.getElementFromSelector(i);s&&!this._isShown(s)&&this._addAriaAndCollapsedClass([i],!1)}this._isTransitioning=!0;const r=()=>{this._isTransitioning=!1,this._element.classList.remove(nn),this._element.classList.add(ne),p.trigger(this._element,Zu)};this._element.style[n]="",this._queueCallback(r,this._element,!0)}_isShown(t=this._element){return t.classList.contains(lr)}_configAfterMerge(t){return t.toggle=!!t.toggle,t.parent=Et(t.parent),t}_getDimension(){return this._element.classList.contains(rf)?sf:of}_initializeChildren(){if(!this._config.parent)return;const t=this._getFirstLevelChildren(Rr);for(const n of t){const r=w.getElementFromSelector(n);r&&this._addAriaAndCollapsedClass([n],this._isShown(r))}}_getFirstLevelChildren(t){const n=w.find(nf,this._config.parent);return w.find(t,this._config.parent).filter(r=>!n.includes(r))}_addAriaAndCollapsedClass(t,n){if(t.length)for(const r of t)r.classList.toggle(ef,!n),r.setAttribute("aria-expanded",n)}static jQueryInterface(t){const n={};return typeof t=="string"&&/show|hide/.test(t)&&(n.toggle=!1),this.each(function(){const r=Me.getOrCreateInstance(this,n);if(typeof t=="string"){if(typeof r[t]>"u")throw new TypeError(`No method named "${t}"`);r[t]()}})}}p.on(document,tf,Rr,function(e){(e.target.tagName==="A"||e.delegateTarget&&e.delegateTarget.tagName==="A")&&e.preventDefault();for(const t of w.getMultipleElementsFromSelector(this))Me.getOrCreateInstance(t,{toggle:!1}).toggle()});Q(Me);const fs="dropdown",uf="bs.dropdown",zt=`.${uf}`,gi=".data-api",ff="Escape",ds="Tab",df="ArrowUp",hs="ArrowDown",hf=2,pf=`hide${zt}`,_f=`hidden${zt}`,mf=`show${zt}`,gf=`shown${zt}`,Xo=`click${zt}${gi}`,Jo=`keydown${zt}${gi}`,Ef=`keyup${zt}${gi}`,ee="show",bf="dropup",vf="dropend",yf="dropstart",Af="dropup-center",wf="dropdown-center",$t='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',Tf=`${$t}.${ee}`,_n=".dropdown-menu",Sf=".navbar",Of=".navbar-nav",Cf=".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",xf=X()?"top-end":"top-start",Nf=X()?"top-start":"top-end",Df=X()?"bottom-end":"bottom-start",Rf=X()?"bottom-start":"bottom-end",Lf=X()?"left-start":"right-start",$f=X()?"right-start":"left-start",If="top",Pf="bottom",Mf={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},kf={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class ct extends nt{constructor(t,n){super(t,n),this._popper=null,this._parent=this._element.parentNode,this._menu=w.next(this._element,_n)[0]||w.prev(this._element,_n)[0]||w.findOne(_n,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return Mf}static get DefaultType(){return kf}static get NAME(){return fs}toggle(){return this._isShown()?this.hide():this.show()}show(){if(bt(this._element)||this._isShown())return;const t={relatedTarget:this._element};if(!p.trigger(this._element,mf,t).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(Of))for(const r of[].concat(...document.body.children))p.on(r,"mouseover",An);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(ee),this._element.classList.add(ee),p.trigger(this._element,gf,t)}}hide(){if(bt(this._element)||!this._isShown())return;const t={relatedTarget:this._element};this._completeHide(t)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(t){if(!p.trigger(this._element,pf,t).defaultPrevented){if("ontouchstart"in document.documentElement)for(const r of[].concat(...document.body.children))p.off(r,"mouseover",An);this._popper&&this._popper.destroy(),this._menu.classList.remove(ee),this._element.classList.remove(ee),this._element.setAttribute("aria-expanded","false"),ft.removeDataAttribute(this._menu,"popper"),p.trigger(this._element,_f,t),this._element.focus()}}_getConfig(t){if(t=super._getConfig(t),typeof t.reference=="object"&&!ut(t.reference)&&typeof t.reference.getBoundingClientRect!="function")throw new TypeError(`${fs.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return t}_createPopper(){if(typeof $o>"u")throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org/docs/v2/)");let t=this._element;this._config.reference==="parent"?t=this._parent:ut(this._config.reference)?t=Et(this._config.reference):typeof this._config.reference=="object"&&(t=this._config.reference);const n=this._getPopperConfig();this._popper=pi(t,this._menu,n)}_isShown(){return this._menu.classList.contains(ee)}_getPlacement(){const t=this._parent;if(t.classList.contains(vf))return Lf;if(t.classList.contains(yf))return $f;if(t.classList.contains(Af))return If;if(t.classList.contains(wf))return Pf;const n=getComputedStyle(this._menu).getPropertyValue("--bs-position").trim()==="end";return t.classList.contains(bf)?n?Nf:xf:n?Rf:Df}_detectNavbar(){return this._element.closest(Sf)!==null}_getOffset(){const{offset:t}=this._config;return typeof t=="string"?t.split(",").map(n=>Number.parseInt(n,10)):typeof t=="function"?n=>t(n,this._element):t}_getPopperConfig(){const t={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||this._config.display==="static")&&(ft.setDataAttribute(this._menu,"popper","static"),t.modifiers=[{name:"applyStyles",enabled:!1}]),{...t,...H(this._config.popperConfig,[void 0,t])}}_selectMenuItem({key:t,target:n}){const r=w.find(Cf,this._menu).filter(i=>he(i));r.length&&_i(r,n,t===hs,!r.includes(n)).focus()}static jQueryInterface(t){return this.each(function(){const n=ct.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof n[t]>"u")throw new TypeError(`No method named "${t}"`);n[t]()}})}static clearMenus(t){if(t.button===hf||t.type==="keyup"&&t.key!==ds)return;const n=w.find(Tf);for(const r of n){const i=ct.getInstance(r);if(!i||i._config.autoClose===!1)continue;const s=t.composedPath(),o=s.includes(i._menu);if(s.includes(i._element)||i._config.autoClose==="inside"&&!o||i._config.autoClose==="outside"&&o||i._menu.contains(t.target)&&(t.type==="keyup"&&t.key===ds||/input|select|option|textarea|form/i.test(t.target.tagName)))continue;const a={relatedTarget:i._element};t.type==="click"&&(a.clickEvent=t),i._completeHide(a)}}static dataApiKeydownHandler(t){const n=/input|textarea/i.test(t.target.tagName),r=t.key===ff,i=[df,hs].includes(t.key);if(!i&&!r||n&&!r)return;t.preventDefault();const s=this.matches($t)?this:w.prev(this,$t)[0]||w.next(this,$t)[0]||w.findOne($t,t.delegateTarget.parentNode),o=ct.getOrCreateInstance(s);if(i){t.stopPropagation(),o.show(),o._selectMenuItem(t);return}o._isShown()&&(t.stopPropagation(),o.hide(),s.focus())}}p.on(document,Jo,$t,ct.dataApiKeydownHandler);p.on(document,Jo,_n,ct.dataApiKeydownHandler);p.on(document,Xo,ct.clearMenus);p.on(document,Ef,ct.clearMenus);p.on(document,Xo,$t,function(e){e.preventDefault(),ct.getOrCreateInstance(this).toggle()});Q(ct);const Qo="backdrop",Ff="fade",ps="show",_s=`mousedown.bs.${Qo}`,jf={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},Bf={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class Zo extends He{constructor(t){super(),this._config=this._getConfig(t),this._isAppended=!1,this._element=null}static get Default(){return jf}static get DefaultType(){return Bf}static get NAME(){return Qo}show(t){if(!this._config.isVisible){H(t);return}this._append();const n=this._getElement();this._config.isAnimated&&Be(n),n.classList.add(ps),this._emulateAnimation(()=>{H(t)})}hide(t){if(!this._config.isVisible){H(t);return}this._getElement().classList.remove(ps),this._emulateAnimation(()=>{this.dispose(),H(t)})}dispose(){this._isAppended&&(p.off(this._element,_s),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const t=document.createElement("div");t.className=this._config.className,this._config.isAnimated&&t.classList.add(Ff),this._element=t}return this._element}_configAfterMerge(t){return t.rootElement=Et(t.rootElement),t}_append(){if(this._isAppended)return;const t=this._getElement();this._config.rootElement.append(t),p.on(t,_s,()=>{H(this._config.clickCallback)}),this._isAppended=!0}_emulateAnimation(t){Fo(t,this._getElement(),this._config.isAnimated)}}const Hf="focustrap",Vf="bs.focustrap",Tn=`.${Vf}`,Uf=`focusin${Tn}`,Wf=`keydown.tab${Tn}`,Kf="Tab",qf="forward",ms="backward",zf={autofocus:!0,trapElement:null},Yf={autofocus:"boolean",trapElement:"element"};class ta extends He{constructor(t){super(),this._config=this._getConfig(t),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return zf}static get DefaultType(){return Yf}static get NAME(){return Hf}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),p.off(document,Tn),p.on(document,Uf,t=>this._handleFocusin(t)),p.on(document,Wf,t=>this._handleKeydown(t)),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,p.off(document,Tn))}_handleFocusin(t){const{trapElement:n}=this._config;if(t.target===document||t.target===n||n.contains(t.target))return;const r=w.focusableChildren(n);r.length===0?n.focus():this._lastTabNavDirection===ms?r[r.length-1].focus():r[0].focus()}_handleKeydown(t){t.key===Kf&&(this._lastTabNavDirection=t.shiftKey?ms:qf)}}const gs=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",Es=".sticky-top",rn="padding-right",bs="margin-right";class Lr{constructor(){this._element=document.body}getWidth(){const t=document.documentElement.clientWidth;return Math.abs(window.innerWidth-t)}hide(){const t=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,rn,n=>n+t),this._setElementAttributes(gs,rn,n=>n+t),this._setElementAttributes(Es,bs,n=>n-t)}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,rn),this._resetElementAttributes(gs,rn),this._resetElementAttributes(Es,bs)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(t,n,r){const i=this.getWidth(),s=o=>{if(o!==this._element&&window.innerWidth>o.clientWidth+i)return;this._saveInitialAttribute(o,n);const a=window.getComputedStyle(o).getPropertyValue(n);o.style.setProperty(n,`${r(Number.parseFloat(a))}px`)};this._applyManipulationCallback(t,s)}_saveInitialAttribute(t,n){const r=t.style.getPropertyValue(n);r&&ft.setDataAttribute(t,n,r)}_resetElementAttributes(t,n){const r=i=>{const s=ft.getDataAttribute(i,n);if(s===null){i.style.removeProperty(n);return}ft.removeDataAttribute(i,n),i.style.setProperty(n,s)};this._applyManipulationCallback(t,r)}_applyManipulationCallback(t,n){if(ut(t)){n(t);return}for(const r of w.find(t,this._element))n(r)}}const Gf="modal",Xf="bs.modal",J=`.${Xf}`,Jf=".data-api",Qf="Escape",Zf=`hide${J}`,td=`hidePrevented${J}`,ea=`hidden${J}`,na=`show${J}`,ed=`shown${J}`,nd=`resize${J}`,rd=`click.dismiss${J}`,id=`mousedown.dismiss${J}`,sd=`keydown.dismiss${J}`,od=`click${J}${Jf}`,vs="modal-open",ad="fade",ys="show",ur="modal-static",cd=".modal.show",ld=".modal-dialog",ud=".modal-body",fd='[data-bs-toggle="modal"]',dd={backdrop:!0,focus:!0,keyboard:!0},hd={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class ce extends nt{constructor(t,n){super(t,n),this._dialog=w.findOne(ld,this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new Lr,this._addEventListeners()}static get Default(){return dd}static get DefaultType(){return hd}static get NAME(){return Gf}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||this._isTransitioning||p.trigger(this._element,na,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(vs),this._adjustDialog(),this._backdrop.show(()=>this._showElement(t)))}hide(){!this._isShown||this._isTransitioning||p.trigger(this._element,Zf).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(ys),this._queueCallback(()=>this._hideModal(),this._element,this._isAnimated()))}dispose(){p.off(window,J),p.off(this._dialog,J),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new Zo({isVisible:!!this._config.backdrop,isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new ta({trapElement:this._element})}_showElement(t){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const n=w.findOne(ud,this._dialog);n&&(n.scrollTop=0),Be(this._element),this._element.classList.add(ys);const r=()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,p.trigger(this._element,ed,{relatedTarget:t})};this._queueCallback(r,this._dialog,this._isAnimated())}_addEventListeners(){p.on(this._element,sd,t=>{if(t.key===Qf){if(this._config.keyboard){this.hide();return}this._triggerBackdropTransition()}}),p.on(window,nd,()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()}),p.on(this._element,id,t=>{p.one(this._element,rd,n=>{if(!(this._element!==t.target||this._element!==n.target)){if(this._config.backdrop==="static"){this._triggerBackdropTransition();return}this._config.backdrop&&this.hide()}})})}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide(()=>{document.body.classList.remove(vs),this._resetAdjustments(),this._scrollBar.reset(),p.trigger(this._element,ea)})}_isAnimated(){return this._element.classList.contains(ad)}_triggerBackdropTransition(){if(p.trigger(this._element,td).defaultPrevented)return;const n=this._element.scrollHeight>document.documentElement.clientHeight,r=this._element.style.overflowY;r==="hidden"||this._element.classList.contains(ur)||(n||(this._element.style.overflowY="hidden"),this._element.classList.add(ur),this._queueCallback(()=>{this._element.classList.remove(ur),this._queueCallback(()=>{this._element.style.overflowY=r},this._dialog)},this._dialog),this._element.focus())}_adjustDialog(){const t=this._element.scrollHeight>document.documentElement.clientHeight,n=this._scrollBar.getWidth(),r=n>0;if(r&&!t){const i=X()?"paddingLeft":"paddingRight";this._element.style[i]=`${n}px`}if(!r&&t){const i=X()?"paddingRight":"paddingLeft";this._element.style[i]=`${n}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(t,n){return this.each(function(){const r=ce.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof r[t]>"u")throw new TypeError(`No method named "${t}"`);r[t](n)}})}}p.on(document,od,fd,function(e){const t=w.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&e.preventDefault(),p.one(t,na,i=>{i.defaultPrevented||p.one(t,ea,()=>{he(this)&&this.focus()})});const n=w.findOne(cd);n&&ce.getInstance(n).hide(),ce.getOrCreateInstance(t).toggle(this)});In(ce);Q(ce);const pd="offcanvas",_d="bs.offcanvas",pt=`.${_d}`,ra=".data-api",md=`load${pt}${ra}`,gd="Escape",As="show",ws="showing",Ts="hiding",Ed="offcanvas-backdrop",ia=".offcanvas.show",bd=`show${pt}`,vd=`shown${pt}`,yd=`hide${pt}`,Ss=`hidePrevented${pt}`,sa=`hidden${pt}`,Ad=`resize${pt}`,wd=`click${pt}${ra}`,Td=`keydown.dismiss${pt}`,Sd='[data-bs-toggle="offcanvas"]',Od={backdrop:!0,keyboard:!0,scroll:!1},Cd={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class vt extends nt{constructor(t,n){super(t,n),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return Od}static get DefaultType(){return Cd}static get NAME(){return pd}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){if(this._isShown||p.trigger(this._element,bd,{relatedTarget:t}).defaultPrevented)return;this._isShown=!0,this._backdrop.show(),this._config.scroll||new Lr().hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(ws);const r=()=>{(!this._config.scroll||this._config.backdrop)&&this._focustrap.activate(),this._element.classList.add(As),this._element.classList.remove(ws),p.trigger(this._element,vd,{relatedTarget:t})};this._queueCallback(r,this._element,!0)}hide(){if(!this._isShown||p.trigger(this._element,yd).defaultPrevented)return;this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(Ts),this._backdrop.hide();const n=()=>{this._element.classList.remove(As,Ts),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||new Lr().reset(),p.trigger(this._element,sa)};this._queueCallback(n,this._element,!0)}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const t=()=>{if(this._config.backdrop==="static"){p.trigger(this._element,Ss);return}this.hide()},n=!!this._config.backdrop;return new Zo({className:Ed,isVisible:n,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:n?t:null})}_initializeFocusTrap(){return new ta({trapElement:this._element})}_addEventListeners(){p.on(this._element,Td,t=>{if(t.key===gd){if(this._config.keyboard){this.hide();return}p.trigger(this._element,Ss)}})}static jQueryInterface(t){return this.each(function(){const n=vt.getOrCreateInstance(this,t);if(typeof t=="string"){if(n[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);n[t](this)}})}}p.on(document,wd,Sd,function(e){const t=w.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&e.preventDefault(),bt(this))return;p.one(t,sa,()=>{he(this)&&this.focus()});const n=w.findOne(ia);n&&n!==t&&vt.getInstance(n).hide(),vt.getOrCreateInstance(t).toggle(this)});p.on(window,md,()=>{for(const e of w.find(ia))vt.getOrCreateInstance(e).show()});p.on(window,Ad,()=>{for(const e of w.find("[aria-modal][class*=show][class*=offcanvas-]"))getComputedStyle(e).position!=="fixed"&&vt.getOrCreateInstance(e).hide()});In(vt);Q(vt);const xd=/^aria-[\w-]*$/i,oa={"*":["class","dir","id","lang","role",xd],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},Nd=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),Dd=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,Rd=(e,t)=>{const n=e.nodeName.toLowerCase();return t.includes(n)?Nd.has(n)?!!Dd.test(e.nodeValue):!0:t.filter(r=>r instanceof RegExp).some(r=>r.test(n))};function Ld(e,t,n){if(!e.length)return e;if(n&&typeof n=="function")return n(e);const i=new window.DOMParser().parseFromString(e,"text/html"),s=[].concat(...i.body.querySelectorAll("*"));for(const o of s){const a=o.nodeName.toLowerCase();if(!Object.keys(t).includes(a)){o.remove();continue}const c=[].concat(...o.attributes),u=[].concat(t["*"]||[],t[a]||[]);for(const l of c)Rd(l,u)||o.removeAttribute(l.nodeName)}return i.body.innerHTML}const $d="TemplateFactory",Id={allowList:oa,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},Pd={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},Md={entry:"(string|element|function|null)",selector:"(string|element)"};class kd extends He{constructor(t){super(),this._config=this._getConfig(t)}static get Default(){return Id}static get DefaultType(){return Pd}static get NAME(){return $d}getContent(){return Object.values(this._config.content).map(t=>this._resolvePossibleFunction(t)).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(t){return this._checkContent(t),this._config.content={...this._config.content,...t},this}toHtml(){const t=document.createElement("div");t.innerHTML=this._maybeSanitize(this._config.template);for(const[i,s]of Object.entries(this._config.content))this._setContent(t,s,i);const n=t.children[0],r=this._resolvePossibleFunction(this._config.extraClass);return r&&n.classList.add(...r.split(" ")),n}_typeCheckConfig(t){super._typeCheckConfig(t),this._checkContent(t.content)}_checkContent(t){for(const[n,r]of Object.entries(t))super._typeCheckConfig({selector:n,entry:r},Md)}_setContent(t,n,r){const i=w.findOne(r,t);if(i){if(n=this._resolvePossibleFunction(n),!n){i.remove();return}if(ut(n)){this._putElementInTemplate(Et(n),i);return}if(this._config.html){i.innerHTML=this._maybeSanitize(n);return}i.textContent=n}}_maybeSanitize(t){return this._config.sanitize?Ld(t,this._config.allowList,this._config.sanitizeFn):t}_resolvePossibleFunction(t){return H(t,[void 0,this])}_putElementInTemplate(t,n){if(this._config.html){n.innerHTML="",n.append(t);return}n.textContent=t.textContent}}const Fd="tooltip",jd=new Set(["sanitize","allowList","sanitizeFn"]),fr="fade",Bd="modal",sn="show",Hd=".tooltip-inner",Os=`.${Bd}`,Cs="hide.bs.modal",Oe="hover",dr="focus",Vd="click",Ud="manual",Wd="hide",Kd="hidden",qd="show",zd="shown",Yd="inserted",Gd="click",Xd="focusin",Jd="focusout",Qd="mouseenter",Zd="mouseleave",th={AUTO:"auto",TOP:"top",RIGHT:X()?"left":"right",BOTTOM:"bottom",LEFT:X()?"right":"left"},eh={allowList:oa,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},nh={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class _e extends nt{constructor(t,n){if(typeof $o>"u")throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org/docs/v2/)");super(t,n),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return eh}static get DefaultType(){return nh}static get NAME(){return Fd}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){if(this._isEnabled){if(this._isShown()){this._leave();return}this._enter()}}dispose(){clearTimeout(this._timeout),p.off(this._element.closest(Os),Cs,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if(this._element.style.display==="none")throw new Error("Please use show on visible elements");if(!(this._isWithContent()&&this._isEnabled))return;const t=p.trigger(this._element,this.constructor.eventName(qd)),r=(Mo(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(t.defaultPrevented||!r)return;this._disposePopper();const i=this._getTipElement();this._element.setAttribute("aria-describedby",i.getAttribute("id"));const{container:s}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(s.append(i),p.trigger(this._element,this.constructor.eventName(Yd))),this._popper=this._createPopper(i),i.classList.add(sn),"ontouchstart"in document.documentElement)for(const a of[].concat(...document.body.children))p.on(a,"mouseover",An);const o=()=>{p.trigger(this._element,this.constructor.eventName(zd)),this._isHovered===!1&&this._leave(),this._isHovered=!1};this._queueCallback(o,this.tip,this._isAnimated())}hide(){if(!this._isShown()||p.trigger(this._element,this.constructor.eventName(Wd)).defaultPrevented)return;if(this._getTipElement().classList.remove(sn),"ontouchstart"in document.documentElement)for(const i of[].concat(...document.body.children))p.off(i,"mouseover",An);this._activeTrigger[Vd]=!1,this._activeTrigger[dr]=!1,this._activeTrigger[Oe]=!1,this._isHovered=null;const r=()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),p.trigger(this._element,this.constructor.eventName(Kd)))};this._queueCallback(r,this.tip,this._isAnimated())}update(){this._popper&&this._popper.update()}_isWithContent(){return!!this._getTitle()}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(t){const n=this._getTemplateFactory(t).toHtml();if(!n)return null;n.classList.remove(fr,sn),n.classList.add(`bs-${this.constructor.NAME}-auto`);const r=Hl(this.constructor.NAME).toString();return n.setAttribute("id",r),this._isAnimated()&&n.classList.add(fr),n}setContent(t){this._newContent=t,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(t){return this._templateFactory?this._templateFactory.changeContent(t):this._templateFactory=new kd({...this._config,content:t,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{[Hd]:this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(t){return this.constructor.getOrCreateInstance(t.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(fr)}_isShown(){return this.tip&&this.tip.classList.contains(sn)}_createPopper(t){const n=H(this._config.placement,[this,t,this._element]),r=th[n.toUpperCase()];return pi(this._element,t,this._getPopperConfig(r))}_getOffset(){const{offset:t}=this._config;return typeof t=="string"?t.split(",").map(n=>Number.parseInt(n,10)):typeof t=="function"?n=>t(n,this._element):t}_resolvePossibleFunction(t){return H(t,[this._element,this._element])}_getPopperConfig(t){const n={placement:t,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:r=>{this._getTipElement().setAttribute("data-popper-placement",r.state.placement)}}]};return{...n,...H(this._config.popperConfig,[void 0,n])}}_setListeners(){const t=this._config.trigger.split(" ");for(const n of t)if(n==="click")p.on(this._element,this.constructor.eventName(Gd),this._config.selector,r=>{this._initializeOnDelegatedTarget(r).toggle()});else if(n!==Ud){const r=n===Oe?this.constructor.eventName(Qd):this.constructor.eventName(Xd),i=n===Oe?this.constructor.eventName(Zd):this.constructor.eventName(Jd);p.on(this._element,r,this._config.selector,s=>{const o=this._initializeOnDelegatedTarget(s);o._activeTrigger[s.type==="focusin"?dr:Oe]=!0,o._enter()}),p.on(this._element,i,this._config.selector,s=>{const o=this._initializeOnDelegatedTarget(s);o._activeTrigger[s.type==="focusout"?dr:Oe]=o._element.contains(s.relatedTarget),o._leave()})}this._hideModalHandler=()=>{this._element&&this.hide()},p.on(this._element.closest(Os),Cs,this._hideModalHandler)}_fixTitle(){const t=this._element.getAttribute("title");t&&(!this._element.getAttribute("aria-label")&&!this._element.textContent.trim()&&this._element.setAttribute("aria-label",t),this._element.setAttribute("data-bs-original-title",t),this._element.removeAttribute("title"))}_enter(){if(this._isShown()||this._isHovered){this._isHovered=!0;return}this._isHovered=!0,this._setTimeout(()=>{this._isHovered&&this.show()},this._config.delay.show)}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout(()=>{this._isHovered||this.hide()},this._config.delay.hide))}_setTimeout(t,n){clearTimeout(this._timeout),this._timeout=setTimeout(t,n)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(t){const n=ft.getDataAttributes(this._element);for(const r of Object.keys(n))jd.has(r)&&delete n[r];return t={...n,...typeof t=="object"&&t?t:{}},t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t.container=t.container===!1?document.body:Et(t.container),typeof t.delay=="number"&&(t.delay={show:t.delay,hide:t.delay}),typeof t.title=="number"&&(t.title=t.title.toString()),typeof t.content=="number"&&(t.content=t.content.toString()),t}_getDelegateConfig(){const t={};for(const[n,r]of Object.entries(this._config))this.constructor.Default[n]!==r&&(t[n]=r);return t.selector=!1,t.trigger="manual",t}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(t){return this.each(function(){const n=_e.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof n[t]>"u")throw new TypeError(`No method named "${t}"`);n[t]()}})}}Q(_e);const rh="popover",ih=".popover-header",sh=".popover-body",oh={..._e.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},ah={..._e.DefaultType,content:"(null|string|element|function)"};class Ei extends _e{static get Default(){return oh}static get DefaultType(){return ah}static get NAME(){return rh}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{[ih]:this._getTitle(),[sh]:this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(t){return this.each(function(){const n=Ei.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof n[t]>"u")throw new TypeError(`No method named "${t}"`);n[t]()}})}}Q(Ei);const ch="scrollspy",lh="bs.scrollspy",bi=`.${lh}`,uh=".data-api",fh=`activate${bi}`,xs=`click${bi}`,dh=`load${bi}${uh}`,hh="dropdown-item",Qt="active",ph='[data-bs-spy="scroll"]',hr="[href]",_h=".nav, .list-group",Ns=".nav-link",mh=".nav-item",gh=".list-group-item",Eh=`${Ns}, ${mh} > ${Ns}, ${gh}`,bh=".dropdown",vh=".dropdown-toggle",yh={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},Ah={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class kn extends nt{constructor(t,n){super(t,n),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement=getComputedStyle(this._element).overflowY==="visible"?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return yh}static get DefaultType(){return Ah}static get NAME(){return ch}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const t of this._observableSections.values())this._observer.observe(t)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(t){return t.target=Et(t.target)||document.body,t.rootMargin=t.offset?`${t.offset}px 0px -30%`:t.rootMargin,typeof t.threshold=="string"&&(t.threshold=t.threshold.split(",").map(n=>Number.parseFloat(n))),t}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(p.off(this._config.target,xs),p.on(this._config.target,xs,hr,t=>{const n=this._observableSections.get(t.target.hash);if(n){t.preventDefault();const r=this._rootElement||window,i=n.offsetTop-this._element.offsetTop;if(r.scrollTo){r.scrollTo({top:i,behavior:"smooth"});return}r.scrollTop=i}}))}_getNewObserver(){const t={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver(n=>this._observerCallback(n),t)}_observerCallback(t){const n=o=>this._targetLinks.get(`#${o.target.id}`),r=o=>{this._previousScrollData.visibleEntryTop=o.target.offsetTop,this._process(n(o))},i=(this._rootElement||document.documentElement).scrollTop,s=i>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=i;for(const o of t){if(!o.isIntersecting){this._activeTarget=null,this._clearActiveClass(n(o));continue}const a=o.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(s&&a){if(r(o),!i)return;continue}!s&&!a&&r(o)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const t=w.find(hr,this._config.target);for(const n of t){if(!n.hash||bt(n))continue;const r=w.findOne(decodeURI(n.hash),this._element);he(r)&&(this._targetLinks.set(decodeURI(n.hash),n),this._observableSections.set(n.hash,r))}}_process(t){this._activeTarget!==t&&(this._clearActiveClass(this._config.target),this._activeTarget=t,t.classList.add(Qt),this._activateParents(t),p.trigger(this._element,fh,{relatedTarget:t}))}_activateParents(t){if(t.classList.contains(hh)){w.findOne(vh,t.closest(bh)).classList.add(Qt);return}for(const n of w.parents(t,_h))for(const r of w.prev(n,Eh))r.classList.add(Qt)}_clearActiveClass(t){t.classList.remove(Qt);const n=w.find(`${hr}.${Qt}`,t);for(const r of n)r.classList.remove(Qt)}static jQueryInterface(t){return this.each(function(){const n=kn.getOrCreateInstance(this,t);if(typeof t=="string"){if(n[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);n[t]()}})}}p.on(window,dh,()=>{for(const e of w.find(ph))kn.getOrCreateInstance(e)});Q(kn);const wh="tab",Th="bs.tab",Yt=`.${Th}`,Sh=`hide${Yt}`,Oh=`hidden${Yt}`,Ch=`show${Yt}`,xh=`shown${Yt}`,Nh=`click${Yt}`,Dh=`keydown${Yt}`,Rh=`load${Yt}`,Lh="ArrowLeft",Ds="ArrowRight",$h="ArrowUp",Rs="ArrowDown",pr="Home",Ls="End",It="active",$s="fade",_r="show",Ih="dropdown",aa=".dropdown-toggle",Ph=".dropdown-menu",mr=`:not(${aa})`,Mh='.list-group, .nav, [role="tablist"]',kh=".nav-item, .list-group-item",Fh=`.nav-link${mr}, .list-group-item${mr}, [role="tab"]${mr}`,ca='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',gr=`${Fh}, ${ca}`,jh=`.${It}[data-bs-toggle="tab"], .${It}[data-bs-toggle="pill"], .${It}[data-bs-toggle="list"]`;class le extends nt{constructor(t){super(t),this._parent=this._element.closest(Mh),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),p.on(this._element,Dh,n=>this._keydown(n)))}static get NAME(){return wh}show(){const t=this._element;if(this._elemIsActive(t))return;const n=this._getActiveElem(),r=n?p.trigger(n,Sh,{relatedTarget:t}):null;p.trigger(t,Ch,{relatedTarget:n}).defaultPrevented||r&&r.defaultPrevented||(this._deactivate(n,t),this._activate(t,n))}_activate(t,n){if(!t)return;t.classList.add(It),this._activate(w.getElementFromSelector(t));const r=()=>{if(t.getAttribute("role")!=="tab"){t.classList.add(_r);return}t.removeAttribute("tabindex"),t.setAttribute("aria-selected",!0),this._toggleDropDown(t,!0),p.trigger(t,xh,{relatedTarget:n})};this._queueCallback(r,t,t.classList.contains($s))}_deactivate(t,n){if(!t)return;t.classList.remove(It),t.blur(),this._deactivate(w.getElementFromSelector(t));const r=()=>{if(t.getAttribute("role")!=="tab"){t.classList.remove(_r);return}t.setAttribute("aria-selected",!1),t.setAttribute("tabindex","-1"),this._toggleDropDown(t,!1),p.trigger(t,Oh,{relatedTarget:n})};this._queueCallback(r,t,t.classList.contains($s))}_keydown(t){if(![Lh,Ds,$h,Rs,pr,Ls].includes(t.key))return;t.stopPropagation(),t.preventDefault();const n=this._getChildren().filter(i=>!bt(i));let r;if([pr,Ls].includes(t.key))r=n[t.key===pr?0:n.length-1];else{const i=[Ds,Rs].includes(t.key);r=_i(n,t.target,i,!0)}r&&(r.focus({preventScroll:!0}),le.getOrCreateInstance(r).show())}_getChildren(){return w.find(gr,this._parent)}_getActiveElem(){return this._getChildren().find(t=>this._elemIsActive(t))||null}_setInitialAttributes(t,n){this._setAttributeIfNotExists(t,"role","tablist");for(const r of n)this._setInitialAttributesOnChild(r)}_setInitialAttributesOnChild(t){t=this._getInnerElement(t);const n=this._elemIsActive(t),r=this._getOuterElement(t);t.setAttribute("aria-selected",n),r!==t&&this._setAttributeIfNotExists(r,"role","presentation"),n||t.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(t,"role","tab"),this._setInitialAttributesOnTargetPanel(t)}_setInitialAttributesOnTargetPanel(t){const n=w.getElementFromSelector(t);n&&(this._setAttributeIfNotExists(n,"role","tabpanel"),t.id&&this._setAttributeIfNotExists(n,"aria-labelledby",`${t.id}`))}_toggleDropDown(t,n){const r=this._getOuterElement(t);if(!r.classList.contains(Ih))return;const i=(s,o)=>{const a=w.findOne(s,r);a&&a.classList.toggle(o,n)};i(aa,It),i(Ph,_r),r.setAttribute("aria-expanded",n)}_setAttributeIfNotExists(t,n,r){t.hasAttribute(n)||t.setAttribute(n,r)}_elemIsActive(t){return t.classList.contains(It)}_getInnerElement(t){return t.matches(gr)?t:w.findOne(gr,t)}_getOuterElement(t){return t.closest(kh)||t}static jQueryInterface(t){return this.each(function(){const n=le.getOrCreateInstance(this);if(typeof t=="string"){if(n[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);n[t]()}})}}p.on(document,Nh,ca,function(e){["A","AREA"].includes(this.tagName)&&e.preventDefault(),!bt(this)&&le.getOrCreateInstance(this).show()});p.on(window,Rh,()=>{for(const e of w.find(jh))le.getOrCreateInstance(e)});Q(le);const Bh="toast",Hh="bs.toast",St=`.${Hh}`,Vh=`mouseover${St}`,Uh=`mouseout${St}`,Wh=`focusin${St}`,Kh=`focusout${St}`,qh=`hide${St}`,zh=`hidden${St}`,Yh=`show${St}`,Gh=`shown${St}`,Xh="fade",Is="hide",on="show",an="showing",Jh={animation:"boolean",autohide:"boolean",delay:"number"},Qh={animation:!0,autohide:!0,delay:5e3};class Fn extends nt{constructor(t,n){super(t,n),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return Qh}static get DefaultType(){return Jh}static get NAME(){return Bh}show(){if(p.trigger(this._element,Yh).defaultPrevented)return;this._clearTimeout(),this._config.animation&&this._element.classList.add(Xh);const n=()=>{this._element.classList.remove(an),p.trigger(this._element,Gh),this._maybeScheduleHide()};this._element.classList.remove(Is),Be(this._element),this._element.classList.add(on,an),this._queueCallback(n,this._element,this._config.animation)}hide(){if(!this.isShown()||p.trigger(this._element,qh).defaultPrevented)return;const n=()=>{this._element.classList.add(Is),this._element.classList.remove(an,on),p.trigger(this._element,zh)};this._element.classList.add(an),this._queueCallback(n,this._element,this._config.animation)}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(on),super.dispose()}isShown(){return this._element.classList.contains(on)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout(()=>{this.hide()},this._config.delay)))}_onInteraction(t,n){switch(t.type){case"mouseover":case"mouseout":{this._hasMouseInteraction=n;break}case"focusin":case"focusout":{this._hasKeyboardInteraction=n;break}}if(n){this._clearTimeout();return}const r=t.relatedTarget;this._element===r||this._element.contains(r)||this._maybeScheduleHide()}_setListeners(){p.on(this._element,Vh,t=>this._onInteraction(t,!0)),p.on(this._element,Uh,t=>this._onInteraction(t,!1)),p.on(this._element,Wh,t=>this._onInteraction(t,!0)),p.on(this._element,Kh,t=>this._onInteraction(t,!1))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(t){return this.each(function(){const n=Fn.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof n[t]>"u")throw new TypeError(`No method named "${t}"`);n[t](this)}})}}In(Fn);Q(Fn);function la(e,t){return function(){return e.apply(t,arguments)}}const{toString:Zh}=Object.prototype,{getPrototypeOf:vi}=Object,{iterator:jn,toStringTag:ua}=Symbol,Bn=(e=>t=>{const n=Zh.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),rt=e=>(e=e.toLowerCase(),t=>Bn(t)===e),Hn=e=>t=>typeof t===e,{isArray:me}=Array,ke=Hn("undefined");function tp(e){return e!==null&&!ke(e)&&e.constructor!==null&&!ke(e.constructor)&&V(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const fa=rt("ArrayBuffer");function ep(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&fa(e.buffer),t}const np=Hn("string"),V=Hn("function"),da=Hn("number"),Vn=e=>e!==null&&typeof e=="object",rp=e=>e===!0||e===!1,mn=e=>{if(Bn(e)!=="object")return!1;const t=vi(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(ua in e)&&!(jn in e)},ip=rt("Date"),sp=rt("File"),op=rt("Blob"),ap=rt("FileList"),cp=e=>Vn(e)&&V(e.pipe),lp=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||V(e.append)&&((t=Bn(e))==="formdata"||t==="object"&&V(e.toString)&&e.toString()==="[object FormData]"))},up=rt("URLSearchParams"),[fp,dp,hp,pp]=["ReadableStream","Request","Response","Headers"].map(rt),_p=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function We(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,i;if(typeof e!="object"&&(e=[e]),me(e))for(r=0,i=e.length;r<i;r++)t.call(null,e[r],r,e);else{const s=n?Object.getOwnPropertyNames(e):Object.keys(e),o=s.length;let a;for(r=0;r<o;r++)a=s[r],t.call(null,e[a],a,e)}}function ha(e,t){t=t.toLowerCase();const n=Object.keys(e);let r=n.length,i;for(;r-- >0;)if(i=n[r],t===i.toLowerCase())return i;return null}const Pt=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,pa=e=>!ke(e)&&e!==Pt;function $r(){const{caseless:e}=pa(this)&&this||{},t={},n=(r,i)=>{const s=e&&ha(t,i)||i;mn(t[s])&&mn(r)?t[s]=$r(t[s],r):mn(r)?t[s]=$r({},r):me(r)?t[s]=r.slice():t[s]=r};for(let r=0,i=arguments.length;r<i;r++)arguments[r]&&We(arguments[r],n);return t}const mp=(e,t,n,{allOwnKeys:r}={})=>(We(t,(i,s)=>{n&&V(i)?e[s]=la(i,n):e[s]=i},{allOwnKeys:r}),e),gp=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Ep=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},bp=(e,t,n,r)=>{let i,s,o;const a={};if(t=t||{},e==null)return t;do{for(i=Object.getOwnPropertyNames(e),s=i.length;s-- >0;)o=i[s],(!r||r(o,e,t))&&!a[o]&&(t[o]=e[o],a[o]=!0);e=n!==!1&&vi(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},vp=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},yp=e=>{if(!e)return null;if(me(e))return e;let t=e.length;if(!da(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},Ap=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&vi(Uint8Array)),wp=(e,t)=>{const r=(e&&e[jn]).call(e);let i;for(;(i=r.next())&&!i.done;){const s=i.value;t.call(e,s[0],s[1])}},Tp=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},Sp=rt("HTMLFormElement"),Op=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,i){return r.toUpperCase()+i}),Ps=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Cp=rt("RegExp"),_a=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};We(n,(i,s)=>{let o;(o=t(i,s,e))!==!1&&(r[s]=o||i)}),Object.defineProperties(e,r)},xp=e=>{_a(e,(t,n)=>{if(V(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(V(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},Np=(e,t)=>{const n={},r=i=>{i.forEach(s=>{n[s]=!0})};return me(e)?r(e):r(String(e).split(t)),n},Dp=()=>{},Rp=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function Lp(e){return!!(e&&V(e.append)&&e[ua]==="FormData"&&e[jn])}const $p=e=>{const t=new Array(10),n=(r,i)=>{if(Vn(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[i]=r;const s=me(r)?[]:{};return We(r,(o,a)=>{const c=n(o,i+1);!ke(c)&&(s[a]=c)}),t[i]=void 0,s}}return r};return n(e,0)},Ip=rt("AsyncFunction"),Pp=e=>e&&(Vn(e)||V(e))&&V(e.then)&&V(e.catch),ma=((e,t)=>e?setImmediate:t?((n,r)=>(Pt.addEventListener("message",({source:i,data:s})=>{i===Pt&&s===n&&r.length&&r.shift()()},!1),i=>{r.push(i),Pt.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",V(Pt.postMessage)),Mp=typeof queueMicrotask<"u"?queueMicrotask.bind(Pt):typeof process<"u"&&process.nextTick||ma,kp=e=>e!=null&&V(e[jn]),f={isArray:me,isArrayBuffer:fa,isBuffer:tp,isFormData:lp,isArrayBufferView:ep,isString:np,isNumber:da,isBoolean:rp,isObject:Vn,isPlainObject:mn,isReadableStream:fp,isRequest:dp,isResponse:hp,isHeaders:pp,isUndefined:ke,isDate:ip,isFile:sp,isBlob:op,isRegExp:Cp,isFunction:V,isStream:cp,isURLSearchParams:up,isTypedArray:Ap,isFileList:ap,forEach:We,merge:$r,extend:mp,trim:_p,stripBOM:gp,inherits:Ep,toFlatObject:bp,kindOf:Bn,kindOfTest:rt,endsWith:vp,toArray:yp,forEachEntry:wp,matchAll:Tp,isHTMLForm:Sp,hasOwnProperty:Ps,hasOwnProp:Ps,reduceDescriptors:_a,freezeMethods:xp,toObjectSet:Np,toCamelCase:Op,noop:Dp,toFiniteNumber:Rp,findKey:ha,global:Pt,isContextDefined:pa,isSpecCompliantForm:Lp,toJSONObject:$p,isAsyncFn:Ip,isThenable:Pp,setImmediate:ma,asap:Mp,isIterable:kp};function O(e,t,n,r,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),i&&(this.response=i,this.status=i.status?i.status:null)}f.inherits(O,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:f.toJSONObject(this.config),code:this.code,status:this.status}}});const ga=O.prototype,Ea={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Ea[e]={value:e}});Object.defineProperties(O,Ea);Object.defineProperty(ga,"isAxiosError",{value:!0});O.from=(e,t,n,r,i,s)=>{const o=Object.create(ga);return f.toFlatObject(e,o,function(c){return c!==Error.prototype},a=>a!=="isAxiosError"),O.call(o,e.message,t,n,r,i),o.cause=e,o.name=e.name,s&&Object.assign(o,s),o};const Fp=null;function Ir(e){return f.isPlainObject(e)||f.isArray(e)}function ba(e){return f.endsWith(e,"[]")?e.slice(0,-2):e}function Ms(e,t,n){return e?e.concat(t).map(function(i,s){return i=ba(i),!n&&s?"["+i+"]":i}).join(n?".":""):t}function jp(e){return f.isArray(e)&&!e.some(Ir)}const Bp=f.toFlatObject(f,{},null,function(t){return/^is[A-Z]/.test(t)});function Un(e,t,n){if(!f.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=f.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(g,d){return!f.isUndefined(d[g])});const r=n.metaTokens,i=n.visitor||l,s=n.dots,o=n.indexes,c=(n.Blob||typeof Blob<"u"&&Blob)&&f.isSpecCompliantForm(t);if(!f.isFunction(i))throw new TypeError("visitor must be a function");function u(m){if(m===null)return"";if(f.isDate(m))return m.toISOString();if(!c&&f.isBlob(m))throw new O("Blob is not supported. Use a Buffer instead.");return f.isArrayBuffer(m)||f.isTypedArray(m)?c&&typeof Blob=="function"?new Blob([m]):Buffer.from(m):m}function l(m,g,d){let b=m;if(m&&!d&&typeof m=="object"){if(f.endsWith(g,"{}"))g=r?g:g.slice(0,-2),m=JSON.stringify(m);else if(f.isArray(m)&&jp(m)||(f.isFileList(m)||f.endsWith(g,"[]"))&&(b=f.toArray(m)))return g=ba(g),b.forEach(function(A,y){!(f.isUndefined(A)||A===null)&&t.append(o===!0?Ms([g],y,s):o===null?g:g+"[]",u(A))}),!1}return Ir(m)?!0:(t.append(Ms(d,g,s),u(m)),!1)}const h=[],_=Object.assign(Bp,{defaultVisitor:l,convertValue:u,isVisitable:Ir});function E(m,g){if(!f.isUndefined(m)){if(h.indexOf(m)!==-1)throw Error("Circular reference detected in "+g.join("."));h.push(m),f.forEach(m,function(b,v){(!(f.isUndefined(b)||b===null)&&i.call(t,b,f.isString(v)?v.trim():v,g,_))===!0&&E(b,g?g.concat(v):[v])}),h.pop()}}if(!f.isObject(e))throw new TypeError("data must be an object");return E(e),t}function ks(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function yi(e,t){this._pairs=[],e&&Un(e,this,t)}const va=yi.prototype;va.append=function(t,n){this._pairs.push([t,n])};va.toString=function(t){const n=t?function(r){return t.call(this,r,ks)}:ks;return this._pairs.map(function(i){return n(i[0])+"="+n(i[1])},"").join("&")};function Hp(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ya(e,t,n){if(!t)return e;const r=n&&n.encode||Hp;f.isFunction(n)&&(n={serialize:n});const i=n&&n.serialize;let s;if(i?s=i(t,n):s=f.isURLSearchParams(t)?t.toString():new yi(t,n).toString(r),s){const o=e.indexOf("#");o!==-1&&(e=e.slice(0,o)),e+=(e.indexOf("?")===-1?"?":"&")+s}return e}class Fs{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){f.forEach(this.handlers,function(r){r!==null&&t(r)})}}const Aa={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Vp=typeof URLSearchParams<"u"?URLSearchParams:yi,Up=typeof FormData<"u"?FormData:null,Wp=typeof Blob<"u"?Blob:null,Kp={isBrowser:!0,classes:{URLSearchParams:Vp,FormData:Up,Blob:Wp},protocols:["http","https","file","blob","url","data"]},Ai=typeof window<"u"&&typeof document<"u",Pr=typeof navigator=="object"&&navigator||void 0,qp=Ai&&(!Pr||["ReactNative","NativeScript","NS"].indexOf(Pr.product)<0),zp=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Yp=Ai&&window.location.href||"http://localhost",Gp=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Ai,hasStandardBrowserEnv:qp,hasStandardBrowserWebWorkerEnv:zp,navigator:Pr,origin:Yp},Symbol.toStringTag,{value:"Module"})),M={...Gp,...Kp};function Xp(e,t){return Un(e,new M.classes.URLSearchParams,Object.assign({visitor:function(n,r,i,s){return M.isNode&&f.isBuffer(n)?(this.append(r,n.toString("base64")),!1):s.defaultVisitor.apply(this,arguments)}},t))}function Jp(e){return f.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Qp(e){const t={},n=Object.keys(e);let r;const i=n.length;let s;for(r=0;r<i;r++)s=n[r],t[s]=e[s];return t}function wa(e){function t(n,r,i,s){let o=n[s++];if(o==="__proto__")return!0;const a=Number.isFinite(+o),c=s>=n.length;return o=!o&&f.isArray(i)?i.length:o,c?(f.hasOwnProp(i,o)?i[o]=[i[o],r]:i[o]=r,!a):((!i[o]||!f.isObject(i[o]))&&(i[o]=[]),t(n,r,i[o],s)&&f.isArray(i[o])&&(i[o]=Qp(i[o])),!a)}if(f.isFormData(e)&&f.isFunction(e.entries)){const n={};return f.forEachEntry(e,(r,i)=>{t(Jp(r),i,n,0)}),n}return null}function Zp(e,t,n){if(f.isString(e))try{return(t||JSON.parse)(e),f.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const Ke={transitional:Aa,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",i=r.indexOf("application/json")>-1,s=f.isObject(t);if(s&&f.isHTMLForm(t)&&(t=new FormData(t)),f.isFormData(t))return i?JSON.stringify(wa(t)):t;if(f.isArrayBuffer(t)||f.isBuffer(t)||f.isStream(t)||f.isFile(t)||f.isBlob(t)||f.isReadableStream(t))return t;if(f.isArrayBufferView(t))return t.buffer;if(f.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let a;if(s){if(r.indexOf("application/x-www-form-urlencoded")>-1)return Xp(t,this.formSerializer).toString();if((a=f.isFileList(t))||r.indexOf("multipart/form-data")>-1){const c=this.env&&this.env.FormData;return Un(a?{"files[]":t}:t,c&&new c,this.formSerializer)}}return s||i?(n.setContentType("application/json",!1),Zp(t)):t}],transformResponse:[function(t){const n=this.transitional||Ke.transitional,r=n&&n.forcedJSONParsing,i=this.responseType==="json";if(f.isResponse(t)||f.isReadableStream(t))return t;if(t&&f.isString(t)&&(r&&!this.responseType||i)){const o=!(n&&n.silentJSONParsing)&&i;try{return JSON.parse(t)}catch(a){if(o)throw a.name==="SyntaxError"?O.from(a,O.ERR_BAD_RESPONSE,this,null,this.response):a}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:M.classes.FormData,Blob:M.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};f.forEach(["delete","get","head","post","put","patch"],e=>{Ke.headers[e]={}});const t_=f.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),e_=e=>{const t={};let n,r,i;return e&&e.split(`
`).forEach(function(o){i=o.indexOf(":"),n=o.substring(0,i).trim().toLowerCase(),r=o.substring(i+1).trim(),!(!n||t[n]&&t_[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},js=Symbol("internals");function Ce(e){return e&&String(e).trim().toLowerCase()}function gn(e){return e===!1||e==null?e:f.isArray(e)?e.map(gn):String(e)}function n_(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const r_=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Er(e,t,n,r,i){if(f.isFunction(r))return r.call(this,t,n);if(i&&(t=n),!!f.isString(t)){if(f.isString(r))return t.indexOf(r)!==-1;if(f.isRegExp(r))return r.test(t)}}function i_(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function s_(e,t){const n=f.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(i,s,o){return this[r].call(this,t,i,s,o)},configurable:!0})})}let U=class{constructor(t){t&&this.set(t)}set(t,n,r){const i=this;function s(a,c,u){const l=Ce(c);if(!l)throw new Error("header name must be a non-empty string");const h=f.findKey(i,l);(!h||i[h]===void 0||u===!0||u===void 0&&i[h]!==!1)&&(i[h||c]=gn(a))}const o=(a,c)=>f.forEach(a,(u,l)=>s(u,l,c));if(f.isPlainObject(t)||t instanceof this.constructor)o(t,n);else if(f.isString(t)&&(t=t.trim())&&!r_(t))o(e_(t),n);else if(f.isObject(t)&&f.isIterable(t)){let a={},c,u;for(const l of t){if(!f.isArray(l))throw TypeError("Object iterator must return a key-value pair");a[u=l[0]]=(c=a[u])?f.isArray(c)?[...c,l[1]]:[c,l[1]]:l[1]}o(a,n)}else t!=null&&s(n,t,r);return this}get(t,n){if(t=Ce(t),t){const r=f.findKey(this,t);if(r){const i=this[r];if(!n)return i;if(n===!0)return n_(i);if(f.isFunction(n))return n.call(this,i,r);if(f.isRegExp(n))return n.exec(i);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=Ce(t),t){const r=f.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||Er(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let i=!1;function s(o){if(o=Ce(o),o){const a=f.findKey(r,o);a&&(!n||Er(r,r[a],a,n))&&(delete r[a],i=!0)}}return f.isArray(t)?t.forEach(s):s(t),i}clear(t){const n=Object.keys(this);let r=n.length,i=!1;for(;r--;){const s=n[r];(!t||Er(this,this[s],s,t,!0))&&(delete this[s],i=!0)}return i}normalize(t){const n=this,r={};return f.forEach(this,(i,s)=>{const o=f.findKey(r,s);if(o){n[o]=gn(i),delete n[s];return}const a=t?i_(s):String(s).trim();a!==s&&delete n[s],n[a]=gn(i),r[a]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return f.forEach(this,(r,i)=>{r!=null&&r!==!1&&(n[i]=t&&f.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(i=>r.set(i)),r}static accessor(t){const r=(this[js]=this[js]={accessors:{}}).accessors,i=this.prototype;function s(o){const a=Ce(o);r[a]||(s_(i,o),r[a]=!0)}return f.isArray(t)?t.forEach(s):s(t),this}};U.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);f.reduceDescriptors(U.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});f.freezeMethods(U);function br(e,t){const n=this||Ke,r=t||n,i=U.from(r.headers);let s=r.data;return f.forEach(e,function(a){s=a.call(n,s,i.normalize(),t?t.status:void 0)}),i.normalize(),s}function Ta(e){return!!(e&&e.__CANCEL__)}function ge(e,t,n){O.call(this,e??"canceled",O.ERR_CANCELED,t,n),this.name="CanceledError"}f.inherits(ge,O,{__CANCEL__:!0});function Sa(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new O("Request failed with status code "+n.status,[O.ERR_BAD_REQUEST,O.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function o_(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function a_(e,t){e=e||10;const n=new Array(e),r=new Array(e);let i=0,s=0,o;return t=t!==void 0?t:1e3,function(c){const u=Date.now(),l=r[s];o||(o=u),n[i]=c,r[i]=u;let h=s,_=0;for(;h!==i;)_+=n[h++],h=h%e;if(i=(i+1)%e,i===s&&(s=(s+1)%e),u-o<t)return;const E=l&&u-l;return E?Math.round(_*1e3/E):void 0}}function c_(e,t){let n=0,r=1e3/t,i,s;const o=(u,l=Date.now())=>{n=l,i=null,s&&(clearTimeout(s),s=null),e.apply(null,u)};return[(...u)=>{const l=Date.now(),h=l-n;h>=r?o(u,l):(i=u,s||(s=setTimeout(()=>{s=null,o(i)},r-h)))},()=>i&&o(i)]}const Sn=(e,t,n=3)=>{let r=0;const i=a_(50,250);return c_(s=>{const o=s.loaded,a=s.lengthComputable?s.total:void 0,c=o-r,u=i(c),l=o<=a;r=o;const h={loaded:o,total:a,progress:a?o/a:void 0,bytes:c,rate:u||void 0,estimated:u&&a&&l?(a-o)/u:void 0,event:s,lengthComputable:a!=null,[t?"download":"upload"]:!0};e(h)},n)},Bs=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Hs=e=>(...t)=>f.asap(()=>e(...t)),l_=M.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,M.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(M.origin),M.navigator&&/(msie|trident)/i.test(M.navigator.userAgent)):()=>!0,u_=M.hasStandardBrowserEnv?{write(e,t,n,r,i,s){const o=[e+"="+encodeURIComponent(t)];f.isNumber(n)&&o.push("expires="+new Date(n).toGMTString()),f.isString(r)&&o.push("path="+r),f.isString(i)&&o.push("domain="+i),s===!0&&o.push("secure"),document.cookie=o.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function f_(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function d_(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Oa(e,t,n){let r=!f_(t);return e&&(r||n==!1)?d_(e,t):t}const Vs=e=>e instanceof U?{...e}:e;function Kt(e,t){t=t||{};const n={};function r(u,l,h,_){return f.isPlainObject(u)&&f.isPlainObject(l)?f.merge.call({caseless:_},u,l):f.isPlainObject(l)?f.merge({},l):f.isArray(l)?l.slice():l}function i(u,l,h,_){if(f.isUndefined(l)){if(!f.isUndefined(u))return r(void 0,u,h,_)}else return r(u,l,h,_)}function s(u,l){if(!f.isUndefined(l))return r(void 0,l)}function o(u,l){if(f.isUndefined(l)){if(!f.isUndefined(u))return r(void 0,u)}else return r(void 0,l)}function a(u,l,h){if(h in t)return r(u,l);if(h in e)return r(void 0,u)}const c={url:s,method:s,data:s,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:a,headers:(u,l,h)=>i(Vs(u),Vs(l),h,!0)};return f.forEach(Object.keys(Object.assign({},e,t)),function(l){const h=c[l]||i,_=h(e[l],t[l],l);f.isUndefined(_)&&h!==a||(n[l]=_)}),n}const Ca=e=>{const t=Kt({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:i,xsrfCookieName:s,headers:o,auth:a}=t;t.headers=o=U.from(o),t.url=ya(Oa(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),a&&o.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):"")));let c;if(f.isFormData(n)){if(M.hasStandardBrowserEnv||M.hasStandardBrowserWebWorkerEnv)o.setContentType(void 0);else if((c=o.getContentType())!==!1){const[u,...l]=c?c.split(";").map(h=>h.trim()).filter(Boolean):[];o.setContentType([u||"multipart/form-data",...l].join("; "))}}if(M.hasStandardBrowserEnv&&(r&&f.isFunction(r)&&(r=r(t)),r||r!==!1&&l_(t.url))){const u=i&&s&&u_.read(s);u&&o.set(i,u)}return t},h_=typeof XMLHttpRequest<"u",p_=h_&&function(e){return new Promise(function(n,r){const i=Ca(e);let s=i.data;const o=U.from(i.headers).normalize();let{responseType:a,onUploadProgress:c,onDownloadProgress:u}=i,l,h,_,E,m;function g(){E&&E(),m&&m(),i.cancelToken&&i.cancelToken.unsubscribe(l),i.signal&&i.signal.removeEventListener("abort",l)}let d=new XMLHttpRequest;d.open(i.method.toUpperCase(),i.url,!0),d.timeout=i.timeout;function b(){if(!d)return;const A=U.from("getAllResponseHeaders"in d&&d.getAllResponseHeaders()),T={data:!a||a==="text"||a==="json"?d.responseText:d.response,status:d.status,statusText:d.statusText,headers:A,config:e,request:d};Sa(function(C){n(C),g()},function(C){r(C),g()},T),d=null}"onloadend"in d?d.onloadend=b:d.onreadystatechange=function(){!d||d.readyState!==4||d.status===0&&!(d.responseURL&&d.responseURL.indexOf("file:")===0)||setTimeout(b)},d.onabort=function(){d&&(r(new O("Request aborted",O.ECONNABORTED,e,d)),d=null)},d.onerror=function(){r(new O("Network Error",O.ERR_NETWORK,e,d)),d=null},d.ontimeout=function(){let y=i.timeout?"timeout of "+i.timeout+"ms exceeded":"timeout exceeded";const T=i.transitional||Aa;i.timeoutErrorMessage&&(y=i.timeoutErrorMessage),r(new O(y,T.clarifyTimeoutError?O.ETIMEDOUT:O.ECONNABORTED,e,d)),d=null},s===void 0&&o.setContentType(null),"setRequestHeader"in d&&f.forEach(o.toJSON(),function(y,T){d.setRequestHeader(T,y)}),f.isUndefined(i.withCredentials)||(d.withCredentials=!!i.withCredentials),a&&a!=="json"&&(d.responseType=i.responseType),u&&([_,m]=Sn(u,!0),d.addEventListener("progress",_)),c&&d.upload&&([h,E]=Sn(c),d.upload.addEventListener("progress",h),d.upload.addEventListener("loadend",E)),(i.cancelToken||i.signal)&&(l=A=>{d&&(r(!A||A.type?new ge(null,e,d):A),d.abort(),d=null)},i.cancelToken&&i.cancelToken.subscribe(l),i.signal&&(i.signal.aborted?l():i.signal.addEventListener("abort",l)));const v=o_(i.url);if(v&&M.protocols.indexOf(v)===-1){r(new O("Unsupported protocol "+v+":",O.ERR_BAD_REQUEST,e));return}d.send(s||null)})},__=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,i;const s=function(u){if(!i){i=!0,a();const l=u instanceof Error?u:this.reason;r.abort(l instanceof O?l:new ge(l instanceof Error?l.message:l))}};let o=t&&setTimeout(()=>{o=null,s(new O(`timeout ${t} of ms exceeded`,O.ETIMEDOUT))},t);const a=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(s):u.removeEventListener("abort",s)}),e=null)};e.forEach(u=>u.addEventListener("abort",s));const{signal:c}=r;return c.unsubscribe=()=>f.asap(a),c}},m_=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let r=0,i;for(;r<n;)i=r+t,yield e.slice(r,i),r=i},g_=async function*(e,t){for await(const n of E_(e))yield*m_(n,t)},E_=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},Us=(e,t,n,r)=>{const i=g_(e,t);let s=0,o,a=c=>{o||(o=!0,r&&r(c))};return new ReadableStream({async pull(c){try{const{done:u,value:l}=await i.next();if(u){a(),c.close();return}let h=l.byteLength;if(n){let _=s+=h;n(_)}c.enqueue(new Uint8Array(l))}catch(u){throw a(u),u}},cancel(c){return a(c),i.return()}},{highWaterMark:2})},Wn=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",xa=Wn&&typeof ReadableStream=="function",b_=Wn&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Na=(e,...t)=>{try{return!!e(...t)}catch{return!1}},v_=xa&&Na(()=>{let e=!1;const t=new Request(M.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Ws=64*1024,Mr=xa&&Na(()=>f.isReadableStream(new Response("").body)),On={stream:Mr&&(e=>e.body)};Wn&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!On[t]&&(On[t]=f.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new O(`Response type '${t}' is not supported`,O.ERR_NOT_SUPPORT,r)})})})(new Response);const y_=async e=>{if(e==null)return 0;if(f.isBlob(e))return e.size;if(f.isSpecCompliantForm(e))return(await new Request(M.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(f.isArrayBufferView(e)||f.isArrayBuffer(e))return e.byteLength;if(f.isURLSearchParams(e)&&(e=e+""),f.isString(e))return(await b_(e)).byteLength},A_=async(e,t)=>{const n=f.toFiniteNumber(e.getContentLength());return n??y_(t)},w_=Wn&&(async e=>{let{url:t,method:n,data:r,signal:i,cancelToken:s,timeout:o,onDownloadProgress:a,onUploadProgress:c,responseType:u,headers:l,withCredentials:h="same-origin",fetchOptions:_}=Ca(e);u=u?(u+"").toLowerCase():"text";let E=__([i,s&&s.toAbortSignal()],o),m;const g=E&&E.unsubscribe&&(()=>{E.unsubscribe()});let d;try{if(c&&v_&&n!=="get"&&n!=="head"&&(d=await A_(l,r))!==0){let T=new Request(t,{method:"POST",body:r,duplex:"half"}),S;if(f.isFormData(r)&&(S=T.headers.get("content-type"))&&l.setContentType(S),T.body){const[C,N]=Bs(d,Sn(Hs(c)));r=Us(T.body,Ws,C,N)}}f.isString(h)||(h=h?"include":"omit");const b="credentials"in Request.prototype;m=new Request(t,{..._,signal:E,method:n.toUpperCase(),headers:l.normalize().toJSON(),body:r,duplex:"half",credentials:b?h:void 0});let v=await fetch(m);const A=Mr&&(u==="stream"||u==="response");if(Mr&&(a||A&&g)){const T={};["status","statusText","headers"].forEach(D=>{T[D]=v[D]});const S=f.toFiniteNumber(v.headers.get("content-length")),[C,N]=a&&Bs(S,Sn(Hs(a),!0))||[];v=new Response(Us(v.body,Ws,C,()=>{N&&N(),g&&g()}),T)}u=u||"text";let y=await On[f.findKey(On,u)||"text"](v,e);return!A&&g&&g(),await new Promise((T,S)=>{Sa(T,S,{data:y,headers:U.from(v.headers),status:v.status,statusText:v.statusText,config:e,request:m})})}catch(b){throw g&&g(),b&&b.name==="TypeError"&&/Load failed|fetch/i.test(b.message)?Object.assign(new O("Network Error",O.ERR_NETWORK,e,m),{cause:b.cause||b}):O.from(b,b&&b.code,e,m)}}),kr={http:Fp,xhr:p_,fetch:w_};f.forEach(kr,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Ks=e=>`- ${e}`,T_=e=>f.isFunction(e)||e===null||e===!1,Da={getAdapter:e=>{e=f.isArray(e)?e:[e];const{length:t}=e;let n,r;const i={};for(let s=0;s<t;s++){n=e[s];let o;if(r=n,!T_(n)&&(r=kr[(o=String(n)).toLowerCase()],r===void 0))throw new O(`Unknown adapter '${o}'`);if(r)break;i[o||"#"+s]=r}if(!r){const s=Object.entries(i).map(([a,c])=>`adapter ${a} `+(c===!1?"is not supported by the environment":"is not available in the build"));let o=t?s.length>1?`since :
`+s.map(Ks).join(`
`):" "+Ks(s[0]):"as no adapter specified";throw new O("There is no suitable adapter to dispatch the request "+o,"ERR_NOT_SUPPORT")}return r},adapters:kr};function vr(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new ge(null,e)}function qs(e){return vr(e),e.headers=U.from(e.headers),e.data=br.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Da.getAdapter(e.adapter||Ke.adapter)(e).then(function(r){return vr(e),r.data=br.call(e,e.transformResponse,r),r.headers=U.from(r.headers),r},function(r){return Ta(r)||(vr(e),r&&r.response&&(r.response.data=br.call(e,e.transformResponse,r.response),r.response.headers=U.from(r.response.headers))),Promise.reject(r)})}const Ra="1.9.0",Kn={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Kn[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const zs={};Kn.transitional=function(t,n,r){function i(s,o){return"[Axios v"+Ra+"] Transitional option '"+s+"'"+o+(r?". "+r:"")}return(s,o,a)=>{if(t===!1)throw new O(i(o," has been removed"+(n?" in "+n:"")),O.ERR_DEPRECATED);return n&&!zs[o]&&(zs[o]=!0,console.warn(i(o," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(s,o,a):!0}};Kn.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function S_(e,t,n){if(typeof e!="object")throw new O("options must be an object",O.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let i=r.length;for(;i-- >0;){const s=r[i],o=t[s];if(o){const a=e[s],c=a===void 0||o(a,s,e);if(c!==!0)throw new O("option "+s+" must be "+c,O.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new O("Unknown option "+s,O.ERR_BAD_OPTION)}}const En={assertOptions:S_,validators:Kn},st=En.validators;let Ft=class{constructor(t){this.defaults=t||{},this.interceptors={request:new Fs,response:new Fs}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let i={};Error.captureStackTrace?Error.captureStackTrace(i):i=new Error;const s=i.stack?i.stack.replace(/^.+\n/,""):"";try{r.stack?s&&!String(r.stack).endsWith(s.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+s):r.stack=s}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=Kt(this.defaults,n);const{transitional:r,paramsSerializer:i,headers:s}=n;r!==void 0&&En.assertOptions(r,{silentJSONParsing:st.transitional(st.boolean),forcedJSONParsing:st.transitional(st.boolean),clarifyTimeoutError:st.transitional(st.boolean)},!1),i!=null&&(f.isFunction(i)?n.paramsSerializer={serialize:i}:En.assertOptions(i,{encode:st.function,serialize:st.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),En.assertOptions(n,{baseUrl:st.spelling("baseURL"),withXsrfToken:st.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let o=s&&f.merge(s.common,s[n.method]);s&&f.forEach(["delete","get","head","post","put","patch","common"],m=>{delete s[m]}),n.headers=U.concat(o,s);const a=[];let c=!0;this.interceptors.request.forEach(function(g){typeof g.runWhen=="function"&&g.runWhen(n)===!1||(c=c&&g.synchronous,a.unshift(g.fulfilled,g.rejected))});const u=[];this.interceptors.response.forEach(function(g){u.push(g.fulfilled,g.rejected)});let l,h=0,_;if(!c){const m=[qs.bind(this),void 0];for(m.unshift.apply(m,a),m.push.apply(m,u),_=m.length,l=Promise.resolve(n);h<_;)l=l.then(m[h++],m[h++]);return l}_=a.length;let E=n;for(h=0;h<_;){const m=a[h++],g=a[h++];try{E=m(E)}catch(d){g.call(this,d);break}}try{l=qs.call(this,E)}catch(m){return Promise.reject(m)}for(h=0,_=u.length;h<_;)l=l.then(u[h++],u[h++]);return l}getUri(t){t=Kt(this.defaults,t);const n=Oa(t.baseURL,t.url,t.allowAbsoluteUrls);return ya(n,t.params,t.paramsSerializer)}};f.forEach(["delete","get","head","options"],function(t){Ft.prototype[t]=function(n,r){return this.request(Kt(r||{},{method:t,url:n,data:(r||{}).data}))}});f.forEach(["post","put","patch"],function(t){function n(r){return function(s,o,a){return this.request(Kt(a||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:s,data:o}))}}Ft.prototype[t]=n(),Ft.prototype[t+"Form"]=n(!0)});let O_=class La{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(s){n=s});const r=this;this.promise.then(i=>{if(!r._listeners)return;let s=r._listeners.length;for(;s-- >0;)r._listeners[s](i);r._listeners=null}),this.promise.then=i=>{let s;const o=new Promise(a=>{r.subscribe(a),s=a}).then(i);return o.cancel=function(){r.unsubscribe(s)},o},t(function(s,o,a){r.reason||(r.reason=new ge(s,o,a),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new La(function(i){t=i}),cancel:t}}};function C_(e){return function(n){return e.apply(null,n)}}function x_(e){return f.isObject(e)&&e.isAxiosError===!0}const Fr={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Fr).forEach(([e,t])=>{Fr[t]=e});function $a(e){const t=new Ft(e),n=la(Ft.prototype.request,t);return f.extend(n,Ft.prototype,t,{allOwnKeys:!0}),f.extend(n,t,null,{allOwnKeys:!0}),n.create=function(i){return $a(Kt(e,i))},n}const $=$a(Ke);$.Axios=Ft;$.CanceledError=ge;$.CancelToken=O_;$.isCancel=Ta;$.VERSION=Ra;$.toFormData=Un;$.AxiosError=O;$.Cancel=$.CanceledError;$.all=function(t){return Promise.all(t)};$.spread=C_;$.isAxiosError=x_;$.mergeConfig=Kt;$.AxiosHeaders=U;$.formToJSON=e=>wa(f.isHTMLForm(e)?new FormData(e):e);$.getAdapter=Da.getAdapter;$.HttpStatusCode=Fr;$.default=$;const{Axios:Bg,AxiosError:Hg,CanceledError:Vg,isCancel:Ug,CancelToken:Wg,VERSION:Kg,all:qg,Cancel:zg,isAxiosError:Yg,spread:Gg,toFormData:Xg,AxiosHeaders:Jg,HttpStatusCode:Qg,formToJSON:Zg,getAdapter:tE,mergeConfig:eE}=$;window.axios=$;window.axios.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";var jr=!1,Br=!1,jt=[],Hr=-1;function N_(e){D_(e)}function D_(e){jt.includes(e)||jt.push(e),L_()}function R_(e){let t=jt.indexOf(e);t!==-1&&t>Hr&&jt.splice(t,1)}function L_(){!Br&&!jr&&(jr=!0,queueMicrotask($_))}function $_(){jr=!1,Br=!0;for(let e=0;e<jt.length;e++)jt[e](),Hr=e;jt.length=0,Hr=-1,Br=!1}var Ee,Gt,be,Ia,Vr=!0;function I_(e){Vr=!1,e(),Vr=!0}function P_(e){Ee=e.reactive,be=e.release,Gt=t=>e.effect(t,{scheduler:n=>{Vr?N_(n):n()}}),Ia=e.raw}function Ys(e){Gt=e}function M_(e){let t=()=>{};return[r=>{let i=Gt(r);return e._x_effects||(e._x_effects=new Set,e._x_runEffects=()=>{e._x_effects.forEach(s=>s())}),e._x_effects.add(i),t=()=>{i!==void 0&&(e._x_effects.delete(i),be(i))},i},()=>{t()}]}function Pa(e,t){let n=!0,r,i=Gt(()=>{let s=e();JSON.stringify(s),n?r=s:queueMicrotask(()=>{t(s,r),r=s}),n=!1});return()=>be(i)}var Ma=[],ka=[],Fa=[];function k_(e){Fa.push(e)}function wi(e,t){typeof t=="function"?(e._x_cleanups||(e._x_cleanups=[]),e._x_cleanups.push(t)):(t=e,ka.push(t))}function ja(e){Ma.push(e)}function Ba(e,t,n){e._x_attributeCleanups||(e._x_attributeCleanups={}),e._x_attributeCleanups[t]||(e._x_attributeCleanups[t]=[]),e._x_attributeCleanups[t].push(n)}function Ha(e,t){e._x_attributeCleanups&&Object.entries(e._x_attributeCleanups).forEach(([n,r])=>{(t===void 0||t.includes(n))&&(r.forEach(i=>i()),delete e._x_attributeCleanups[n])})}function F_(e){var t,n;for((t=e._x_effects)==null||t.forEach(R_);(n=e._x_cleanups)!=null&&n.length;)e._x_cleanups.pop()()}var Ti=new MutationObserver(xi),Si=!1;function Oi(){Ti.observe(document,{subtree:!0,childList:!0,attributes:!0,attributeOldValue:!0}),Si=!0}function Va(){j_(),Ti.disconnect(),Si=!1}var xe=[];function j_(){let e=Ti.takeRecords();xe.push(()=>e.length>0&&xi(e));let t=xe.length;queueMicrotask(()=>{if(xe.length===t)for(;xe.length>0;)xe.shift()()})}function R(e){if(!Si)return e();Va();let t=e();return Oi(),t}var Ci=!1,Cn=[];function B_(){Ci=!0}function H_(){Ci=!1,xi(Cn),Cn=[]}function xi(e){if(Ci){Cn=Cn.concat(e);return}let t=[],n=new Set,r=new Map,i=new Map;for(let s=0;s<e.length;s++)if(!e[s].target._x_ignoreMutationObserver&&(e[s].type==="childList"&&(e[s].removedNodes.forEach(o=>{o.nodeType===1&&o._x_marker&&n.add(o)}),e[s].addedNodes.forEach(o=>{if(o.nodeType===1){if(n.has(o)){n.delete(o);return}o._x_marker||t.push(o)}})),e[s].type==="attributes")){let o=e[s].target,a=e[s].attributeName,c=e[s].oldValue,u=()=>{r.has(o)||r.set(o,[]),r.get(o).push({name:a,value:o.getAttribute(a)})},l=()=>{i.has(o)||i.set(o,[]),i.get(o).push(a)};o.hasAttribute(a)&&c===null?u():o.hasAttribute(a)?(l(),u()):l()}i.forEach((s,o)=>{Ha(o,s)}),r.forEach((s,o)=>{Ma.forEach(a=>a(o,s))});for(let s of n)t.some(o=>o.contains(s))||ka.forEach(o=>o(s));for(let s of t)s.isConnected&&Fa.forEach(o=>o(s));t=null,n=null,r=null,i=null}function Ua(e){return ze(ue(e))}function qe(e,t,n){return e._x_dataStack=[t,...ue(n||e)],()=>{e._x_dataStack=e._x_dataStack.filter(r=>r!==t)}}function ue(e){return e._x_dataStack?e._x_dataStack:typeof ShadowRoot=="function"&&e instanceof ShadowRoot?ue(e.host):e.parentNode?ue(e.parentNode):[]}function ze(e){return new Proxy({objects:e},V_)}var V_={ownKeys({objects:e}){return Array.from(new Set(e.flatMap(t=>Object.keys(t))))},has({objects:e},t){return t==Symbol.unscopables?!1:e.some(n=>Object.prototype.hasOwnProperty.call(n,t)||Reflect.has(n,t))},get({objects:e},t,n){return t=="toJSON"?U_:Reflect.get(e.find(r=>Reflect.has(r,t))||{},t,n)},set({objects:e},t,n,r){const i=e.find(o=>Object.prototype.hasOwnProperty.call(o,t))||e[e.length-1],s=Object.getOwnPropertyDescriptor(i,t);return s!=null&&s.set&&(s!=null&&s.get)?s.set.call(r,n)||!0:Reflect.set(i,t,n)}};function U_(){return Reflect.ownKeys(this).reduce((t,n)=>(t[n]=Reflect.get(this,n),t),{})}function Wa(e){let t=r=>typeof r=="object"&&!Array.isArray(r)&&r!==null,n=(r,i="")=>{Object.entries(Object.getOwnPropertyDescriptors(r)).forEach(([s,{value:o,enumerable:a}])=>{if(a===!1||o===void 0||typeof o=="object"&&o!==null&&o.__v_skip)return;let c=i===""?s:`${i}.${s}`;typeof o=="object"&&o!==null&&o._x_interceptor?r[s]=o.initialize(e,c,s):t(o)&&o!==r&&!(o instanceof Element)&&n(o,c)})};return n(e)}function Ka(e,t=()=>{}){let n={initialValue:void 0,_x_interceptor:!0,initialize(r,i,s){return e(this.initialValue,()=>W_(r,i),o=>Ur(r,i,o),i,s)}};return t(n),r=>{if(typeof r=="object"&&r!==null&&r._x_interceptor){let i=n.initialize.bind(n);n.initialize=(s,o,a)=>{let c=r.initialize(s,o,a);return n.initialValue=c,i(s,o,a)}}else n.initialValue=r;return n}}function W_(e,t){return t.split(".").reduce((n,r)=>n[r],e)}function Ur(e,t,n){if(typeof t=="string"&&(t=t.split(".")),t.length===1)e[t[0]]=n;else{if(t.length===0)throw error;return e[t[0]]||(e[t[0]]={}),Ur(e[t[0]],t.slice(1),n)}}var qa={};function it(e,t){qa[e]=t}function Wr(e,t){let n=K_(t);return Object.entries(qa).forEach(([r,i])=>{Object.defineProperty(e,`$${r}`,{get(){return i(t,n)},enumerable:!1})}),e}function K_(e){let[t,n]=Qa(e),r={interceptor:Ka,...t};return wi(e,n),r}function q_(e,t,n,...r){try{return n(...r)}catch(i){Fe(i,e,t)}}function Fe(e,t,n=void 0){e=Object.assign(e??{message:"No error message given."},{el:t,expression:n}),console.warn(`Alpine Expression Error: ${e.message}

${n?'Expression: "'+n+`"

`:""}`,t),setTimeout(()=>{throw e},0)}var bn=!0;function za(e){let t=bn;bn=!1;let n=e();return bn=t,n}function Bt(e,t,n={}){let r;return B(e,t)(i=>r=i,n),r}function B(...e){return Ya(...e)}var Ya=Ga;function z_(e){Ya=e}function Ga(e,t){let n={};Wr(n,e);let r=[n,...ue(e)],i=typeof t=="function"?Y_(r,t):X_(r,t,e);return q_.bind(null,e,t,i)}function Y_(e,t){return(n=()=>{},{scope:r={},params:i=[]}={})=>{let s=t.apply(ze([r,...e]),i);xn(n,s)}}var yr={};function G_(e,t){if(yr[e])return yr[e];let n=Object.getPrototypeOf(async function(){}).constructor,r=/^[\n\s]*if.*\(.*\)/.test(e.trim())||/^(let|const)\s/.test(e.trim())?`(async()=>{ ${e} })()`:e,s=(()=>{try{let o=new n(["__self","scope"],`with (scope) { __self.result = ${r} }; __self.finished = true; return __self.result;`);return Object.defineProperty(o,"name",{value:`[Alpine] ${e}`}),o}catch(o){return Fe(o,t,e),Promise.resolve()}})();return yr[e]=s,s}function X_(e,t,n){let r=G_(t,n);return(i=()=>{},{scope:s={},params:o=[]}={})=>{r.result=void 0,r.finished=!1;let a=ze([s,...e]);if(typeof r=="function"){let c=r(r,a).catch(u=>Fe(u,n,t));r.finished?(xn(i,r.result,a,o,n),r.result=void 0):c.then(u=>{xn(i,u,a,o,n)}).catch(u=>Fe(u,n,t)).finally(()=>r.result=void 0)}}}function xn(e,t,n,r,i){if(bn&&typeof t=="function"){let s=t.apply(n,r);s instanceof Promise?s.then(o=>xn(e,o,n,r)).catch(o=>Fe(o,i,t)):e(s)}else typeof t=="object"&&t instanceof Promise?t.then(s=>e(s)):e(t)}var Ni="x-";function ve(e=""){return Ni+e}function J_(e){Ni=e}var Nn={};function I(e,t){return Nn[e]=t,{before(n){if(!Nn[n]){console.warn(String.raw`Cannot find directive \`${n}\`. \`${e}\` will use the default order of execution`);return}const r=Mt.indexOf(n);Mt.splice(r>=0?r:Mt.indexOf("DEFAULT"),0,e)}}}function Q_(e){return Object.keys(Nn).includes(e)}function Di(e,t,n){if(t=Array.from(t),e._x_virtualDirectives){let s=Object.entries(e._x_virtualDirectives).map(([a,c])=>({name:a,value:c})),o=Xa(s);s=s.map(a=>o.find(c=>c.name===a.name)?{name:`x-bind:${a.name}`,value:`"${a.value}"`}:a),t=t.concat(s)}let r={};return t.map(ec((s,o)=>r[s]=o)).filter(rc).map(em(r,n)).sort(nm).map(s=>tm(e,s))}function Xa(e){return Array.from(e).map(ec()).filter(t=>!rc(t))}var Kr=!1,Re=new Map,Ja=Symbol();function Z_(e){Kr=!0;let t=Symbol();Ja=t,Re.set(t,[]);let n=()=>{for(;Re.get(t).length;)Re.get(t).shift()();Re.delete(t)},r=()=>{Kr=!1,n()};e(n),r()}function Qa(e){let t=[],n=a=>t.push(a),[r,i]=M_(e);return t.push(i),[{Alpine:Ye,effect:r,cleanup:n,evaluateLater:B.bind(B,e),evaluate:Bt.bind(Bt,e)},()=>t.forEach(a=>a())]}function tm(e,t){let n=()=>{},r=Nn[t.type]||n,[i,s]=Qa(e);Ba(e,t.original,s);let o=()=>{e._x_ignore||e._x_ignoreSelf||(r.inline&&r.inline(e,t,i),r=r.bind(r,e,t,i),Kr?Re.get(Ja).push(r):r())};return o.runCleanups=s,o}var Za=(e,t)=>({name:n,value:r})=>(n.startsWith(e)&&(n=n.replace(e,t)),{name:n,value:r}),tc=e=>e;function ec(e=()=>{}){return({name:t,value:n})=>{let{name:r,value:i}=nc.reduce((s,o)=>o(s),{name:t,value:n});return r!==t&&e(r,t),{name:r,value:i}}}var nc=[];function Ri(e){nc.push(e)}function rc({name:e}){return ic().test(e)}var ic=()=>new RegExp(`^${Ni}([^:^.]+)\\b`);function em(e,t){return({name:n,value:r})=>{let i=n.match(ic()),s=n.match(/:([a-zA-Z0-9\-_:]+)/),o=n.match(/\.[^.\]]+(?=[^\]]*$)/g)||[],a=t||e[n]||n;return{type:i?i[1]:null,value:s?s[1]:null,modifiers:o.map(c=>c.replace(".","")),expression:r,original:a}}}var qr="DEFAULT",Mt=["ignore","ref","data","id","anchor","bind","init","for","model","modelable","transition","show","if",qr,"teleport"];function nm(e,t){let n=Mt.indexOf(e.type)===-1?qr:e.type,r=Mt.indexOf(t.type)===-1?qr:t.type;return Mt.indexOf(n)-Mt.indexOf(r)}function Ie(e,t,n={}){e.dispatchEvent(new CustomEvent(t,{detail:n,bubbles:!0,composed:!0,cancelable:!0}))}function qt(e,t){if(typeof ShadowRoot=="function"&&e instanceof ShadowRoot){Array.from(e.children).forEach(i=>qt(i,t));return}let n=!1;if(t(e,()=>n=!0),n)return;let r=e.firstElementChild;for(;r;)qt(r,t),r=r.nextElementSibling}function Y(e,...t){console.warn(`Alpine Warning: ${e}`,...t)}var Gs=!1;function rm(){Gs&&Y("Alpine has already been initialized on this page. Calling Alpine.start() more than once can cause problems."),Gs=!0,document.body||Y("Unable to initialize. Trying to load Alpine before `<body>` is available. Did you forget to add `defer` in Alpine's `<script>` tag?"),Ie(document,"alpine:init"),Ie(document,"alpine:initializing"),Oi(),k_(t=>ht(t,qt)),wi(t=>Ae(t)),ja((t,n)=>{Di(t,n).forEach(r=>r())});let e=t=>!qn(t.parentElement,!0);Array.from(document.querySelectorAll(ac().join(","))).filter(e).forEach(t=>{ht(t)}),Ie(document,"alpine:initialized"),setTimeout(()=>{am()})}var Li=[],sc=[];function oc(){return Li.map(e=>e())}function ac(){return Li.concat(sc).map(e=>e())}function cc(e){Li.push(e)}function lc(e){sc.push(e)}function qn(e,t=!1){return ye(e,n=>{if((t?ac():oc()).some(i=>n.matches(i)))return!0})}function ye(e,t){if(e){if(t(e))return e;if(e._x_teleportBack&&(e=e._x_teleportBack),!!e.parentElement)return ye(e.parentElement,t)}}function im(e){return oc().some(t=>e.matches(t))}var uc=[];function sm(e){uc.push(e)}var om=1;function ht(e,t=qt,n=()=>{}){ye(e,r=>r._x_ignore)||Z_(()=>{t(e,(r,i)=>{r._x_marker||(n(r,i),uc.forEach(s=>s(r,i)),Di(r,r.attributes).forEach(s=>s()),r._x_ignore||(r._x_marker=om++),r._x_ignore&&i())})})}function Ae(e,t=qt){t(e,n=>{F_(n),Ha(n),delete n._x_marker})}function am(){[["ui","dialog",["[x-dialog], [x-popover]"]],["anchor","anchor",["[x-anchor]"]],["sort","sort",["[x-sort]"]]].forEach(([t,n,r])=>{Q_(n)||r.some(i=>{if(document.querySelector(i))return Y(`found "${i}", but missing ${t} plugin`),!0})})}var zr=[],$i=!1;function Ii(e=()=>{}){return queueMicrotask(()=>{$i||setTimeout(()=>{Yr()})}),new Promise(t=>{zr.push(()=>{e(),t()})})}function Yr(){for($i=!1;zr.length;)zr.shift()()}function cm(){$i=!0}function Pi(e,t){return Array.isArray(t)?Xs(e,t.join(" ")):typeof t=="object"&&t!==null?lm(e,t):typeof t=="function"?Pi(e,t()):Xs(e,t)}function Xs(e,t){let n=i=>i.split(" ").filter(s=>!e.classList.contains(s)).filter(Boolean),r=i=>(e.classList.add(...i),()=>{e.classList.remove(...i)});return t=t===!0?t="":t||"",r(n(t))}function lm(e,t){let n=a=>a.split(" ").filter(Boolean),r=Object.entries(t).flatMap(([a,c])=>c?n(a):!1).filter(Boolean),i=Object.entries(t).flatMap(([a,c])=>c?!1:n(a)).filter(Boolean),s=[],o=[];return i.forEach(a=>{e.classList.contains(a)&&(e.classList.remove(a),o.push(a))}),r.forEach(a=>{e.classList.contains(a)||(e.classList.add(a),s.push(a))}),()=>{o.forEach(a=>e.classList.add(a)),s.forEach(a=>e.classList.remove(a))}}function zn(e,t){return typeof t=="object"&&t!==null?um(e,t):fm(e,t)}function um(e,t){let n={};return Object.entries(t).forEach(([r,i])=>{n[r]=e.style[r],r.startsWith("--")||(r=dm(r)),e.style.setProperty(r,i)}),setTimeout(()=>{e.style.length===0&&e.removeAttribute("style")}),()=>{zn(e,n)}}function fm(e,t){let n=e.getAttribute("style",t);return e.setAttribute("style",t),()=>{e.setAttribute("style",n||"")}}function dm(e){return e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function Gr(e,t=()=>{}){let n=!1;return function(){n?t.apply(this,arguments):(n=!0,e.apply(this,arguments))}}I("transition",(e,{value:t,modifiers:n,expression:r},{evaluate:i})=>{typeof r=="function"&&(r=i(r)),r!==!1&&(!r||typeof r=="boolean"?pm(e,n,t):hm(e,r,t))});function hm(e,t,n){fc(e,Pi,""),{enter:i=>{e._x_transition.enter.during=i},"enter-start":i=>{e._x_transition.enter.start=i},"enter-end":i=>{e._x_transition.enter.end=i},leave:i=>{e._x_transition.leave.during=i},"leave-start":i=>{e._x_transition.leave.start=i},"leave-end":i=>{e._x_transition.leave.end=i}}[n](t)}function pm(e,t,n){fc(e,zn);let r=!t.includes("in")&&!t.includes("out")&&!n,i=r||t.includes("in")||["enter"].includes(n),s=r||t.includes("out")||["leave"].includes(n);t.includes("in")&&!r&&(t=t.filter((b,v)=>v<t.indexOf("out"))),t.includes("out")&&!r&&(t=t.filter((b,v)=>v>t.indexOf("out")));let o=!t.includes("opacity")&&!t.includes("scale"),a=o||t.includes("opacity"),c=o||t.includes("scale"),u=a?0:1,l=c?Ne(t,"scale",95)/100:1,h=Ne(t,"delay",0)/1e3,_=Ne(t,"origin","center"),E="opacity, transform",m=Ne(t,"duration",150)/1e3,g=Ne(t,"duration",75)/1e3,d="cubic-bezier(0.4, 0.0, 0.2, 1)";i&&(e._x_transition.enter.during={transformOrigin:_,transitionDelay:`${h}s`,transitionProperty:E,transitionDuration:`${m}s`,transitionTimingFunction:d},e._x_transition.enter.start={opacity:u,transform:`scale(${l})`},e._x_transition.enter.end={opacity:1,transform:"scale(1)"}),s&&(e._x_transition.leave.during={transformOrigin:_,transitionDelay:`${h}s`,transitionProperty:E,transitionDuration:`${g}s`,transitionTimingFunction:d},e._x_transition.leave.start={opacity:1,transform:"scale(1)"},e._x_transition.leave.end={opacity:u,transform:`scale(${l})`})}function fc(e,t,n={}){e._x_transition||(e._x_transition={enter:{during:n,start:n,end:n},leave:{during:n,start:n,end:n},in(r=()=>{},i=()=>{}){Xr(e,t,{during:this.enter.during,start:this.enter.start,end:this.enter.end},r,i)},out(r=()=>{},i=()=>{}){Xr(e,t,{during:this.leave.during,start:this.leave.start,end:this.leave.end},r,i)}})}window.Element.prototype._x_toggleAndCascadeWithTransitions=function(e,t,n,r){const i=document.visibilityState==="visible"?requestAnimationFrame:setTimeout;let s=()=>i(n);if(t){e._x_transition&&(e._x_transition.enter||e._x_transition.leave)?e._x_transition.enter&&(Object.entries(e._x_transition.enter.during).length||Object.entries(e._x_transition.enter.start).length||Object.entries(e._x_transition.enter.end).length)?e._x_transition.in(n):s():e._x_transition?e._x_transition.in(n):s();return}e._x_hidePromise=e._x_transition?new Promise((o,a)=>{e._x_transition.out(()=>{},()=>o(r)),e._x_transitioning&&e._x_transitioning.beforeCancel(()=>a({isFromCancelledTransition:!0}))}):Promise.resolve(r),queueMicrotask(()=>{let o=dc(e);o?(o._x_hideChildren||(o._x_hideChildren=[]),o._x_hideChildren.push(e)):i(()=>{let a=c=>{let u=Promise.all([c._x_hidePromise,...(c._x_hideChildren||[]).map(a)]).then(([l])=>l==null?void 0:l());return delete c._x_hidePromise,delete c._x_hideChildren,u};a(e).catch(c=>{if(!c.isFromCancelledTransition)throw c})})})};function dc(e){let t=e.parentNode;if(t)return t._x_hidePromise?t:dc(t)}function Xr(e,t,{during:n,start:r,end:i}={},s=()=>{},o=()=>{}){if(e._x_transitioning&&e._x_transitioning.cancel(),Object.keys(n).length===0&&Object.keys(r).length===0&&Object.keys(i).length===0){s(),o();return}let a,c,u;_m(e,{start(){a=t(e,r)},during(){c=t(e,n)},before:s,end(){a(),u=t(e,i)},after:o,cleanup(){c(),u()}})}function _m(e,t){let n,r,i,s=Gr(()=>{R(()=>{n=!0,r||t.before(),i||(t.end(),Yr()),t.after(),e.isConnected&&t.cleanup(),delete e._x_transitioning})});e._x_transitioning={beforeCancels:[],beforeCancel(o){this.beforeCancels.push(o)},cancel:Gr(function(){for(;this.beforeCancels.length;)this.beforeCancels.shift()();s()}),finish:s},R(()=>{t.start(),t.during()}),cm(),requestAnimationFrame(()=>{if(n)return;let o=Number(getComputedStyle(e).transitionDuration.replace(/,.*/,"").replace("s",""))*1e3,a=Number(getComputedStyle(e).transitionDelay.replace(/,.*/,"").replace("s",""))*1e3;o===0&&(o=Number(getComputedStyle(e).animationDuration.replace("s",""))*1e3),R(()=>{t.before()}),r=!0,requestAnimationFrame(()=>{n||(R(()=>{t.end()}),Yr(),setTimeout(e._x_transitioning.finish,o+a),i=!0)})})}function Ne(e,t,n){if(e.indexOf(t)===-1)return n;const r=e[e.indexOf(t)+1];if(!r||t==="scale"&&isNaN(r))return n;if(t==="duration"||t==="delay"){let i=r.match(/([0-9]+)ms/);if(i)return i[1]}return t==="origin"&&["top","right","left","center","bottom"].includes(e[e.indexOf(t)+2])?[r,e[e.indexOf(t)+2]].join(" "):r}var yt=!1;function Ot(e,t=()=>{}){return(...n)=>yt?t(...n):e(...n)}function mm(e){return(...t)=>yt&&e(...t)}var hc=[];function Yn(e){hc.push(e)}function gm(e,t){hc.forEach(n=>n(e,t)),yt=!0,pc(()=>{ht(t,(n,r)=>{r(n,()=>{})})}),yt=!1}var Jr=!1;function Em(e,t){t._x_dataStack||(t._x_dataStack=e._x_dataStack),yt=!0,Jr=!0,pc(()=>{bm(t)}),yt=!1,Jr=!1}function bm(e){let t=!1;ht(e,(r,i)=>{qt(r,(s,o)=>{if(t&&im(s))return o();t=!0,i(s,o)})})}function pc(e){let t=Gt;Ys((n,r)=>{let i=t(n);return be(i),()=>{}}),e(),Ys(t)}function _c(e,t,n,r=[]){switch(e._x_bindings||(e._x_bindings=Ee({})),e._x_bindings[t]=n,t=r.includes("camel")?Cm(t):t,t){case"value":vm(e,n);break;case"style":Am(e,n);break;case"class":ym(e,n);break;case"selected":case"checked":wm(e,t,n);break;default:mc(e,t,n);break}}function vm(e,t){if(bc(e))e.attributes.value===void 0&&(e.value=t),window.fromModel&&(typeof t=="boolean"?e.checked=vn(e.value)===t:e.checked=Js(e.value,t));else if(Mi(e))Number.isInteger(t)?e.value=t:!Array.isArray(t)&&typeof t!="boolean"&&![null,void 0].includes(t)?e.value=String(t):Array.isArray(t)?e.checked=t.some(n=>Js(n,e.value)):e.checked=!!t;else if(e.tagName==="SELECT")Om(e,t);else{if(e.value===t)return;e.value=t===void 0?"":t}}function ym(e,t){e._x_undoAddedClasses&&e._x_undoAddedClasses(),e._x_undoAddedClasses=Pi(e,t)}function Am(e,t){e._x_undoAddedStyles&&e._x_undoAddedStyles(),e._x_undoAddedStyles=zn(e,t)}function wm(e,t,n){mc(e,t,n),Sm(e,t,n)}function mc(e,t,n){[null,void 0,!1].includes(n)&&Nm(t)?e.removeAttribute(t):(gc(t)&&(n=t),Tm(e,t,n))}function Tm(e,t,n){e.getAttribute(t)!=n&&e.setAttribute(t,n)}function Sm(e,t,n){e[t]!==n&&(e[t]=n)}function Om(e,t){const n=[].concat(t).map(r=>r+"");Array.from(e.options).forEach(r=>{r.selected=n.includes(r.value)})}function Cm(e){return e.toLowerCase().replace(/-(\w)/g,(t,n)=>n.toUpperCase())}function Js(e,t){return e==t}function vn(e){return[1,"1","true","on","yes",!0].includes(e)?!0:[0,"0","false","off","no",!1].includes(e)?!1:e?!!e:null}var xm=new Set(["allowfullscreen","async","autofocus","autoplay","checked","controls","default","defer","disabled","formnovalidate","inert","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","selected","shadowrootclonable","shadowrootdelegatesfocus","shadowrootserializable"]);function gc(e){return xm.has(e)}function Nm(e){return!["aria-pressed","aria-checked","aria-expanded","aria-selected"].includes(e)}function Dm(e,t,n){return e._x_bindings&&e._x_bindings[t]!==void 0?e._x_bindings[t]:Ec(e,t,n)}function Rm(e,t,n,r=!0){if(e._x_bindings&&e._x_bindings[t]!==void 0)return e._x_bindings[t];if(e._x_inlineBindings&&e._x_inlineBindings[t]!==void 0){let i=e._x_inlineBindings[t];return i.extract=r,za(()=>Bt(e,i.expression))}return Ec(e,t,n)}function Ec(e,t,n){let r=e.getAttribute(t);return r===null?typeof n=="function"?n():n:r===""?!0:gc(t)?!![t,"true"].includes(r):r}function Mi(e){return e.type==="checkbox"||e.localName==="ui-checkbox"||e.localName==="ui-switch"}function bc(e){return e.type==="radio"||e.localName==="ui-radio"}function vc(e,t){var n;return function(){var r=this,i=arguments,s=function(){n=null,e.apply(r,i)};clearTimeout(n),n=setTimeout(s,t)}}function yc(e,t){let n;return function(){let r=this,i=arguments;n||(e.apply(r,i),n=!0,setTimeout(()=>n=!1,t))}}function Ac({get:e,set:t},{get:n,set:r}){let i=!0,s,o=Gt(()=>{let a=e(),c=n();if(i)r(Ar(a)),i=!1;else{let u=JSON.stringify(a),l=JSON.stringify(c);u!==s?r(Ar(a)):u!==l&&t(Ar(c))}s=JSON.stringify(e()),JSON.stringify(n())});return()=>{be(o)}}function Ar(e){return typeof e=="object"?JSON.parse(JSON.stringify(e)):e}function Lm(e){(Array.isArray(e)?e:[e]).forEach(n=>n(Ye))}var Lt={},Qs=!1;function $m(e,t){if(Qs||(Lt=Ee(Lt),Qs=!0),t===void 0)return Lt[e];Lt[e]=t,Wa(Lt[e]),typeof t=="object"&&t!==null&&t.hasOwnProperty("init")&&typeof t.init=="function"&&Lt[e].init()}function Im(){return Lt}var wc={};function Pm(e,t){let n=typeof t!="function"?()=>t:t;return e instanceof Element?Tc(e,n()):(wc[e]=n,()=>{})}function Mm(e){return Object.entries(wc).forEach(([t,n])=>{Object.defineProperty(e,t,{get(){return(...r)=>n(...r)}})}),e}function Tc(e,t,n){let r=[];for(;r.length;)r.pop()();let i=Object.entries(t).map(([o,a])=>({name:o,value:a})),s=Xa(i);return i=i.map(o=>s.find(a=>a.name===o.name)?{name:`x-bind:${o.name}`,value:`"${o.value}"`}:o),Di(e,i,n).map(o=>{r.push(o.runCleanups),o()}),()=>{for(;r.length;)r.pop()()}}var Sc={};function km(e,t){Sc[e]=t}function Fm(e,t){return Object.entries(Sc).forEach(([n,r])=>{Object.defineProperty(e,n,{get(){return(...i)=>r.bind(t)(...i)},enumerable:!1})}),e}var jm={get reactive(){return Ee},get release(){return be},get effect(){return Gt},get raw(){return Ia},version:"3.14.9",flushAndStopDeferringMutations:H_,dontAutoEvaluateFunctions:za,disableEffectScheduling:I_,startObservingMutations:Oi,stopObservingMutations:Va,setReactivityEngine:P_,onAttributeRemoved:Ba,onAttributesAdded:ja,closestDataStack:ue,skipDuringClone:Ot,onlyDuringClone:mm,addRootSelector:cc,addInitSelector:lc,interceptClone:Yn,addScopeToNode:qe,deferMutations:B_,mapAttributes:Ri,evaluateLater:B,interceptInit:sm,setEvaluator:z_,mergeProxies:ze,extractProp:Rm,findClosest:ye,onElRemoved:wi,closestRoot:qn,destroyTree:Ae,interceptor:Ka,transition:Xr,setStyles:zn,mutateDom:R,directive:I,entangle:Ac,throttle:yc,debounce:vc,evaluate:Bt,initTree:ht,nextTick:Ii,prefixed:ve,prefix:J_,plugin:Lm,magic:it,store:$m,start:rm,clone:Em,cloneNode:gm,bound:Dm,$data:Ua,watch:Pa,walk:qt,data:km,bind:Pm},Ye=jm;function Bm(e,t){const n=Object.create(null),r=e.split(",");for(let i=0;i<r.length;i++)n[r[i]]=!0;return i=>!!n[i]}var Hm=Object.freeze({}),Vm=Object.prototype.hasOwnProperty,Gn=(e,t)=>Vm.call(e,t),Ht=Array.isArray,Pe=e=>Oc(e)==="[object Map]",Um=e=>typeof e=="string",ki=e=>typeof e=="symbol",Xn=e=>e!==null&&typeof e=="object",Wm=Object.prototype.toString,Oc=e=>Wm.call(e),Cc=e=>Oc(e).slice(8,-1),Fi=e=>Um(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Km=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},qm=Km(e=>e.charAt(0).toUpperCase()+e.slice(1)),xc=(e,t)=>e!==t&&(e===e||t===t),Qr=new WeakMap,De=[],ot,Vt=Symbol("iterate"),Zr=Symbol("Map key iterate");function zm(e){return e&&e._isEffect===!0}function Ym(e,t=Hm){zm(e)&&(e=e.raw);const n=Jm(e,t);return t.lazy||n(),n}function Gm(e){e.active&&(Nc(e),e.options.onStop&&e.options.onStop(),e.active=!1)}var Xm=0;function Jm(e,t){const n=function(){if(!n.active)return e();if(!De.includes(n)){Nc(n);try{return Zm(),De.push(n),ot=n,e()}finally{De.pop(),Dc(),ot=De[De.length-1]}}};return n.id=Xm++,n.allowRecurse=!!t.allowRecurse,n._isEffect=!0,n.active=!0,n.raw=e,n.deps=[],n.options=t,n}function Nc(e){const{deps:t}=e;if(t.length){for(let n=0;n<t.length;n++)t[n].delete(e);t.length=0}}var fe=!0,ji=[];function Qm(){ji.push(fe),fe=!1}function Zm(){ji.push(fe),fe=!0}function Dc(){const e=ji.pop();fe=e===void 0?!0:e}function et(e,t,n){if(!fe||ot===void 0)return;let r=Qr.get(e);r||Qr.set(e,r=new Map);let i=r.get(n);i||r.set(n,i=new Set),i.has(ot)||(i.add(ot),ot.deps.push(i),ot.options.onTrack&&ot.options.onTrack({effect:ot,target:e,type:t,key:n}))}function At(e,t,n,r,i,s){const o=Qr.get(e);if(!o)return;const a=new Set,c=l=>{l&&l.forEach(h=>{(h!==ot||h.allowRecurse)&&a.add(h)})};if(t==="clear")o.forEach(c);else if(n==="length"&&Ht(e))o.forEach((l,h)=>{(h==="length"||h>=r)&&c(l)});else switch(n!==void 0&&c(o.get(n)),t){case"add":Ht(e)?Fi(n)&&c(o.get("length")):(c(o.get(Vt)),Pe(e)&&c(o.get(Zr)));break;case"delete":Ht(e)||(c(o.get(Vt)),Pe(e)&&c(o.get(Zr)));break;case"set":Pe(e)&&c(o.get(Vt));break}const u=l=>{l.options.onTrigger&&l.options.onTrigger({effect:l,target:e,key:n,type:t,newValue:r,oldValue:i,oldTarget:s}),l.options.scheduler?l.options.scheduler(l):l()};a.forEach(u)}var tg=Bm("__proto__,__v_isRef,__isVue"),Rc=new Set(Object.getOwnPropertyNames(Symbol).map(e=>Symbol[e]).filter(ki)),eg=Lc(),ng=Lc(!0),Zs=rg();function rg(){const e={};return["includes","indexOf","lastIndexOf"].forEach(t=>{e[t]=function(...n){const r=x(this);for(let s=0,o=this.length;s<o;s++)et(r,"get",s+"");const i=r[t](...n);return i===-1||i===!1?r[t](...n.map(x)):i}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...n){Qm();const r=x(this)[t].apply(this,n);return Dc(),r}}),e}function Lc(e=!1,t=!1){return function(r,i,s){if(i==="__v_isReactive")return!e;if(i==="__v_isReadonly")return e;if(i==="__v_raw"&&s===(e?t?gg:Mc:t?mg:Pc).get(r))return r;const o=Ht(r);if(!e&&o&&Gn(Zs,i))return Reflect.get(Zs,i,s);const a=Reflect.get(r,i,s);return(ki(i)?Rc.has(i):tg(i))||(e||et(r,"get",i),t)?a:ti(a)?!o||!Fi(i)?a.value:a:Xn(a)?e?kc(a):Ui(a):a}}var ig=sg();function sg(e=!1){return function(n,r,i,s){let o=n[r];if(!e&&(i=x(i),o=x(o),!Ht(n)&&ti(o)&&!ti(i)))return o.value=i,!0;const a=Ht(n)&&Fi(r)?Number(r)<n.length:Gn(n,r),c=Reflect.set(n,r,i,s);return n===x(s)&&(a?xc(i,o)&&At(n,"set",r,i,o):At(n,"add",r,i)),c}}function og(e,t){const n=Gn(e,t),r=e[t],i=Reflect.deleteProperty(e,t);return i&&n&&At(e,"delete",t,void 0,r),i}function ag(e,t){const n=Reflect.has(e,t);return(!ki(t)||!Rc.has(t))&&et(e,"has",t),n}function cg(e){return et(e,"iterate",Ht(e)?"length":Vt),Reflect.ownKeys(e)}var lg={get:eg,set:ig,deleteProperty:og,has:ag,ownKeys:cg},ug={get:ng,set(e,t){return console.warn(`Set operation on key "${String(t)}" failed: target is readonly.`,e),!0},deleteProperty(e,t){return console.warn(`Delete operation on key "${String(t)}" failed: target is readonly.`,e),!0}},Bi=e=>Xn(e)?Ui(e):e,Hi=e=>Xn(e)?kc(e):e,Vi=e=>e,Jn=e=>Reflect.getPrototypeOf(e);function cn(e,t,n=!1,r=!1){e=e.__v_raw;const i=x(e),s=x(t);t!==s&&!n&&et(i,"get",t),!n&&et(i,"get",s);const{has:o}=Jn(i),a=r?Vi:n?Hi:Bi;if(o.call(i,t))return a(e.get(t));if(o.call(i,s))return a(e.get(s));e!==i&&e.get(t)}function ln(e,t=!1){const n=this.__v_raw,r=x(n),i=x(e);return e!==i&&!t&&et(r,"has",e),!t&&et(r,"has",i),e===i?n.has(e):n.has(e)||n.has(i)}function un(e,t=!1){return e=e.__v_raw,!t&&et(x(e),"iterate",Vt),Reflect.get(e,"size",e)}function to(e){e=x(e);const t=x(this);return Jn(t).has.call(t,e)||(t.add(e),At(t,"add",e,e)),this}function eo(e,t){t=x(t);const n=x(this),{has:r,get:i}=Jn(n);let s=r.call(n,e);s?Ic(n,r,e):(e=x(e),s=r.call(n,e));const o=i.call(n,e);return n.set(e,t),s?xc(t,o)&&At(n,"set",e,t,o):At(n,"add",e,t),this}function no(e){const t=x(this),{has:n,get:r}=Jn(t);let i=n.call(t,e);i?Ic(t,n,e):(e=x(e),i=n.call(t,e));const s=r?r.call(t,e):void 0,o=t.delete(e);return i&&At(t,"delete",e,void 0,s),o}function ro(){const e=x(this),t=e.size!==0,n=Pe(e)?new Map(e):new Set(e),r=e.clear();return t&&At(e,"clear",void 0,void 0,n),r}function fn(e,t){return function(r,i){const s=this,o=s.__v_raw,a=x(o),c=t?Vi:e?Hi:Bi;return!e&&et(a,"iterate",Vt),o.forEach((u,l)=>r.call(i,c(u),c(l),s))}}function dn(e,t,n){return function(...r){const i=this.__v_raw,s=x(i),o=Pe(s),a=e==="entries"||e===Symbol.iterator&&o,c=e==="keys"&&o,u=i[e](...r),l=n?Vi:t?Hi:Bi;return!t&&et(s,"iterate",c?Zr:Vt),{next(){const{value:h,done:_}=u.next();return _?{value:h,done:_}:{value:a?[l(h[0]),l(h[1])]:l(h),done:_}},[Symbol.iterator](){return this}}}}function gt(e){return function(...t){{const n=t[0]?`on key "${t[0]}" `:"";console.warn(`${qm(e)} operation ${n}failed: target is readonly.`,x(this))}return e==="delete"?!1:this}}function fg(){const e={get(s){return cn(this,s)},get size(){return un(this)},has:ln,add:to,set:eo,delete:no,clear:ro,forEach:fn(!1,!1)},t={get(s){return cn(this,s,!1,!0)},get size(){return un(this)},has:ln,add:to,set:eo,delete:no,clear:ro,forEach:fn(!1,!0)},n={get(s){return cn(this,s,!0)},get size(){return un(this,!0)},has(s){return ln.call(this,s,!0)},add:gt("add"),set:gt("set"),delete:gt("delete"),clear:gt("clear"),forEach:fn(!0,!1)},r={get(s){return cn(this,s,!0,!0)},get size(){return un(this,!0)},has(s){return ln.call(this,s,!0)},add:gt("add"),set:gt("set"),delete:gt("delete"),clear:gt("clear"),forEach:fn(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(s=>{e[s]=dn(s,!1,!1),n[s]=dn(s,!0,!1),t[s]=dn(s,!1,!0),r[s]=dn(s,!0,!0)}),[e,n,t,r]}var[dg,hg,nE,rE]=fg();function $c(e,t){const n=e?hg:dg;return(r,i,s)=>i==="__v_isReactive"?!e:i==="__v_isReadonly"?e:i==="__v_raw"?r:Reflect.get(Gn(n,i)&&i in r?n:r,i,s)}var pg={get:$c(!1)},_g={get:$c(!0)};function Ic(e,t,n){const r=x(n);if(r!==n&&t.call(e,r)){const i=Cc(e);console.warn(`Reactive ${i} contains both the raw and reactive versions of the same object${i==="Map"?" as keys":""}, which can lead to inconsistencies. Avoid differentiating between the raw and reactive versions of an object and only use the reactive version if possible.`)}}var Pc=new WeakMap,mg=new WeakMap,Mc=new WeakMap,gg=new WeakMap;function Eg(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function bg(e){return e.__v_skip||!Object.isExtensible(e)?0:Eg(Cc(e))}function Ui(e){return e&&e.__v_isReadonly?e:Fc(e,!1,lg,pg,Pc)}function kc(e){return Fc(e,!0,ug,_g,Mc)}function Fc(e,t,n,r,i){if(!Xn(e))return console.warn(`value cannot be made reactive: ${String(e)}`),e;if(e.__v_raw&&!(t&&e.__v_isReactive))return e;const s=i.get(e);if(s)return s;const o=bg(e);if(o===0)return e;const a=new Proxy(e,o===2?r:n);return i.set(e,a),a}function x(e){return e&&x(e.__v_raw)||e}function ti(e){return!!(e&&e.__v_isRef===!0)}it("nextTick",()=>Ii);it("dispatch",e=>Ie.bind(Ie,e));it("watch",(e,{evaluateLater:t,cleanup:n})=>(r,i)=>{let s=t(r),a=Pa(()=>{let c;return s(u=>c=u),c},i);n(a)});it("store",Im);it("data",e=>Ua(e));it("root",e=>qn(e));it("refs",e=>(e._x_refs_proxy||(e._x_refs_proxy=ze(vg(e))),e._x_refs_proxy));function vg(e){let t=[];return ye(e,n=>{n._x_refs&&t.push(n._x_refs)}),t}var wr={};function jc(e){return wr[e]||(wr[e]=0),++wr[e]}function yg(e,t){return ye(e,n=>{if(n._x_ids&&n._x_ids[t])return!0})}function Ag(e,t){e._x_ids||(e._x_ids={}),e._x_ids[t]||(e._x_ids[t]=jc(t))}it("id",(e,{cleanup:t})=>(n,r=null)=>{let i=`${n}${r?`-${r}`:""}`;return wg(e,i,t,()=>{let s=yg(e,n),o=s?s._x_ids[n]:jc(n);return r?`${n}-${o}-${r}`:`${n}-${o}`})});Yn((e,t)=>{e._x_id&&(t._x_id=e._x_id)});function wg(e,t,n,r){if(e._x_id||(e._x_id={}),e._x_id[t])return e._x_id[t];let i=r();return e._x_id[t]=i,n(()=>{delete e._x_id[t]}),i}it("el",e=>e);Bc("Focus","focus","focus");Bc("Persist","persist","persist");function Bc(e,t,n){it(t,r=>Y(`You can't use [$${t}] without first installing the "${e}" plugin here: https://alpinejs.dev/plugins/${n}`,r))}I("modelable",(e,{expression:t},{effect:n,evaluateLater:r,cleanup:i})=>{let s=r(t),o=()=>{let l;return s(h=>l=h),l},a=r(`${t} = __placeholder`),c=l=>a(()=>{},{scope:{__placeholder:l}}),u=o();c(u),queueMicrotask(()=>{if(!e._x_model)return;e._x_removeModelListeners.default();let l=e._x_model.get,h=e._x_model.set,_=Ac({get(){return l()},set(E){h(E)}},{get(){return o()},set(E){c(E)}});i(_)})});I("teleport",(e,{modifiers:t,expression:n},{cleanup:r})=>{e.tagName.toLowerCase()!=="template"&&Y("x-teleport can only be used on a <template> tag",e);let i=io(n),s=e.content.cloneNode(!0).firstElementChild;e._x_teleport=s,s._x_teleportBack=e,e.setAttribute("data-teleport-template",!0),s.setAttribute("data-teleport-target",!0),e._x_forwardEvents&&e._x_forwardEvents.forEach(a=>{s.addEventListener(a,c=>{c.stopPropagation(),e.dispatchEvent(new c.constructor(c.type,c))})}),qe(s,{},e);let o=(a,c,u)=>{u.includes("prepend")?c.parentNode.insertBefore(a,c):u.includes("append")?c.parentNode.insertBefore(a,c.nextSibling):c.appendChild(a)};R(()=>{o(s,i,t),Ot(()=>{ht(s)})()}),e._x_teleportPutBack=()=>{let a=io(n);R(()=>{o(e._x_teleport,a,t)})},r(()=>R(()=>{s.remove(),Ae(s)}))});var Tg=document.createElement("div");function io(e){let t=Ot(()=>document.querySelector(e),()=>Tg)();return t||Y(`Cannot find x-teleport element for selector: "${e}"`),t}var Hc=()=>{};Hc.inline=(e,{modifiers:t},{cleanup:n})=>{t.includes("self")?e._x_ignoreSelf=!0:e._x_ignore=!0,n(()=>{t.includes("self")?delete e._x_ignoreSelf:delete e._x_ignore})};I("ignore",Hc);I("effect",Ot((e,{expression:t},{effect:n})=>{n(B(e,t))}));function ei(e,t,n,r){let i=e,s=c=>r(c),o={},a=(c,u)=>l=>u(c,l);if(n.includes("dot")&&(t=Sg(t)),n.includes("camel")&&(t=Og(t)),n.includes("passive")&&(o.passive=!0),n.includes("capture")&&(o.capture=!0),n.includes("window")&&(i=window),n.includes("document")&&(i=document),n.includes("debounce")){let c=n[n.indexOf("debounce")+1]||"invalid-wait",u=Dn(c.split("ms")[0])?Number(c.split("ms")[0]):250;s=vc(s,u)}if(n.includes("throttle")){let c=n[n.indexOf("throttle")+1]||"invalid-wait",u=Dn(c.split("ms")[0])?Number(c.split("ms")[0]):250;s=yc(s,u)}return n.includes("prevent")&&(s=a(s,(c,u)=>{u.preventDefault(),c(u)})),n.includes("stop")&&(s=a(s,(c,u)=>{u.stopPropagation(),c(u)})),n.includes("once")&&(s=a(s,(c,u)=>{c(u),i.removeEventListener(t,s,o)})),(n.includes("away")||n.includes("outside"))&&(i=document,s=a(s,(c,u)=>{e.contains(u.target)||u.target.isConnected!==!1&&(e.offsetWidth<1&&e.offsetHeight<1||e._x_isShown!==!1&&c(u))})),n.includes("self")&&(s=a(s,(c,u)=>{u.target===e&&c(u)})),(xg(t)||Vc(t))&&(s=a(s,(c,u)=>{Ng(u,n)||c(u)})),i.addEventListener(t,s,o),()=>{i.removeEventListener(t,s,o)}}function Sg(e){return e.replace(/-/g,".")}function Og(e){return e.toLowerCase().replace(/-(\w)/g,(t,n)=>n.toUpperCase())}function Dn(e){return!Array.isArray(e)&&!isNaN(e)}function Cg(e){return[" ","_"].includes(e)?e:e.replace(/([a-z])([A-Z])/g,"$1-$2").replace(/[_\s]/,"-").toLowerCase()}function xg(e){return["keydown","keyup"].includes(e)}function Vc(e){return["contextmenu","click","mouse"].some(t=>e.includes(t))}function Ng(e,t){let n=t.filter(s=>!["window","document","prevent","stop","once","capture","self","away","outside","passive"].includes(s));if(n.includes("debounce")){let s=n.indexOf("debounce");n.splice(s,Dn((n[s+1]||"invalid-wait").split("ms")[0])?2:1)}if(n.includes("throttle")){let s=n.indexOf("throttle");n.splice(s,Dn((n[s+1]||"invalid-wait").split("ms")[0])?2:1)}if(n.length===0||n.length===1&&so(e.key).includes(n[0]))return!1;const i=["ctrl","shift","alt","meta","cmd","super"].filter(s=>n.includes(s));return n=n.filter(s=>!i.includes(s)),!(i.length>0&&i.filter(o=>((o==="cmd"||o==="super")&&(o="meta"),e[`${o}Key`])).length===i.length&&(Vc(e.type)||so(e.key).includes(n[0])))}function so(e){if(!e)return[];e=Cg(e);let t={ctrl:"control",slash:"/",space:" ",spacebar:" ",cmd:"meta",esc:"escape",up:"arrow-up",down:"arrow-down",left:"arrow-left",right:"arrow-right",period:".",comma:",",equal:"=",minus:"-",underscore:"_"};return t[e]=e,Object.keys(t).map(n=>{if(t[n]===e)return n}).filter(n=>n)}I("model",(e,{modifiers:t,expression:n},{effect:r,cleanup:i})=>{let s=e;t.includes("parent")&&(s=e.parentNode);let o=B(s,n),a;typeof n=="string"?a=B(s,`${n} = __placeholder`):typeof n=="function"&&typeof n()=="string"?a=B(s,`${n()} = __placeholder`):a=()=>{};let c=()=>{let _;return o(E=>_=E),oo(_)?_.get():_},u=_=>{let E;o(m=>E=m),oo(E)?E.set(_):a(()=>{},{scope:{__placeholder:_}})};typeof n=="string"&&e.type==="radio"&&R(()=>{e.hasAttribute("name")||e.setAttribute("name",n)});var l=e.tagName.toLowerCase()==="select"||["checkbox","radio"].includes(e.type)||t.includes("lazy")?"change":"input";let h=yt?()=>{}:ei(e,l,t,_=>{u(Tr(e,t,_,c()))});if(t.includes("fill")&&([void 0,null,""].includes(c())||Mi(e)&&Array.isArray(c())||e.tagName.toLowerCase()==="select"&&e.multiple)&&u(Tr(e,t,{target:e},c())),e._x_removeModelListeners||(e._x_removeModelListeners={}),e._x_removeModelListeners.default=h,i(()=>e._x_removeModelListeners.default()),e.form){let _=ei(e.form,"reset",[],E=>{Ii(()=>e._x_model&&e._x_model.set(Tr(e,t,{target:e},c())))});i(()=>_())}e._x_model={get(){return c()},set(_){u(_)}},e._x_forceModelUpdate=_=>{_===void 0&&typeof n=="string"&&n.match(/\./)&&(_=""),window.fromModel=!0,R(()=>_c(e,"value",_)),delete window.fromModel},r(()=>{let _=c();t.includes("unintrusive")&&document.activeElement.isSameNode(e)||e._x_forceModelUpdate(_)})});function Tr(e,t,n,r){return R(()=>{if(n instanceof CustomEvent&&n.detail!==void 0)return n.detail!==null&&n.detail!==void 0?n.detail:n.target.value;if(Mi(e))if(Array.isArray(r)){let i=null;return t.includes("number")?i=Sr(n.target.value):t.includes("boolean")?i=vn(n.target.value):i=n.target.value,n.target.checked?r.includes(i)?r:r.concat([i]):r.filter(s=>!Dg(s,i))}else return n.target.checked;else{if(e.tagName.toLowerCase()==="select"&&e.multiple)return t.includes("number")?Array.from(n.target.selectedOptions).map(i=>{let s=i.value||i.text;return Sr(s)}):t.includes("boolean")?Array.from(n.target.selectedOptions).map(i=>{let s=i.value||i.text;return vn(s)}):Array.from(n.target.selectedOptions).map(i=>i.value||i.text);{let i;return bc(e)?n.target.checked?i=n.target.value:i=r:i=n.target.value,t.includes("number")?Sr(i):t.includes("boolean")?vn(i):t.includes("trim")?i.trim():i}}})}function Sr(e){let t=e?parseFloat(e):null;return Rg(t)?t:e}function Dg(e,t){return e==t}function Rg(e){return!Array.isArray(e)&&!isNaN(e)}function oo(e){return e!==null&&typeof e=="object"&&typeof e.get=="function"&&typeof e.set=="function"}I("cloak",e=>queueMicrotask(()=>R(()=>e.removeAttribute(ve("cloak")))));lc(()=>`[${ve("init")}]`);I("init",Ot((e,{expression:t},{evaluate:n})=>typeof t=="string"?!!t.trim()&&n(t,{},!1):n(t,{},!1)));I("text",(e,{expression:t},{effect:n,evaluateLater:r})=>{let i=r(t);n(()=>{i(s=>{R(()=>{e.textContent=s})})})});I("html",(e,{expression:t},{effect:n,evaluateLater:r})=>{let i=r(t);n(()=>{i(s=>{R(()=>{e.innerHTML=s,e._x_ignoreSelf=!0,ht(e),delete e._x_ignoreSelf})})})});Ri(Za(":",tc(ve("bind:"))));var Uc=(e,{value:t,modifiers:n,expression:r,original:i},{effect:s,cleanup:o})=>{if(!t){let c={};Mm(c),B(e,r)(l=>{Tc(e,l,i)},{scope:c});return}if(t==="key")return Lg(e,r);if(e._x_inlineBindings&&e._x_inlineBindings[t]&&e._x_inlineBindings[t].extract)return;let a=B(e,r);s(()=>a(c=>{c===void 0&&typeof r=="string"&&r.match(/\./)&&(c=""),R(()=>_c(e,t,c,n))})),o(()=>{e._x_undoAddedClasses&&e._x_undoAddedClasses(),e._x_undoAddedStyles&&e._x_undoAddedStyles()})};Uc.inline=(e,{value:t,modifiers:n,expression:r})=>{t&&(e._x_inlineBindings||(e._x_inlineBindings={}),e._x_inlineBindings[t]={expression:r,extract:!1})};I("bind",Uc);function Lg(e,t){e._x_keyExpression=t}cc(()=>`[${ve("data")}]`);I("data",(e,{expression:t},{cleanup:n})=>{if($g(e))return;t=t===""?"{}":t;let r={};Wr(r,e);let i={};Fm(i,r);let s=Bt(e,t,{scope:i});(s===void 0||s===!0)&&(s={}),Wr(s,e);let o=Ee(s);Wa(o);let a=qe(e,o);o.init&&Bt(e,o.init),n(()=>{o.destroy&&Bt(e,o.destroy),a()})});Yn((e,t)=>{e._x_dataStack&&(t._x_dataStack=e._x_dataStack,t.setAttribute("data-has-alpine-state",!0))});function $g(e){return yt?Jr?!0:e.hasAttribute("data-has-alpine-state"):!1}I("show",(e,{modifiers:t,expression:n},{effect:r})=>{let i=B(e,n);e._x_doHide||(e._x_doHide=()=>{R(()=>{e.style.setProperty("display","none",t.includes("important")?"important":void 0)})}),e._x_doShow||(e._x_doShow=()=>{R(()=>{e.style.length===1&&e.style.display==="none"?e.removeAttribute("style"):e.style.removeProperty("display")})});let s=()=>{e._x_doHide(),e._x_isShown=!1},o=()=>{e._x_doShow(),e._x_isShown=!0},a=()=>setTimeout(o),c=Gr(h=>h?o():s(),h=>{typeof e._x_toggleAndCascadeWithTransitions=="function"?e._x_toggleAndCascadeWithTransitions(e,h,o,s):h?a():s()}),u,l=!0;r(()=>i(h=>{!l&&h===u||(t.includes("immediate")&&(h?a():s()),c(h),u=h,l=!1)}))});I("for",(e,{expression:t},{effect:n,cleanup:r})=>{let i=Pg(t),s=B(e,i.items),o=B(e,e._x_keyExpression||"index");e._x_prevKeys=[],e._x_lookup={},n(()=>Ig(e,i,s,o)),r(()=>{Object.values(e._x_lookup).forEach(a=>R(()=>{Ae(a),a.remove()})),delete e._x_prevKeys,delete e._x_lookup})});function Ig(e,t,n,r){let i=o=>typeof o=="object"&&!Array.isArray(o),s=e;n(o=>{Mg(o)&&o>=0&&(o=Array.from(Array(o).keys(),d=>d+1)),o===void 0&&(o=[]);let a=e._x_lookup,c=e._x_prevKeys,u=[],l=[];if(i(o))o=Object.entries(o).map(([d,b])=>{let v=ao(t,b,d,o);r(A=>{l.includes(A)&&Y("Duplicate key on x-for",e),l.push(A)},{scope:{index:d,...v}}),u.push(v)});else for(let d=0;d<o.length;d++){let b=ao(t,o[d],d,o);r(v=>{l.includes(v)&&Y("Duplicate key on x-for",e),l.push(v)},{scope:{index:d,...b}}),u.push(b)}let h=[],_=[],E=[],m=[];for(let d=0;d<c.length;d++){let b=c[d];l.indexOf(b)===-1&&E.push(b)}c=c.filter(d=>!E.includes(d));let g="template";for(let d=0;d<l.length;d++){let b=l[d],v=c.indexOf(b);if(v===-1)c.splice(d,0,b),h.push([g,d]);else if(v!==d){let A=c.splice(d,1)[0],y=c.splice(v-1,1)[0];c.splice(d,0,y),c.splice(v,0,A),_.push([A,y])}else m.push(b);g=b}for(let d=0;d<E.length;d++){let b=E[d];b in a&&(R(()=>{Ae(a[b]),a[b].remove()}),delete a[b])}for(let d=0;d<_.length;d++){let[b,v]=_[d],A=a[b],y=a[v],T=document.createElement("div");R(()=>{y||Y('x-for ":key" is undefined or invalid',s,v,a),y.after(T),A.after(y),y._x_currentIfEl&&y.after(y._x_currentIfEl),T.before(A),A._x_currentIfEl&&A.after(A._x_currentIfEl),T.remove()}),y._x_refreshXForScope(u[l.indexOf(v)])}for(let d=0;d<h.length;d++){let[b,v]=h[d],A=b==="template"?s:a[b];A._x_currentIfEl&&(A=A._x_currentIfEl);let y=u[v],T=l[v],S=document.importNode(s.content,!0).firstElementChild,C=Ee(y);qe(S,C,s),S._x_refreshXForScope=N=>{Object.entries(N).forEach(([D,L])=>{C[D]=L})},R(()=>{A.after(S),Ot(()=>ht(S))()}),typeof T=="object"&&Y("x-for key cannot be an object, it must be a string or an integer",s),a[T]=S}for(let d=0;d<m.length;d++)a[m[d]]._x_refreshXForScope(u[l.indexOf(m[d])]);s._x_prevKeys=l})}function Pg(e){let t=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/,n=/^\s*\(|\)\s*$/g,r=/([\s\S]*?)\s+(?:in|of)\s+([\s\S]*)/,i=e.match(r);if(!i)return;let s={};s.items=i[2].trim();let o=i[1].replace(n,"").trim(),a=o.match(t);return a?(s.item=o.replace(t,"").trim(),s.index=a[1].trim(),a[2]&&(s.collection=a[2].trim())):s.item=o,s}function ao(e,t,n,r){let i={};return/^\[.*\]$/.test(e.item)&&Array.isArray(t)?e.item.replace("[","").replace("]","").split(",").map(o=>o.trim()).forEach((o,a)=>{i[o]=t[a]}):/^\{.*\}$/.test(e.item)&&!Array.isArray(t)&&typeof t=="object"?e.item.replace("{","").replace("}","").split(",").map(o=>o.trim()).forEach(o=>{i[o]=t[o]}):i[e.item]=t,e.index&&(i[e.index]=n),e.collection&&(i[e.collection]=r),i}function Mg(e){return!Array.isArray(e)&&!isNaN(e)}function Wc(){}Wc.inline=(e,{expression:t},{cleanup:n})=>{let r=qn(e);r._x_refs||(r._x_refs={}),r._x_refs[t]=e,n(()=>delete r._x_refs[t])};I("ref",Wc);I("if",(e,{expression:t},{effect:n,cleanup:r})=>{e.tagName.toLowerCase()!=="template"&&Y("x-if can only be used on a <template> tag",e);let i=B(e,t),s=()=>{if(e._x_currentIfEl)return e._x_currentIfEl;let a=e.content.cloneNode(!0).firstElementChild;return qe(a,{},e),R(()=>{e.after(a),Ot(()=>ht(a))()}),e._x_currentIfEl=a,e._x_undoIf=()=>{R(()=>{Ae(a),a.remove()}),delete e._x_currentIfEl},a},o=()=>{e._x_undoIf&&(e._x_undoIf(),delete e._x_undoIf)};n(()=>i(a=>{a?s():o()})),r(()=>e._x_undoIf&&e._x_undoIf())});I("id",(e,{expression:t},{evaluate:n})=>{n(t).forEach(i=>Ag(e,i))});Yn((e,t)=>{e._x_ids&&(t._x_ids=e._x_ids)});Ri(Za("@",tc(ve("on:"))));I("on",Ot((e,{value:t,modifiers:n,expression:r},{cleanup:i})=>{let s=r?B(e,r):()=>{};e.tagName.toLowerCase()==="template"&&(e._x_forwardEvents||(e._x_forwardEvents=[]),e._x_forwardEvents.includes(t)||e._x_forwardEvents.push(t));let o=ei(e,t,n,a=>{s(()=>{},{scope:{$event:a},params:[a]})});i(()=>o())}));Qn("Collapse","collapse","collapse");Qn("Intersect","intersect","intersect");Qn("Focus","trap","focus");Qn("Mask","mask","mask");function Qn(e,t,n){I(t,r=>Y(`You can't use [x-${t}] without first installing the "${e}" plugin here: https://alpinejs.dev/plugins/${n}`,r))}Ye.setEvaluator(Ga);Ye.setReactivityEngine({reactive:Ui,effect:Ym,release:Gm,raw:x});var kg=Ye,Kc=kg;window.Alpine=Kc;Kc.start();
