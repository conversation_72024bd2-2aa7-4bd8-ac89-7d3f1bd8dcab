<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Contact Us') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="flex flex-col md:flex-row">
                        <div class="md:w-1/2 p-4">
                            <h1 class="text-3xl font-bold text-gray-800 mb-4">Get in Touch</h1>
                            <p class="text-gray-600 mb-6">
                                Have questions about our courses or need assistance? Fill out the form and our team will get back to you as soon as possible.
                            </p>
                            
                            <form method="POST" action="{{ route('contact.submit') }}">
                                @csrf
                                
                                <div class="mb-4">
                                    <label for="name" class="block text-gray-700 text-sm font-bold mb-2">Name</label>
                                    <input type="text" name="name" id="name" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                                </div>
                                
                                <div class="mb-4">
                                    <label for="email" class="block text-gray-700 text-sm font-bold mb-2">Email</label>
                                    <input type="email" name="email" id="email" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                                </div>
                                
                                <div class="mb-4">
                                    <label for="subject" class="block text-gray-700 text-sm font-bold mb-2">Subject</label>
                                    <input type="text" name="subject" id="subject" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                                </div>
                                
                                <div class="mb-6">
                                    <label for="message" class="block text-gray-700 text-sm font-bold mb-2">Message</label>
                                    <textarea name="message" id="message" rows="5" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required></textarea>
                                </div>
                                
                                <div class="flex items-center justify-between">
                                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                                        Send Message
                                    </button>
                                </div>
                            </form>
                        </div>
                        <div class="md:w-1/2 p-4">
                            <div class="mb-8">
                                <h2 class="text-xl font-semibold text-gray-800 mb-4">Contact Information</h2>
                                <div class="flex items-start mb-4">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                    <div>
                                        <p class="font-semibold">Address:</p>
                                        <p class="text-gray-600">123 Soroban Street, Dubai, UAE</p>
                                    </div>
                                </div>
                                <div class="flex items-start mb-4">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                    </svg>
                                    <div>
                                        <p class="font-semibold">Email:</p>
                                        <p class="text-gray-600"><EMAIL></p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                    </svg>
                                    <div>
                                        <p class="font-semibold">Phone:</p>
                                        <p class="text-gray-600">+971 4 123 4567</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <h2 class="text-xl font-semibold text-gray-800 mb-4">Business Hours</h2>
                                <div class="flex justify-between mb-2">
                                    <span class="text-gray-600">Monday - Friday:</span>
                                    <span class="text-gray-800">9:00 AM - 6:00 PM</span>
                                </div>
                                <div class="flex justify-between mb-2">
                                    <span class="text-gray-600">Saturday:</span>
                                    <span class="text-gray-800">10:00 AM - 4:00 PM</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Sunday:</span>
                                    <span class="text-gray-800">Closed</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Map -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Our Location</h2>
                    <div class="h-96 bg-gray-200">
                        <!-- Replace with actual map embed code -->
                        <div class="w-full h-full flex items-center justify-center">
                            <p class="text-gray-500">Map Placeholder - Replace with Google Maps or other map service</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>