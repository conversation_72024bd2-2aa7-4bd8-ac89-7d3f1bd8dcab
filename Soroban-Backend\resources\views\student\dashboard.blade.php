@extends('layouts.app')

@section('title', 'Student Dashboard')

@section('content')
<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 bg-white border-b border-gray-200">
                <h1 class="text-3xl font-bold text-gray-800 mb-6">Student Dashboard</h1>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                        <h2 class="text-xl font-semibold text-gray-800 mb-3">Upcoming Quizzes</h2>
                        <ul class="list-disc pl-5 space-y-2">
                            @foreach($upcomingQuizzes as $quiz)
                                <li><a href="{{ route('student.quizzes.show', $quiz) }}">{{ $quiz->name }}</a></li>
                            @endforeach
                        </ul>
                    </div>
                    
                    <div>
                        <h2 class="text-xl font-semibold text-gray-800 mb-3">Earned Certificates</h2>
                        <ul class="list-disc pl-5 space-y-2">
                            @foreach($earnedCertificates as $certificate)
                                <li><a href="{{ route('student.certificates.download', $certificate) }}">{{ $certificate->name }}</a></li>
                            @endforeach
                        </ul>
                    </div>

                    <div>
                        <h2 class="text-xl font-semibold text-gray-800 mb-3">Progress</h2>
                        <p>Completion Rate: {{ $completionRate }}%</p>
                        <p>Points Earned: {{ $pointsEarned }}</p>
                    </div>
                    
                    <div>
                        <h2 class="text-xl font-semibold text-gray-800 mb-3">Competitions</h2>
                        <p>Rank: {{ $competitionRank }}</p>
                        <p>Points: {{ $competitionPoints }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection