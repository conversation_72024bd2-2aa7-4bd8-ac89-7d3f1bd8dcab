@echo off
echo Setting up MySQL database for Soroban Educational Platform...
echo.

echo Step 1: Creating database and tables...
mysql -u root -P 3307 < database/complete_schema.sql

if %errorlevel% equ 0 (
    echo Database schema created successfully!
    echo.
    echo Step 2: Running Laravel migrations...
    php artisan migrate:status
    echo.
    echo Step 3: If needed, run fresh migrations...
    echo To run fresh migrations, execute: php artisan migrate:fresh
    echo.
    echo Setup completed successfully!
) else (
    echo Error: Failed to create database schema.
    echo Please check your MySQL connection and credentials.
    echo Make sure MySQL is running and the credentials in .env are correct.
)

pause
