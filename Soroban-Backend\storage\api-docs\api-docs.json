{"openapi": "3.0.0", "info": {"title": "Soroban-AlBarq API", "description": "Educational Platform API for Soroban mental math learning", "contact": {"email": "<EMAIL>"}, "version": "1.0.0"}, "servers": [{"url": "http://localhost:8000/api", "description": "Local Development Server"}, {"url": "https://api.soroban-albarq.com", "description": "Production Server"}], "paths": {"/register": {"post": {"tags": ["Authentication"], "summary": "Register a new user", "description": "Register a new user and automatically send email verification", "operationId": "5d4fa5d5607ed675ba93907e1fc94534", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["first_name", "second_name", "email", "password", "password_confirmation", "age", "role"], "properties": {"first_name": {"type": "string", "example": "<PERSON>"}, "second_name": {"type": "string", "example": "<PERSON>"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "password": {"type": "string", "format": "password", "example": "password123"}, "password_confirmation": {"type": "string", "format": "password", "example": "password123"}, "age": {"type": "integer", "example": 25}, "role": {"type": "string", "enum": ["student", "teacher", "admin", "superAdmin", "guest"], "example": "student"}, "age_group": {"type": "string", "enum": ["kids", "adults"], "example": "adults"}, "phone": {"type": "string", "example": "+**********"}, "birth_date": {"type": "string", "format": "date", "example": "1999-01-01"}, "address": {"type": "string", "example": "123 Main Street"}}, "type": "object"}}}}, "responses": {"201": {"description": "User registered successfully", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "User registered successfully. Please check your email to verify your account."}, "data": {"properties": {"user": {"type": "object"}, "token": {"type": "string"}, "token_type": {"type": "string", "example": "Bearer"}, "email_verification_sent": {"type": "boolean", "example": true}}, "type": "object"}}, "type": "object"}}}}, "422": {"description": "Validation errors", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Validation errors"}, "errors": {"type": "object"}}, "type": "object"}}}}}}}, "/login": {"post": {"tags": ["Authentication"], "summary": "User login", "description": "Authenticate user and return access token", "operationId": "67d6d0fdd54fba132e1b6f55ca254183", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["email", "password"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "password": {"type": "string", "format": "password", "example": "password123"}}, "type": "object"}}}}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Login successful"}, "data": {"properties": {"user": {"type": "object"}, "token": {"type": "string"}, "token_type": {"type": "string", "example": "Bearer"}}, "type": "object"}}, "type": "object"}}}}, "401": {"description": "Invalid credentials", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Invalid credentials"}}, "type": "object"}}}}}}}, "/logout": {"post": {"tags": ["Authentication"], "summary": "User logout", "description": "Logout user and revoke access token", "operationId": "28d48c13fca984400ac8de99f54ee13a", "responses": {"200": {"description": "Logout successful", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Logged out successfully"}}, "type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Unauthenticated."}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/forgot-password": {"post": {"tags": ["Authentication"], "summary": "Send password reset email", "description": "Send a password reset link to the user's email address", "operationId": "f46fe87b31c0dadd30642d353886e6f1", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["email"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}}, "type": "object"}}}}, "responses": {"200": {"description": "Password reset link sent", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Password reset link sent to your email address"}}, "type": "object"}}}}, "422": {"description": "Validation errors", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Validation errors"}, "errors": {"type": "object"}}, "type": "object"}}}}}}}, "/email/verification-notification": {"post": {"tags": ["Email Verification"], "summary": "Resend email verification", "description": "Resend email verification notification to the authenticated user", "operationId": "747fffb0acc817885968dbbc85b8db17", "responses": {"200": {"description": "Verification email sent", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Verification email sent"}}, "type": "object"}}}}, "400": {"description": "Email already verified", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Email already verified"}}, "type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Unauthenticated."}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/courses": {"get": {"tags": ["Courses"], "summary": "Get all courses", "description": "Retrieve a paginated list of all courses with their levels", "operationId": "75403048c36d79fe464ce4716c204c61", "parameters": [{"name": "search", "in": "query", "description": "Search courses by name or description", "required": false, "schema": {"type": "string"}}, {"name": "per_page", "in": "query", "description": "Number of courses per page", "required": false, "schema": {"type": "integer", "default": 15}}], "responses": {"200": {"description": "Courses retrieved successfully", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"properties": {"data": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Soroban Fundamentals"}, "description": {"type": "string", "example": "Learn the basics of Soroban calculation"}, "levels": {"type": "array", "items": {"type": "object"}}}, "type": "object"}}, "current_page": {"type": "integer"}, "total": {"type": "integer"}}, "type": "object"}}, "type": "object"}}}}}}}, "/subscriptions": {"get": {"tags": ["Subscriptions"], "summary": "Get all subscriptions", "description": "Retrieve a paginated list of subscriptions with filtering options", "operationId": "b6cad0cd2df2d41298b989a858d779db", "parameters": [{"name": "student_id", "in": "query", "description": "Filter by student ID", "required": false, "schema": {"type": "integer"}}, {"name": "status", "in": "query", "description": "Filter by renewal status", "required": false, "schema": {"type": "string", "enum": ["active", "expired", "cancelled"]}}], "responses": {"200": {"description": "Subscriptions retrieved successfully", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/users": {"get": {"tags": ["Users"], "summary": "Get all users", "description": "Retrieve a paginated list of users with filtering options", "operationId": "7208c29037df2b8bb02499ba39ed8a78", "parameters": [{"name": "role", "in": "query", "description": "Filter by user role", "required": false, "schema": {"type": "string", "enum": ["student", "teacher", "admin", "superAdmin", "guest"]}}, {"name": "status", "in": "query", "description": "Filter by account status", "required": false, "schema": {"type": "string", "enum": ["active", "inactive"]}}, {"name": "search", "in": "query", "description": "Search users by name or email", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Users retrieved successfully", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object"}}, "type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Unauthenticated."}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "description": "Enter token in format: Bear<PERSON> {token}", "bearerFormat": "JWT", "scheme": "bearer"}}}, "tags": [{"name": "Authentication", "description": "Authentication"}, {"name": "Email Verification", "description": "Email Verification"}, {"name": "Courses", "description": "Courses"}, {"name": "Subscriptions", "description": "Subscriptions"}, {"name": "Users", "description": "Users"}]}