<?php

// Simple login test script
$baseUrl = 'http://localhost:8000/api';

function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        $headers[] = 'Content-Type: application/json';
    }
    
    $headers[] = 'Accept: application/json';
    
    if (!empty($headers)) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'status' => $httpCode,
        'body' => json_decode($response, true)
    ];
}

echo "🧪 Testing Login API...\n";
echo "========================\n\n";

// Test with test user
$testCredentials = [
    ['email' => '<EMAIL>', 'password' => 'password'],
];

foreach ($testCredentials as $credentials) {
    echo "🔐 Testing login with: {$credentials['email']}\n";
    
    $response = makeRequest($baseUrl . '/login', 'POST', $credentials);
    
    echo "Status: {$response['status']}\n";
    
    if ($response['status'] === 200 && isset($response['body']['success']) && $response['body']['success']) {
        echo "✅ Login successful!\n";
        echo "User: {$response['body']['data']['user']['first_name']} {$response['body']['data']['user']['second_name']}\n";
        echo "Role: {$response['body']['data']['user']['role']}\n";
        echo "Token: " . substr($response['body']['data']['token'], 0, 20) . "...\n";
        break;
    } else {
        echo "❌ Login failed: " . ($response['body']['message'] ?? 'Unknown error') . "\n";
    }
    echo "\n";
}

echo "\n🎉 Login test complete!\n";
?>
