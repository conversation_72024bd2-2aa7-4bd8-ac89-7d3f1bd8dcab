<?php
// Test MySQL connection with the same credentials as Laravel

$host = '127.0.0.1';
$port = '3307';
$database = 'soroban_albark';
$username = 'root';
$password = ''; // XAMPP default - no password

echo "Testing MySQL connection...\n";
echo "Host: $host\n";
echo "Port: $port\n";
echo "Database: $database\n";
echo "Username: $username\n";
echo "Password: " . str_repeat('*', strlen($password)) . "\n\n";

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "✅ Connection successful!\n";
    
    // Test database existence
    $stmt = $pdo->query("SELECT DATABASE() as current_db");
    $result = $stmt->fetch();
    echo "✅ Current database: " . $result['current_db'] . "\n";
    
    // List tables
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "📋 Tables in database (" . count($tables) . "):\n";
    foreach ($tables as $table) {
        echo "   - $table\n";
    }
    
    if (empty($tables)) {
        echo "   (No tables found - database is empty)\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Connection failed: " . $e->getMessage() . "\n";
    echo "\nTroubleshooting tips:\n";
    echo "1. Check if MySQL is running\n";
    echo "2. Verify the port number (common ports: 3306, 3307)\n";
    echo "3. Check username and password\n";
    echo "4. Ensure the database 'soroban_albark' exists\n";
    echo "5. Check MySQL user permissions\n";
}
?>
