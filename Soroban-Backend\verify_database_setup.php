<?php
// Verify complete database setup for Soroban Educational Platform

echo "🔍 Verifying Soroban Educational Platform Database Setup\n";
echo "=" . str_repeat("=", 60) . "\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;port=3307;dbname=soroban_albark;charset=utf8mb4', 'root', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "✅ Database Connection: SUCCESS\n\n";
    
    // Expected tables from the schema
    $expectedTables = [
        'users', 'user_profiles', 'companies', 'courses', 'course_levels',
        'attachments', 'lessons', 'quizzes', 'exams', 'student_exams',
        'student_progress', 'discounts', 'subscriptions', 'payments',
        'groups', 'group_members', 'certificates', 'competitions',
        'competition_participants', 'notifications', 'whatsapp_link_history'
    ];
    
    // Get actual tables
    $stmt = $pdo->query("SHOW TABLES");
    $actualTables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "📋 Table Verification:\n";
    $missingTables = [];
    foreach ($expectedTables as $table) {
        if (in_array($table, $actualTables)) {
            echo "   ✅ $table\n";
        } else {
            echo "   ❌ $table (MISSING)\n";
            $missingTables[] = $table;
        }
    }
    
    if (empty($missingTables)) {
        echo "\n🎉 All expected tables are present!\n\n";
    } else {
        echo "\n⚠️  Missing tables: " . implode(', ', $missingTables) . "\n\n";
    }
    
    // Check foreign key relationships
    echo "🔗 Foreign Key Relationships:\n";
    $stmt = $pdo->query("
        SELECT 
            TABLE_NAME, 
            COLUMN_NAME, 
            REFERENCED_TABLE_NAME, 
            REFERENCED_COLUMN_NAME
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
        WHERE REFERENCED_TABLE_SCHEMA = 'soroban_albark'
        ORDER BY TABLE_NAME, COLUMN_NAME
    ");
    $relationships = $stmt->fetchAll();
    
    echo "   Found " . count($relationships) . " foreign key relationships:\n";
    foreach ($relationships as $rel) {
        echo "   - {$rel['TABLE_NAME']}.{$rel['COLUMN_NAME']} → {$rel['REFERENCED_TABLE_NAME']}.{$rel['REFERENCED_COLUMN_NAME']}\n";
    }
    
    // Check constraints
    echo "\n🛡️  Constraints:\n";
    $stmt = $pdo->query("
        SELECT CONSTRAINT_NAME, CONSTRAINT_TYPE 
        FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS 
        WHERE CONSTRAINT_SCHEMA = 'soroban_albark' 
        AND CONSTRAINT_TYPE = 'CHECK'
    ");
    $constraints = $stmt->fetchAll();
    
    if (!empty($constraints)) {
        foreach ($constraints as $constraint) {
            echo "   ✅ {$constraint['CONSTRAINT_NAME']} ({$constraint['CONSTRAINT_TYPE']})\n";
        }
    } else {
        echo "   ℹ️  No CHECK constraints found (may not be supported in this MySQL version)\n";
    }
    
    // Test basic functionality
    echo "\n🧪 Basic Functionality Test:\n";
    
    // Test inserting a company
    $stmt = $pdo->prepare("INSERT INTO companies (name, contact_email) VALUES (?, ?)");
    $stmt->execute(['Test Company', '<EMAIL>']);
    $companyId = $pdo->lastInsertId();
    echo "   ✅ Company insertion: SUCCESS (ID: $companyId)\n";
    
    // Test inserting a user
    $stmt = $pdo->prepare("INSERT INTO users (first_name, second_name, email, password, age, role, company_id) VALUES (?, ?, ?, ?, ?, ?, ?)");
    $stmt->execute(['Test', 'User', '<EMAIL>', 'password123', 25, 'student', $companyId]);
    $userId = $pdo->lastInsertId();
    echo "   ✅ User insertion: SUCCESS (ID: $userId)\n";
    
    // Test foreign key relationship
    $stmt = $pdo->prepare("SELECT u.first_name, u.second_name, c.name as company_name FROM users u LEFT JOIN companies c ON u.company_id = c.id WHERE u.id = ?");
    $stmt->execute([$userId]);
    $result = $stmt->fetch();
    echo "   ✅ Foreign key relationship: SUCCESS ({$result['first_name']} {$result['second_name']} → {$result['company_name']})\n";
    
    // Clean up test data
    $pdo->prepare("DELETE FROM users WHERE id = ?")->execute([$userId]);
    $pdo->prepare("DELETE FROM companies WHERE id = ?")->execute([$companyId]);
    echo "   ✅ Test data cleanup: SUCCESS\n";
    
    echo "\n🎉 DATABASE SETUP VERIFICATION COMPLETE!\n";
    echo "✅ Your Soroban Educational Platform database is ready for use.\n";
    echo "✅ All tables, relationships, and constraints are properly configured.\n";
    echo "✅ MySQL connection on port 3307 is working correctly.\n\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
