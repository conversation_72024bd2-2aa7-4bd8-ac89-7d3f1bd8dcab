# Soroban Frontend Project Instructions

This document provides essential context for AI agents working with this Vue 3 + Vite codebase.

## Project Architecture

- **Framework**: Vue 3 with TypeScript and Vite
- **Styling**: TailwindCSS + Shadcn UI components
- **Routing**: Vue Router (configured in `src/router/index.ts`)
- **Icons**: FontAwesome and Lucide Vue

### Key Directories

- `src/screens/` - Main view components organized by feature
- `src/components/` - Reusable UI components
- `src/lib/` - Shared utilities and business logic
- `src/locales/` - Internationalization files
- `src/router/` - Route configurations

## Project Patterns

### Screen Organization
- Screens are organized in feature-based directories (e.g., `Auth`, `Competition`, `Courses`)
- Each major section in the home page has its own component in `screens/Home/sections/`
- Screen components follow the `.vue` extension convention

### Component Structure
- Components use the Composition API with `<script setup lang="ts">`
- Shadcn UI components are used for consistent styling
- FontAwesome icons are used via the `@fortawesome` packages

## Development Workflow

### Setup and Running
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build
```

### Development Server
- Runs on port 5173 by default
- Uses Vite for fast HMR (Hot Module Replacement)

## File Naming and Structure
- Vue components use PascalCase (e.g., `NavigationBarSection.vue`)
- Screen components have their own directories with index files
- Utility functions are organized in `lib/` with descriptive names

## Common Patterns
- Route guards and authentication logic in router configuration
- Screen-specific components are co-located with their screens
- Reusable components are placed in the global `components/` directory

Remember to follow these conventions when making changes to maintain consistency across the codebase.
