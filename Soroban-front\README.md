# Soroban Learning Platform - Frontend

A modern Vue.js frontend for the Soroban (Japanese Abacus) learning platform.

## 🚀 Features

- **Modern Vue.js 3** with Composition API and TypeScript
- **Role-based Authentication** with Laravel Sanctum integration
- **Responsive Design** with Tailwind CSS
- **Multi-language Support** with i18n
- **Role-specific Dashboards** for Students, Teachers, Admins, and Super Admins
- **Course Management** with lessons, quizzes, and progress tracking
- **Real-time Updates** and notifications
- **Mobile-first Design** with PWA capabilities

## 🛠️ Technology Stack

- **Framework**: Vue.js 3 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **Icons**: Lucide Vue Next & FontAwesome
- **Routing**: Vue Router 4
- **State Management**: Composables with reactive state
- **HTTP Client**: Native Fetch API with custom service layer

## 📋 Prerequisites

- Node.js 18+ 
- npm or yarn
- Running Laravel backend API (see `../Soroban-Backend`)

## 🚀 Quick Start

### 1. Install Dependencies
```bash
npm install
```

### 2. Environment Configuration
```bash
cp .env.example .env
```

Update `.env` with your backend API URL:
```env
VITE_API_BASE_URL=http://localhost:8000/api
VITE_BACKEND_URL=http://localhost:8000
```

### 3. Start Development Server
```bash
npm run dev
```

The application will be available at `http://localhost:5173`

### 4. Build for Production
```bash
npm run build
```

## 🏗️ Project Structure

```
src/
├── components/          # Reusable Vue components
├── composables/         # Vue composables for shared logic
├── screens/            # Page components organized by feature
│   ├── Auth/           # Authentication pages
│   ├── Home/           # Landing page
│   ├── Courses/        # Course-related pages
│   ├── StudentDashboard/
│   ├── TeacherDashboard/
│   ├── AdminDashboard/
│   └── SuperAdminDashboard/
├── services/           # API service layer
├── router/             # Vue Router configuration
├── locales/            # Internationalization
└── lib/                # Utility functions
```

## 🔐 Authentication

The frontend uses Laravel Sanctum for authentication:

- **Token-based Authentication** with Bearer tokens
- **Role-based Access Control** with route guards
- **Automatic Token Management** with localStorage
- **Protected Routes** based on user roles

### User Roles
- **Student**: Access to courses, progress tracking, certificates
- **Teacher**: Course creation, student management, grading
- **Admin**: User management, course oversight, reports
- **Super Admin**: Full system access, company management

## 🎨 Styling

- **Tailwind CSS** for utility-first styling
- **Responsive Design** with mobile-first approach
- **Dark Mode Support** (configurable)
- **Custom Components** with consistent design system

## 🌐 API Integration

The frontend communicates with the Laravel backend through:

- **Centralized API Service** (`src/services/api.ts`)
- **Type-safe Interfaces** for all API responses
- **Error Handling** with user-friendly messages
- **Automatic Token Refresh** and logout on expiry

## 🧪 Development

### Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run type-check` - Run TypeScript type checking

### Code Style
- **TypeScript** for type safety
- **Composition API** for better code organization
- **Single File Components** with `<script setup>`
- **Consistent Naming** following Vue.js conventions

## 🔧 Configuration

### Environment Variables
- `VITE_API_BASE_URL` - Backend API base URL
- `VITE_BACKEND_URL` - Backend server URL
- `VITE_APP_NAME` - Application name
- `VITE_APP_ENV` - Environment (development/production)

### Router Configuration
- **Route Guards** for authentication and authorization
- **Role-based Redirects** to appropriate dashboards
- **Lazy Loading** for better performance

## 📱 Features by Role

### Student Dashboard
- Course enrollment and progress
- Lesson viewing and completion
- Quiz and exam taking
- Certificate downloads
- Progress analytics

### Teacher Dashboard
- Course creation and management
- Student progress monitoring
- Grade management
- Content upload

### Admin Dashboard
- User management
- Course oversight
- System reports
- Payment management

### Super Admin Dashboard
- Full system control
- Company management
- Advanced analytics
- System configuration

## 🚀 Deployment

### Production Build
```bash
npm run build
```

### Deploy to Static Hosting
The built files in `dist/` can be deployed to any static hosting service:
- Netlify
- Vercel
- GitHub Pages
- AWS S3 + CloudFront

### Environment Configuration
Ensure production environment variables are set:
```env
VITE_API_BASE_URL=https://your-api-domain.com/api
VITE_BACKEND_URL=https://your-api-domain.com
```

## 📞 Support

For support and questions, please refer to the project documentation or contact the development team.

## 📄 License

This project is proprietary software. All rights reserved.
