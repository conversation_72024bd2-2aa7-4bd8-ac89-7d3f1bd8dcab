{"version": "1.0.0", "source": "./index.html", "type": "module", "name": "soroban-frontend", "description": "Soroban Learning Platform - Vue.js Frontend", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "class-variance-authority": "^0.7.0", "clsx": "2.1.1", "lucide-vue-next": "^0.453.0", "tailwind-merge": "2.5.4", "vue": "^3.4.0", "vue-router": "^4.5.1"}, "devDependencies": {"@animaapp/vite-plugin-screen-graph": "^0.1.5", "@types/node": "^20.0.0", "@vitejs/plugin-vue": "^5.0.0", "autoprefixer": "^10.4.21", "esbuild": "0.24.0", "globals": "15.12.0", "tailwindcss": "3.4.16", "typescript": "^5.0.0", "vite": "6.0.4", "vue-tsc": "^1.8.0"}}