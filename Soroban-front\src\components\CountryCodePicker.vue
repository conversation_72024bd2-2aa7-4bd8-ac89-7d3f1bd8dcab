<template>
  <div class="relative w-full" ref="dropdownRef">
    <button
      type="button"
      @click="isOpen = !isOpen"
      class="form-select-country w-full flex justify-between items-center"
    >
      <span class="truncate text-gray-900 dark:text-white">{{ selectedCountry.name }} ({{ selectedCountry.dial_code }})</span>
      <svg class="w-5 h-5 text-gray-400 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M10 3a.75.75 0 01.53.22l3.5 3.5a.75.75 0 01-1.06 1.06L10 4.81 6.53 8.28a.75.75 0 01-1.06-1.06l3.5-3.5A.75.75 0 0110 3zM10 17a.75.75 0 01-.53-.22l-3.5-3.5a.75.75 0 011.06-1.06L10 15.19l3.47-3.47a.75.75 0 011.06 1.06l-3.5 3.5A.75.75 0 0110 17z" clip-rule="evenodd" />
      </svg>
    </button>

    <div
      v-if="isOpen"
      class="absolute z-10 mt-1 w-full bg-white dark:bg-gray-800 shadow-lg rounded-md border border-gray-200 dark:border-gray-700"
    >
      <div class="p-2">
        <input
          v-model="searchTerm"
          type="text"
          :placeholder="t('register.searchCountry')"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400"
        />
      </div>
      <ul class="max-h-60 overflow-y-auto">
        <li
          v-for="country in filteredCountries"
          :key="country.code"
          @click="selectCountry(country)"
          class="px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer text-sm text-gray-900 dark:text-white"
        >
          {{ country.name }} ({{ country.dial_code }})
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue';
import { countries } from '../lib/countries';
import { t } from '../locales';

interface Country {
  name: string;
  dial_code: string;
  code: string;
}

const props = defineProps<{
  modelValue: string;
}>();

const emit = defineEmits(['update:modelValue']);

const isOpen = ref(false);
const searchTerm = ref('');
const dropdownRef = ref<HTMLElement | null>(null);

const selectedCountry = computed(() => {
  return countries.find(c => c.dial_code === props.modelValue) || countries.find(c => c.code === 'DZ')!;
});

const filteredCountries = computed(() => {
  if (!searchTerm.value) {
    return countries;
  }
  return countries.filter(country =>
    country.name.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
    country.dial_code.includes(searchTerm.value)
  );
});

function selectCountry(country: Country) {
  emit('update:modelValue', country.dial_code);
  isOpen.value = false;
  searchTerm.value = '';
}

function handleClickOutside(event: MouseEvent) {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    isOpen.value = false;
  }
}

onMounted(() => {
  document.addEventListener('mousedown', handleClickOutside);
});

onBeforeUnmount(() => {
  document.removeEventListener('mousedown', handleClickOutside);
});

</script>

<style scoped>
.form-select-country {
  @apply bg-gray-50 dark:bg-gray-700 border border-solid border-gray-300 dark:border-gray-600 px-3 py-4 rounded-lg text-gray-900 dark:text-white;
  /* Removed rounded-l-lg and border-r-0 as it's now a standalone component */
}
</style> 