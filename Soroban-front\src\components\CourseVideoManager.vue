<template>
  <div class="course-video-manager">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h2 class="text-2xl font-bold text-gray-900">Course Videos</h2>
        <p class="text-gray-600">Manage YouTube playlists and video lessons</p>
      </div>
      <button 
        @click="showCreateModal = true"
        class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors flex items-center gap-2"
      >
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
        </svg>
        Add Video Lesson
      </button>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow p-4 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Course</label>
          <select v-model="filters.course_id" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
            <option value="">All Courses</option>
            <option v-for="course in courses" :key="course.id" :value="course.id">
              {{ course.name }}
            </option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Level</label>
          <select v-model="filters.course_level_id" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
            <option value="">All Levels</option>
            <option v-for="level in filteredLevels" :key="level.id" :value="level.id">
              {{ level.title }}
            </option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Access Type</label>
          <select v-model="filters.requires_payment" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
            <option value="">All Videos</option>
            <option value="false">Free Videos</option>
            <option value="true">Premium Videos</option>
          </select>
        </div>
        <div class="flex items-end">
          <button 
            @click="applyFilters"
            class="w-full bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors"
          >
            Apply Filters
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
      <div class="text-red-500 text-lg mb-4">{{ error }}</div>
      <button @click="fetchVideoLessons()" class="bg-red-500 text-white px-6 py-3 rounded-lg hover:bg-red-600 transition-colors">
        Try Again
      </button>
    </div>

    <!-- Video Lessons Grid -->
    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div v-for="video in videos" :key="video.id" class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
        <!-- Video Thumbnail -->
        <div class="relative aspect-video bg-gray-200">
          <div v-if="video.youtube_playlist_id" class="absolute inset-0 bg-gradient-to-br from-red-500 to-red-600 flex items-center justify-center">
            <svg class="w-16 h-16 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
            </svg>
          </div>
          <div v-else class="absolute inset-0 bg-gray-300 flex items-center justify-center">
            <svg class="w-12 h-12 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
            </svg>
          </div>
          
          <!-- Access Badge -->
          <div class="absolute top-2 right-2">
            <span v-if="video.requires_payment" class="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium">
              Premium
            </span>
            <span v-else class="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
              Free
            </span>
          </div>

          <!-- Day/Sequence Badge -->
          <div class="absolute top-2 left-2">
            <span class="bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs font-medium">
              Day {{ video.day_number }} - {{ video.sequence }}
            </span>
          </div>
        </div>

        <!-- Video Info -->
        <div class="p-4">
          <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">{{ video.title }}</h3>
          <p class="text-sm text-gray-600 mb-3">
            {{ video.course_level?.course?.name }} - {{ video.course_level?.title }}
          </p>
          
          <!-- Video Stats -->
          <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
            <span>{{ formatDate(video.created_at) }}</span>
            <div class="flex items-center gap-2">
              <span v-if="video.youtube_playlist_id" class="text-green-600">✓ YouTube</span>
              <span v-if="video.pdf_attachment_id" class="text-blue-600">📄 PDF</span>
              <span v-if="video.quiz_id" class="text-purple-600">❓ Quiz</span>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex gap-2">
            <button 
              @click="viewVideo(video)"
              class="flex-1 bg-blue-500 text-white px-3 py-2 rounded-lg hover:bg-blue-600 transition-colors text-sm"
            >
              View
            </button>
            <button 
              @click="editVideo(video)"
              class="bg-gray-500 text-white px-3 py-2 rounded-lg hover:bg-gray-600 transition-colors text-sm"
            >
              Edit
            </button>
            <button 
              @click="deleteVideo(video)"
              class="bg-red-500 text-white px-3 py-2 rounded-lg hover:bg-red-600 transition-colors text-sm"
            >
              Delete
            </button>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="videos.length === 0" class="col-span-full text-center py-12">
        <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
        </svg>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No Video Lessons Found</h3>
        <p class="text-gray-600 mb-4">Get started by creating your first video lesson.</p>
        <button 
          @click="showCreateModal = true"
          class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors"
        >
          Create Video Lesson
        </button>
      </div>
    </div>

    <!-- Create/Edit Modal -->
    <VideoLessonModal
      :isOpen="showCreateModal || showEditModal"
      :lesson="selectedVideo"
      :courses="courses"
      @close="closeModal"
      @save="handleSave"
    />

    <!-- View Modal -->
    <VideoViewModal
      :isOpen="showViewModal"
      :lesson="selectedVideo"
      @close="showViewModal = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useVideos, type VideoLesson } from '../composables/useVideos'
import { useCourses } from '../composables/useCourses'
import VideoLessonModal from './VideoLessonModal.vue'
import VideoViewModal from './VideoViewModal.vue'

// Composables
const {
  videos,
  isLoading,
  error,
  fetchVideoLessons,
  deleteVideoLesson
} = useVideos()

const { courses, courseLevels, fetchCourses, fetchCourseLevels } = useCourses()

// State
const showCreateModal = ref(false)
const showEditModal = ref(false)
const showViewModal = ref(false)
const selectedVideo = ref<VideoLesson | null>(null)

const filters = ref({
  course_id: '',
  course_level_id: '',
  requires_payment: ''
})

// Computed
const filteredLevels = computed(() => {
  if (!filters.value.course_id) return courseLevels.value
  return courseLevels.value.filter(level => 
    level.course_id.toString() === filters.value.course_id
  )
})

// Methods
const applyFilters = () => {
  const filterParams: any = {}
  if (filters.value.course_id) filterParams.course_id = parseInt(filters.value.course_id)
  if (filters.value.course_level_id) filterParams.course_level_id = parseInt(filters.value.course_level_id)
  if (filters.value.requires_payment !== '') filterParams.requires_payment = filters.value.requires_payment === 'true'
  
  fetchVideoLessons(filterParams)
}

const viewVideo = (video: VideoLesson) => {
  selectedVideo.value = video
  showViewModal.value = true
}

const editVideo = (video: VideoLesson) => {
  selectedVideo.value = video
  showEditModal.value = true
}

const deleteVideo = async (video: VideoLesson) => {
  if (confirm(`Are you sure you want to delete "${video.title}"?`)) {
    await deleteVideoLesson(video.id)
  }
}

const closeModal = () => {
  showCreateModal.value = false
  showEditModal.value = false
  selectedVideo.value = null
}

const handleSave = () => {
  closeModal()
  applyFilters() // Refresh the list
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric' 
  })
}

// Watch for course selection to update levels
watch(() => filters.value.course_id, () => {
  filters.value.course_level_id = '' // Reset level when course changes
})

// Initialize
onMounted(async () => {
  await Promise.all([
    fetchCourses(),
    fetchCourseLevels(),
    fetchVideoLessons()
  ])
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.aspect-video {
  aspect-ratio: 16 / 9;
}
</style>
