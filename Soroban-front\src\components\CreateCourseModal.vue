<template>
  <div v-if="isOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-2xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <h2 class="text-2xl font-bold text-gray-900">Create New Course</h2>
        <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600 transition-colors">
          <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>

      <!-- Form -->
      <form @submit.prevent="handleSubmit" class="p-6 space-y-6">
        <!-- Course Name -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Course Name *</label>
          <input 
            v-model="form.name" 
            type="text" 
            required
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter course name"
          />
        </div>

        <!-- Course Description -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
          <textarea 
            v-model="form.description" 
            rows="4"
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter course description"
          ></textarea>
        </div>

        <!-- Course Image -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Course Image</label>
          <input 
            type="file" 
            accept="image/*"
            @change="handleImageUpload"
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <p class="text-sm text-gray-500 mt-1">Upload an image for the course (optional)</p>
        </div>

        <!-- Course Status -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
          <select 
            v-model="form.status" 
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="draft">Draft</option>
          </select>
        </div>

        <!-- Course Levels -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Course Levels</label>
          <div class="space-y-3">
            <div v-for="(level, index) in form.levels" :key="index" class="flex items-center gap-3 p-3 border border-gray-200 rounded-lg">
              <select 
                v-model="level.level_number" 
                class="px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500"
              >
                <option value="one">Level One</option>
                <option value="two">Level Two</option>
                <option value="three">Level Three</option>
              </select>
              <input 
                v-model="level.title" 
                type="text" 
                placeholder="Level title"
                class="flex-1 px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500"
              />
              <input 
                v-model.number="level.price" 
                type="number" 
                placeholder="Price"
                class="w-24 px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500"
              />
              <button 
                type="button"
                @click="removeLevel(index)"
                class="text-red-500 hover:text-red-700 p-1"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                </svg>
              </button>
            </div>
            <button 
              type="button"
              @click="addLevel"
              class="w-full py-2 px-4 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-blue-500 hover:text-blue-500 transition-colors"
            >
              + Add Level
            </button>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex gap-4 pt-6 border-t border-gray-200">
          <button
            type="button"
            @click="$emit('close')"
            class="flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            :disabled="isSubmitting || !isFormValid"
            class="flex-1 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
          >
            {{ isSubmitting ? 'Creating...' : 'Create Course' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useCourses } from '../composables/useCourses'

// Props
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['close', 'save'])

// Composables
const { createCourse } = useCourses()

// State
const isSubmitting = ref(false)
const form = ref({
  name: '',
  description: '',
  status: 'active',
  image: null,
  levels: [
    {
      level_number: 'one',
      title: '',
      price: 0
    }
  ]
})

// Computed
const isFormValid = computed(() => {
  return form.value.name.trim() && 
         form.value.levels.length > 0 &&
         form.value.levels.every(level => level.title.trim())
})

// Methods
const addLevel = () => {
  form.value.levels.push({
    level_number: 'one',
    title: '',
    price: 0
  })
}

const removeLevel = (index: number) => {
  if (form.value.levels.length > 1) {
    form.value.levels.splice(index, 1)
  }
}

const handleImageUpload = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (file) {
    form.value.image = file
  }
}

const handleSubmit = async () => {
  if (!isFormValid.value) return

  isSubmitting.value = true
  try {
    const courseData = {
      name: form.value.name,
      description: form.value.description,
      status: form.value.status,
      levels: form.value.levels
    }

    const result = await createCourse(courseData)
    if (result) {
      // Reset form
      form.value = {
        name: '',
        description: '',
        status: 'active',
        image: null,
        levels: [
          {
            level_number: 'one',
            title: '',
            price: 0
          }
        ]
      }
      emit('save')
    }
  } catch (error) {
    console.error('Failed to create course:', error)
    alert('Failed to create course. Please try again.')
  } finally {
    isSubmitting.value = false
  }
}
</script>
