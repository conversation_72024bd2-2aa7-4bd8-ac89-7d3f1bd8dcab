<template>
  <div class="flex min-h-screen bg-[#F9FAFB]">
    <!-- Sidebar -->
    <aside class="w-64 bg-[#F6F9FF] flex flex-col justify-between border-r border-[#D1D5DC]">
      <div>
        <!-- Logo -->
        <router-link to="/" class="flex items-center gap-2 px-6 py-8 hover:opacity-80 transition-opacity">
          <img src="https://c.animaapp.com/mc5ppr8hKB91iA/img/logo-lerning-removebg-preview-1-1.png" alt="ELBARQ Logo" class="h-10 w-10 rounded" />
          <div>
            <div class="font-bold text-[#162456] text-lg leading-4">ELBARQ</div>
            <div class="text-xs text-[#d08700] font-semibold -mt-1">Soroban</div>
          </div>
        </router-link>

        <!-- Navigation -->
        <nav class="mt-2">
          <ul class="space-y-2 px-2">
            <!-- Home Link -->
            <li>
              <router-link to="/" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all hover:bg-white hover:shadow-md">
                <span>
                  <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
                    <path d="M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" stroke="#043355" stroke-width="2"/>
                    <path d="M9 22V12h6v10" stroke="#043355" stroke-width="2"/>
                  </svg>
                </span>
                Home
              </router-link>
            </li>

            <!-- Dynamic Navigation Items -->
            <li v-for="item in navigationItems" :key="item.key">
              <a 
                href="#" 
                @click="$emit('section-change', item.key)" 
                :class="activeSection === item.key ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" 
                class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all"
              >
                <span v-html="item.icon"></span>
                {{ item.label }}
              </a>
            </li>
          </ul>
        </nav>
      </div>

      <!-- Bottom Navigation -->
      <div class="mb-6 px-2 space-y-2">
        <!-- Profile Link -->
        <a 
          href="#" 
          @click="$emit('section-change', 'profile')" 
          :class="activeSection === 'profile' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" 
          class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all"
        >
          <span>
            <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
              <path d="M20 21v-2a4 4 0 00-4-4H8a4 4 0 00-4 4v2" stroke="#043355" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <circle cx="12" cy="7" r="4" stroke="#043355" stroke-width="2"/>
            </svg>
          </span>
          Profile
        </a>

        <!-- Settings Link -->
        <a 
          href="#" 
          @click="$emit('section-change', 'settings')" 
          :class="activeSection === 'settings' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" 
          class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all"
        >
          <span>
            <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
              <circle cx="12" cy="12" r="3" stroke="#043355" stroke-width="2"/>
              <path d="M19.4 15a1.65 1.65 0 00.33 1.82l.06.06a2 2 0 010 2.83 2 2 0 01-2.83 0l-.06-.06a1.65 1.65 0 00-1.82-.33 1.65 1.65 0 00-1 1.51V21a2 2 0 01-2 2 2 2 0 01-2-2v-.09A1.65 1.65 0 009 19.4a1.65 1.65 0 00-1.82.33l-.06.06a2 2 0 01-2.83 0 2 2 0 010-2.83l.06-.06a1.65 1.65 0 00.33-1.82 1.65 1.65 0 00-1.51-1H3a2 2 0 01-2-2 2 2 0 012-2h.09A1.65 1.65 0 004.6 9a1.65 1.65 0 00-.33-1.82l-.06-.06a2 2 0 010-2.83 2 2 0 012.83 0l.06.06a1.65 1.65 0 001.82.33H9a1.65 1.65 0 001-1.51V3a2 2 0 012-2 2 2 0 012 2v.09a1.65 1.65 0 001 1.51 1.65 1.65 0 001.82-.33l.06-.06a2 2 0 012.83 0 2 2 0 010 2.83l-.06.06a1.65 1.65 0 00-.33 1.82V9a1.65 1.65 0 001.51 1H21a2 2 0 012 2 2 2 0 01-2 2h-.09a1.65 1.65 0 00-1.51 1z" stroke="#043355" stroke-width="2"/>
            </svg>
          </span>
          Settings
        </a>

        <!-- Logout -->
        <a href="#" @click="showLogoutConfirm = true" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#e7000b] hover:bg-red-50 hover:shadow-md transition-all">
          <span>
            <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
              <path d="M9 21H5a2 2 0 01-2-2V5a2 2 0 012-2h4" stroke="#e7000b" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M16 17l5-5-5-5" stroke="#e7000b" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M21 12H9" stroke="#e7000b" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </span>
          Log Out
        </a>
      </div>
    </aside>

    <!-- Main Content -->
    <main class="flex-1 min-h-screen">
      <!-- Header -->
      <div class="flex justify-end items-center px-10 py-4 bg-white shadow-sm border-b border-[#F3F3F3]" style="min-height:107px;">
        <div class="flex items-center gap-6">
          <!-- Dark Mode Toggle -->
          <span class="text-xl text-black">
            <svg width="20" height="20" fill="none" viewBox="0 0 24 24">
              <path d="M21 12.79A9 9 0 1 1 11.21 3a7 7 0 0 0 9.79 9.79Z" stroke="#000" stroke-width="2"/>
            </svg>
          </span>
          
          <!-- Notifications -->
          <span class="relative">
            <svg class="w-7 h-7 text-[#043355]" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V4a2 2 0 10-4 0v1.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/>
            </svg>
            <span class="absolute -top-2 -right-2 bg-[#BC0000] text-white rounded-full w-5 h-5 flex items-center justify-center text-xs border-2 border-white">+1</span>
          </span>
          
          <!-- User Info -->
          <div class="flex items-center gap-2 bg-white border border-[#D1D5DC] rounded-full px-4 py-2">
            <img :src="userAvatar" :alt="userDisplayName" class="w-8 h-8 rounded-full object-cover border border-[#D1D5DC]" />
            <span class="font-bold text-[#043355]">{{ userDisplayName }}</span>
          </div>
        </div>
      </div>

      <!-- Content Slot -->
      <slot></slot>
    </main>

    <!-- Logout Confirmation Modal -->
    <div v-if="showLogoutConfirm" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4">
        <!-- Header -->
        <div class="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 class="text-xl font-bold text-gray-900">Confirm Logout</h2>
          <button @click="showLogoutConfirm = false" class="text-gray-400 hover:text-gray-600 transition-colors">
            <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </div>

        <!-- Content -->
        <div class="p-6">
          <div class="flex items-center gap-4 mb-6">
            <div class="bg-red-100 p-3 rounded-full">
              <svg width="24" height="24" fill="#EF4444" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-gray-900">Are you sure you want to logout?</h3>
              <p class="text-sm text-gray-600 mt-1">You will be redirected to the home page and will need to login again to access your dashboard.</p>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex gap-3">
            <button
              @click="showLogoutConfirm = false"
              class="flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
            >
              Cancel
            </button>
            <button
              @click="handleLogout"
              :disabled="isLoggingOut"
              class="flex-1 px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:bg-red-400 disabled:cursor-not-allowed transition-colors font-medium"
            >
              {{ isLoggingOut ? 'Logging out...' : 'Yes, Logout' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuth } from '../composables/useAuth'

const router = useRouter()

// Logout modal state
const showLogoutConfirm = ref(false)
const isLoggingOut = ref(false)

// Auth composable
const { logout } = useAuth()

// Props
const props = defineProps({
  activeSection: {
    type: String,
    default: 'dashboard'
  },
  navigationItems: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['section-change', 'logout'])

// Get current user data
const currentUser = computed(() => {
  const userStr = localStorage.getItem('user')
  return userStr ? JSON.parse(userStr) : null
})

const userDisplayName = computed(() => {
  if (!currentUser.value) return 'Guest User'
  return `${currentUser.value.first_name} ${currentUser.value.second_name}`
})

const userAvatar = computed(() => {
  if (currentUser.value?.profile?.profile_picture) {
    return currentUser.value.profile.profile_picture
  }
  // Generate avatar based on user initials
  return `https://ui-avatars.com/api/?name=${encodeURIComponent(userDisplayName.value)}&background=3B82F6&color=fff&size=200`
})

// Logout function
const handleLogout = async () => {
  isLoggingOut.value = true

  try {
    await logout()
    // The logout composable handles clearing localStorage and redirecting
    showLogoutConfirm.value = false
  } catch (error) {
    console.error('Logout failed:', error)
    // Even if logout fails on server, clear local storage and redirect
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user')
    router.push('/')
    showLogoutConfirm.value = false
  } finally {
    isLoggingOut.value = false
  }
}
</script>
