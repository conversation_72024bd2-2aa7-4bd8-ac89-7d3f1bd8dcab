<template>
  <div v-if="isOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-2xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <h2 class="text-2xl font-bold text-gray-900">Edit Course</h2>
        <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600 transition-colors">
          <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>

      <!-- Form -->
      <form @submit.prevent="handleSubmit" class="p-6 space-y-6">
        <!-- Course Name -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Course Name *</label>
          <input 
            v-model="form.name" 
            type="text" 
            required
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter course name"
          />
        </div>

        <!-- Course Description -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
          <textarea 
            v-model="form.description" 
            rows="4"
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter course description"
          ></textarea>
        </div>

        <!-- Course Status -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
          <select 
            v-model="form.status" 
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="draft">Draft</option>
          </select>
        </div>

        <!-- Course Statistics -->
        <div v-if="course" class="bg-gray-50 rounded-lg p-4">
          <h3 class="text-lg font-semibold text-gray-900 mb-3">Course Statistics</h3>
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span class="text-gray-600">Total Levels:</span>
              <span class="font-medium ml-2">{{ course.levels?.length || 0 }}</span>
            </div>
            <div>
              <span class="text-gray-600">Total Lessons:</span>
              <span class="font-medium ml-2">{{ getTotalLessons() }}</span>
            </div>
            <div>
              <span class="text-gray-600">Created:</span>
              <span class="font-medium ml-2">{{ formatDate(course.created_at) }}</span>
            </div>
            <div>
              <span class="text-gray-600">Last Updated:</span>
              <span class="font-medium ml-2">{{ formatDate(course.updated_at) }}</span>
            </div>
          </div>
        </div>

        <!-- Course Levels -->
        <div v-if="course?.levels">
          <label class="block text-sm font-medium text-gray-700 mb-2">Course Levels</label>
          <div class="space-y-3">
            <div v-for="level in course.levels" :key="level.id" class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
              <div>
                <h4 class="font-medium text-gray-900">{{ level.title }}</h4>
                <p class="text-sm text-gray-600">Level {{ level.level_number }}</p>
              </div>
              <div class="flex items-center gap-2">
                <span class="text-sm text-gray-500">{{ getLessonCount(level.id) }} lessons</span>
                <button 
                  type="button"
                  @click="manageLevelLessons(level)"
                  class="text-blue-600 hover:text-blue-800 text-sm font-medium"
                >
                  Manage
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-blue-50 rounded-lg p-4">
          <h3 class="text-lg font-semibold text-gray-900 mb-3">Quick Actions</h3>
          <div class="flex flex-wrap gap-2">
            <button 
              type="button"
              @click="viewCourse"
              class="bg-green-500 text-white px-3 py-2 rounded-lg hover:bg-green-600 transition-colors text-sm"
            >
              View Course
            </button>
            <button 
              type="button"
              @click="manageVideos"
              class="bg-purple-500 text-white px-3 py-2 rounded-lg hover:bg-purple-600 transition-colors text-sm"
            >
              Manage Videos
            </button>
            <button 
              type="button"
              @click="viewAnalytics"
              class="bg-orange-500 text-white px-3 py-2 rounded-lg hover:bg-orange-600 transition-colors text-sm"
            >
              View Analytics
            </button>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex gap-4 pt-6 border-t border-gray-200">
          <button
            type="button"
            @click="$emit('close')"
            class="flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            :disabled="isSubmitting || !isFormValid"
            class="flex-1 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
          >
            {{ isSubmitting ? 'Updating...' : 'Update Course' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useCourses } from '../composables/useCourses'
import { useVideos } from '../composables/useVideos'

const router = useRouter()

// Props
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  course: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['close', 'save'])

// Composables
const { updateCourse } = useCourses()
const { videos } = useVideos()

// State
const isSubmitting = ref(false)
const form = ref({
  name: '',
  description: '',
  status: 'active'
})

// Computed
const isFormValid = computed(() => {
  return form.value.name.trim()
})

// Methods
const initializeForm = () => {
  if (props.course) {
    form.value = {
      name: props.course.name || '',
      description: props.course.description || '',
      status: props.course.status || 'active'
    }
  }
}

const getTotalLessons = () => {
  if (!props.course?.levels) return 0
  return props.course.levels.reduce((total: number, level: any) => {
    return total + getLessonCount(level.id)
  }, 0)
}

const getLessonCount = (levelId: number) => {
  return videos.value.filter(video => video.course_level_id === levelId).length
}

const manageLevelLessons = (level: any) => {
  // Navigate to video management for this level
  router.push({
    name: 'admin-videos',
    query: { course_level_id: level.id }
  })
  emit('close')
}

const viewCourse = () => {
  if (props.course) {
    router.push(`/courses/${props.course.id}/videos`)
    emit('close')
  }
}

const manageVideos = () => {
  if (props.course) {
    router.push({
      name: 'admin-videos',
      query: { course_id: props.course.id }
    })
    emit('close')
  }
}

const viewAnalytics = () => {
  // Navigate to analytics page (to be implemented)
  console.log('View analytics for course:', props.course?.id)
}

const formatDate = (dateString: string) => {
  if (!dateString) return 'N/A'
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric' 
  })
}

const handleSubmit = async () => {
  if (!isFormValid.value || !props.course) return

  isSubmitting.value = true
  try {
    const result = await updateCourse(props.course.id, form.value)
    if (result) {
      emit('save')
    }
  } catch (error) {
    console.error('Failed to update course:', error)
    alert('Failed to update course. Please try again.')
  } finally {
    isSubmitting.value = false
  }
}

// Watch for course changes
watch(() => props.course, () => {
  initializeForm()
}, { immediate: true })

// Watch for modal open/close
watch(() => props.isOpen, (isOpen) => {
  if (isOpen) {
    initializeForm()
  }
})
</script>
