<template>
  <div class="px-10 pt-8 pb-10">
    <div class="flex items-center justify-between mb-8">
      <h1 class="text-4xl font-extrabold text-[#043355]">Payment Management</h1>
      
      <!-- Filter Controls -->
      <div class="flex items-center gap-4">
        <select v-model="statusFilter" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
          <option value="">All Payments</option>
          <option value="pending">Pending</option>
          <option value="confirmed">Confirmed</option>
        </select>
        
        <button @click="fetchPayments()" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
          <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20" class="inline mr-2">
            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"/>
          </svg>
          Refresh
        </button>
      </div>
    </div>

    <!-- Payment Statistics -->
    <div v-if="paymentStats" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div class="bg-white rounded-2xl shadow-lg p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Total Payments</p>
            <p class="text-3xl font-bold text-gray-900">{{ paymentStats.total_payments }}</p>
          </div>
          <div class="bg-blue-100 p-3 rounded-full">
            <svg width="24" height="24" fill="#3B82F6" viewBox="0 0 20 20">
              <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"/>
            </svg>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-2xl shadow-lg p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Confirmed</p>
            <p class="text-3xl font-bold text-green-600">{{ paymentStats.confirmed_payments }}</p>
          </div>
          <div class="bg-green-100 p-3 rounded-full">
            <svg width="24" height="24" fill="#10B981" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
            </svg>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-2xl shadow-lg p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Pending</p>
            <p class="text-3xl font-bold text-orange-600">{{ paymentStats.pending_payments }}</p>
          </div>
          <div class="bg-orange-100 p-3 rounded-full">
            <svg width="24" height="24" fill="#F59E0B" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
            </svg>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-2xl shadow-lg p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Total Revenue</p>
            <p class="text-3xl font-bold text-blue-600">${{ paymentStats.total_revenue }}</p>
          </div>
          <div class="bg-blue-100 p-3 rounded-full">
            <svg width="24" height="24" fill="#3B82F6" viewBox="0 0 20 20">
              <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"/>
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"/>
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="text-center py-12">
      <div class="text-red-500 text-lg mb-4">{{ error }}</div>
      <button @click="fetchPayments()" class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors">
        Try Again
      </button>
    </div>

    <!-- Payments Table -->
    <div v-else class="bg-white rounded-2xl shadow-lg overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Recent Payments</h3>
      </div>
      
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Course</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Transaction</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="payment in filteredPayments" :key="payment.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <img 
                    :src="`https://ui-avatars.com/api/?name=${encodeURIComponent(payment.student?.first_name + ' ' + payment.student?.second_name)}&background=3B82F6&color=fff&size=40`" 
                    :alt="payment.student?.first_name" 
                    class="w-10 h-10 rounded-full"
                  />
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">
                      {{ payment.student?.first_name }} {{ payment.student?.second_name }}
                    </div>
                    <div class="text-sm text-gray-500">{{ payment.student?.email }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">{{ payment.level?.title }}</div>
                <div class="text-sm text-gray-500">{{ payment.level?.course?.title }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-bold text-gray-900">${{ payment.amount }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ payment.transaction_code || 'N/A' }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getStatusClass(payment.is_confirmed)" class="px-2 py-1 text-xs font-semibold rounded-full">
                  {{ payment.is_confirmed ? 'Confirmed' : 'Pending' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ formatDate(payment.created_at) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex items-center gap-2">
                  <button 
                    @click="viewPaymentDetails(payment)"
                    class="text-blue-600 hover:text-blue-900 transition-colors"
                  >
                    View
                  </button>
                  <button 
                    v-if="!payment.is_confirmed"
                    @click="confirmPayment(payment.id)"
                    class="text-green-600 hover:text-green-900 transition-colors"
                  >
                    Confirm
                  </button>
                  <button 
                    v-if="payment.transaction_pic"
                    @click="viewReceipt(payment.transaction_pic)"
                    class="text-purple-600 hover:text-purple-900 transition-colors"
                  >
                    Receipt
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
        
        <!-- Empty State -->
        <div v-if="filteredPayments.length === 0" class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No payments found</h3>
          <p class="mt-1 text-sm text-gray-500">No payments match your current filter criteria.</p>
        </div>
      </div>
    </div>

    <!-- Payment Details Modal -->
    <div v-if="selectedPayment" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-2xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 class="text-2xl font-bold text-gray-900">Payment Details</h2>
          <button @click="selectedPayment = null" class="text-gray-400 hover:text-gray-600 transition-colors">
            <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </div>
        
        <div class="p-6 space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700">Student</label>
              <p class="text-sm text-gray-900">{{ selectedPayment.student?.first_name }} {{ selectedPayment.student?.second_name }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">Email</label>
              <p class="text-sm text-gray-900">{{ selectedPayment.student?.email }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">Course</label>
              <p class="text-sm text-gray-900">{{ selectedPayment.level?.course?.title }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">Level</label>
              <p class="text-sm text-gray-900">{{ selectedPayment.level?.title }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">Amount</label>
              <p class="text-sm font-bold text-gray-900">${{ selectedPayment.amount }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">Transaction Code</label>
              <p class="text-sm text-gray-900">{{ selectedPayment.transaction_code || 'N/A' }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">Status</label>
              <span :class="getStatusClass(selectedPayment.is_confirmed)" class="px-2 py-1 text-xs font-semibold rounded-full">
                {{ selectedPayment.is_confirmed ? 'Confirmed' : 'Pending' }}
              </span>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">Date</label>
              <p class="text-sm text-gray-900">{{ formatDate(selectedPayment.created_at) }}</p>
            </div>
          </div>
          
          <div v-if="selectedPayment.subscription_start_date || selectedPayment.subscription_end_date">
            <label class="block text-sm font-medium text-gray-700 mb-2">Subscription Period</label>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-xs text-gray-500">Start Date</label>
                <p class="text-sm text-gray-900">{{ formatDate(selectedPayment.subscription_start_date) }}</p>
              </div>
              <div>
                <label class="block text-xs text-gray-500">End Date</label>
                <p class="text-sm text-gray-900">{{ formatDate(selectedPayment.subscription_end_date) }}</p>
              </div>
            </div>
          </div>
          
          <div v-if="selectedPayment.transaction_pic" class="text-center">
            <label class="block text-sm font-medium text-gray-700 mb-2">Payment Receipt</label>
            <img :src="selectedPayment.transaction_pic" alt="Payment Receipt" class="max-w-full h-auto rounded-lg border">
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { usePayments } from '../composables/usePayments'

// Composables
const { 
  payments, 
  paymentStats, 
  isLoading, 
  error, 
  fetchPayments, 
  fetchPaymentStats, 
  confirmPayment: confirmPaymentAction 
} = usePayments()

// State
const statusFilter = ref('')
const selectedPayment = ref(null)

// Computed
const filteredPayments = computed(() => {
  if (!statusFilter.value) return payments.value
  
  const isConfirmed = statusFilter.value === 'confirmed'
  return payments.value.filter(payment => payment.is_confirmed === isConfirmed)
})

// Methods
const getStatusClass = (isConfirmed) => {
  return isConfirmed 
    ? 'bg-green-100 text-green-800' 
    : 'bg-orange-100 text-orange-800'
}

const formatDate = (dateString) => {
  if (!dateString) return 'N/A'
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const viewPaymentDetails = (payment) => {
  selectedPayment.value = payment
}

const viewReceipt = (receiptUrl) => {
  window.open(receiptUrl, '_blank')
}

const confirmPayment = async (paymentId) => {
  if (confirm('Are you sure you want to confirm this payment?')) {
    const result = await confirmPaymentAction(paymentId)
    if (result) {
      alert('Payment confirmed successfully!')
    }
  }
}

// Watch for filter changes
watch(statusFilter, () => {
  const filters = {}
  if (statusFilter.value === 'confirmed') {
    filters.confirmed = true
  } else if (statusFilter.value === 'pending') {
    filters.confirmed = false
  }
  fetchPayments(filters)
})

// Initialize
onMounted(async () => {
  await Promise.all([
    fetchPayments(),
    fetchPaymentStats()
  ])
})
</script>
