<template>
  <div v-if="isOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-2xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <h2 class="text-2xl font-bold text-gray-900">Complete Payment</h2>
        <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600 transition-colors">
          <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>

      <!-- Course Information -->
      <div class="p-6 border-b border-gray-200">
        <div class="flex items-center gap-4">
          <img 
            :src="courseLevel?.course?.image || 'https://images.pexels.com/photos/3184307/pexels-photo-3184307.jpeg'" 
            :alt="courseLevel?.title" 
            class="w-20 h-20 rounded-lg object-cover"
          />
          <div>
            <h3 class="text-xl font-bold text-gray-900">{{ courseLevel?.title }}</h3>
            <p class="text-gray-600">{{ courseLevel?.course?.title }}</p>
            <div class="flex items-center gap-4 mt-2">
              <span class="text-2xl font-bold text-blue-600">${{ courseLevel?.price }}</span>
              <span class="text-sm text-gray-500">{{ courseLevel?.duration }} hours</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Payment Form -->
      <form @submit.prevent="submitPayment" class="p-6 space-y-6">
        <!-- Payment Method Selection -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-3">Payment Method</label>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div 
              @click="paymentMethod = 'bank_transfer'"
              :class="paymentMethod === 'bank_transfer' ? 'border-blue-500 bg-blue-50' : 'border-gray-300'"
              class="border-2 rounded-lg p-4 cursor-pointer transition-all hover:border-blue-400"
            >
              <div class="flex items-center gap-3">
                <div class="w-6 h-6 rounded-full border-2 border-blue-500 flex items-center justify-center">
                  <div v-if="paymentMethod === 'bank_transfer'" class="w-3 h-3 bg-blue-500 rounded-full"></div>
                </div>
                <div>
                  <h4 class="font-semibold text-gray-900">Bank Transfer</h4>
                  <p class="text-sm text-gray-600">Transfer to our bank account</p>
                </div>
              </div>
            </div>

            <div 
              @click="paymentMethod = 'mobile_wallet'"
              :class="paymentMethod === 'mobile_wallet' ? 'border-blue-500 bg-blue-50' : 'border-gray-300'"
              class="border-2 rounded-lg p-4 cursor-pointer transition-all hover:border-blue-400"
            >
              <div class="flex items-center gap-3">
                <div class="w-6 h-6 rounded-full border-2 border-blue-500 flex items-center justify-center">
                  <div v-if="paymentMethod === 'mobile_wallet'" class="w-3 h-3 bg-blue-500 rounded-full"></div>
                </div>
                <div>
                  <h4 class="font-semibold text-gray-900">Mobile Wallet</h4>
                  <p class="text-sm text-gray-600">Vodafone Cash, Orange Money</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Bank Transfer Details -->
        <div v-if="paymentMethod === 'bank_transfer'" class="bg-gray-50 rounded-lg p-4">
          <h4 class="font-semibold text-gray-900 mb-3">Bank Transfer Details</h4>
          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span class="text-gray-600">Bank Name:</span>
              <span class="font-medium">National Bank of Egypt</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Account Number:</span>
              <span class="font-medium">****************</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Account Name:</span>
              <span class="font-medium">ELBARQ Soroban Academy</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Amount:</span>
              <span class="font-bold text-blue-600">${{ courseLevel?.price }}</span>
            </div>
          </div>
        </div>

        <!-- Mobile Wallet Details -->
        <div v-if="paymentMethod === 'mobile_wallet'" class="bg-gray-50 rounded-lg p-4">
          <h4 class="font-semibold text-gray-900 mb-3">Mobile Wallet Details</h4>
          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span class="text-gray-600">Vodafone Cash:</span>
              <span class="font-medium">***********</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Orange Money:</span>
              <span class="font-medium">***********</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Amount:</span>
              <span class="font-bold text-blue-600">${{ courseLevel?.price }}</span>
            </div>
          </div>
        </div>

        <!-- Transaction Code -->
        <div>
          <label for="transaction_code" class="block text-sm font-medium text-gray-700 mb-2">
            Transaction Code/Reference Number *
          </label>
          <input
            id="transaction_code"
            v-model="formData.transaction_code"
            type="text"
            required
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter transaction reference number"
          />
        </div>

        <!-- Receipt Upload -->
        <div>
          <label for="receipt" class="block text-sm font-medium text-gray-700 mb-2">
            Payment Receipt/Screenshot *
          </label>
          <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <input
              id="receipt"
              type="file"
              accept="image/*"
              @change="handleFileUpload"
              class="hidden"
              ref="fileInput"
            />
            <div v-if="!selectedFile" @click="$refs.fileInput.click()" class="cursor-pointer">
              <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <p class="mt-2 text-sm text-gray-600">
                <span class="font-medium text-blue-600">Click to upload</span> or drag and drop
              </p>
              <p class="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
            </div>
            <div v-else class="flex items-center justify-between">
              <div class="flex items-center gap-3">
                <img :src="filePreview" alt="Receipt preview" class="w-16 h-16 object-cover rounded-lg">
                <div>
                  <p class="text-sm font-medium text-gray-900">{{ selectedFile.name }}</p>
                  <p class="text-xs text-gray-500">{{ formatFileSize(selectedFile.size) }}</p>
                </div>
              </div>
              <button type="button" @click="removeFile" class="text-red-500 hover:text-red-700">
                <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- Subscription Period -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label for="start_date" class="block text-sm font-medium text-gray-700 mb-2">
              Subscription Start Date
            </label>
            <input
              id="start_date"
              v-model="formData.subscription_start_date"
              type="date"
              :min="today"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div>
            <label for="end_date" class="block text-sm font-medium text-gray-700 mb-2">
              Subscription End Date
            </label>
            <input
              id="end_date"
              v-model="formData.subscription_end_date"
              type="date"
              :min="formData.subscription_start_date"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        <!-- Submit Button -->
        <div class="flex gap-4 pt-4">
          <button
            type="button"
            @click="$emit('close')"
            class="flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            :disabled="isSubmitting || !selectedFile || !formData.transaction_code"
            class="flex-1 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
          >
            {{ isSubmitting ? 'Processing...' : 'Submit Payment' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { usePayments } from '../composables/usePayments'

// Props
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  courseLevel: {
    type: Object,
    default: null
  },
  subscription: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['close', 'success'])

// Composables
const { createPayment, uploadPaymentReceipt, isLoading } = usePayments()

// Form data
const paymentMethod = ref('bank_transfer')
const selectedFile = ref(null)
const filePreview = ref('')
const fileInput = ref(null)
const isSubmitting = ref(false)

const formData = ref({
  transaction_code: '',
  subscription_start_date: '',
  subscription_end_date: ''
})

// Computed
const today = computed(() => {
  return new Date().toISOString().split('T')[0]
})

// Set default dates
watch(() => props.isOpen, (isOpen) => {
  if (isOpen) {
    const startDate = new Date()
    const endDate = new Date()
    endDate.setMonth(endDate.getMonth() + 1) // Default 1 month subscription
    
    formData.value.subscription_start_date = startDate.toISOString().split('T')[0]
    formData.value.subscription_end_date = endDate.toISOString().split('T')[0]
  }
})

// File handling
const handleFileUpload = (event) => {
  const file = event.target.files[0]
  if (file) {
    selectedFile.value = file
    const reader = new FileReader()
    reader.onload = (e) => {
      filePreview.value = e.target.result
    }
    reader.readAsDataURL(file)
  }
}

const removeFile = () => {
  selectedFile.value = null
  filePreview.value = ''
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Submit payment
const submitPayment = async () => {
  if (!props.courseLevel || !props.subscription || !selectedFile.value) return

  isSubmitting.value = true
  
  try {
    // Upload receipt first
    const receiptUrl = await uploadPaymentReceipt(selectedFile.value)
    if (!receiptUrl) {
      throw new Error('Failed to upload receipt')
    }

    // Create payment record
    const paymentData = {
      student_id: props.subscription.student_id,
      level_id: props.courseLevel.id,
      subscription_id: props.subscription.id,
      amount: props.courseLevel.price,
      transaction_code: formData.value.transaction_code,
      transaction_pic: receiptUrl,
      subscription_start_date: formData.value.subscription_start_date,
      subscription_end_date: formData.value.subscription_end_date
    }

    const payment = await createPayment(paymentData)
    if (payment) {
      emit('success', payment)
      emit('close')
      
      // Reset form
      formData.value = {
        transaction_code: '',
        subscription_start_date: '',
        subscription_end_date: ''
      }
      removeFile()
    }
  } catch (error) {
    console.error('Payment submission failed:', error)
    alert('Payment submission failed. Please try again.')
  } finally {
    isSubmitting.value = false
  }
}
</script>
