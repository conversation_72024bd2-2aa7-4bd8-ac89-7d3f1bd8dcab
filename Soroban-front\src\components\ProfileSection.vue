<template>
  <div class="px-10 pt-8 pb-10">
    <h1 class="text-4xl font-extrabold text-[#043355] mb-8">Profile</h1>
    
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Profile Card -->
      <div class="lg:col-span-1">
        <div class="bg-white rounded-2xl shadow-lg p-6 text-center">
          <div class="relative inline-block mb-4">
            <img :src="userAvatar" :alt="userDisplayName" class="w-24 h-24 rounded-full object-cover border-4 border-blue-100 mx-auto" />
            <button class="absolute bottom-0 right-0 bg-blue-500 text-white rounded-full p-2 hover:bg-blue-600 transition-colors">
              <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
              </svg>
            </button>
          </div>
          <h2 class="text-xl font-bold text-gray-900 mb-2">{{ userDisplayName }}</h2>
          <p class="text-gray-600 mb-2">{{ currentUser?.email }}</p>
          <span class="inline-block px-3 py-1 text-sm font-medium rounded-full" :class="userRoleBadgeClass">
            {{ userRoleDisplay }}
          </span>
          <div class="mt-4 pt-4 border-t border-gray-200">
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">Member since</span>
              <span class="font-medium">{{ memberSince }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Profile Information -->
      <div class="lg:col-span-2">
        <div class="bg-white rounded-2xl shadow-lg p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-xl font-bold text-gray-900">Personal Information</h3>
            <button @click="isEditing = !isEditing" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
              {{ isEditing ? 'Cancel' : 'Edit Profile' }}
            </button>
          </div>

          <form @submit.prevent="saveProfile" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- First Name -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                <input 
                  v-model="profileForm.first_name" 
                  :disabled="!isEditing"
                  type="text" 
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500"
                />
              </div>

              <!-- Last Name -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                <input 
                  v-model="profileForm.second_name" 
                  :disabled="!isEditing"
                  type="text" 
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500"
                />
              </div>

              <!-- Email -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                <input 
                  v-model="profileForm.email" 
                  :disabled="!isEditing"
                  type="email" 
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500"
                />
              </div>

              <!-- Age -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Age</label>
                <input 
                  v-model="profileForm.age" 
                  :disabled="!isEditing"
                  type="number" 
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500"
                />
              </div>

              <!-- Phone -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                <input 
                  v-model="profileForm.phone" 
                  :disabled="!isEditing"
                  type="tel" 
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500"
                />
              </div>

              <!-- Address -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                <input 
                  v-model="profileForm.address" 
                  :disabled="!isEditing"
                  type="text" 
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500"
                />
              </div>
            </div>

            <!-- Bio -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Bio</label>
              <textarea 
                v-model="profileForm.bio" 
                :disabled="!isEditing"
                rows="4" 
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500"
                placeholder="Tell us about yourself..."
              ></textarea>
            </div>

            <!-- Save Button -->
            <div v-if="isEditing" class="flex justify-end">
              <button type="submit" class="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors">
                Save Changes
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// Profile editing state
const isEditing = ref(false)

// Get current user data
const currentUser = computed(() => {
  const userStr = localStorage.getItem('user')
  return userStr ? JSON.parse(userStr) : null
})

const userDisplayName = computed(() => {
  if (!currentUser.value) return 'Guest User'
  return `${currentUser.value.first_name} ${currentUser.value.second_name}`
})

const userRoleDisplay = computed(() => {
  if (!currentUser.value) return 'Guest'
  const roleMap = {
    'superAdmin': 'Super Admin',
    'admin': 'Admin',
    'teacher': 'Teacher',
    'student': 'Student',
    'guest': 'Guest'
  }
  return roleMap[currentUser.value.role] || currentUser.value.role
})

const userRoleBadgeClass = computed(() => {
  if (!currentUser.value) return 'bg-gray-100 text-gray-800'
  const roleClasses = {
    'superAdmin': 'bg-purple-100 text-purple-800',
    'admin': 'bg-red-100 text-red-800',
    'teacher': 'bg-blue-100 text-blue-800',
    'student': 'bg-green-100 text-green-800',
    'guest': 'bg-gray-100 text-gray-800'
  }
  return roleClasses[currentUser.value.role] || 'bg-gray-100 text-gray-800'
})

const userAvatar = computed(() => {
  if (currentUser.value?.profile?.profile_picture) {
    return currentUser.value.profile.profile_picture
  }
  // Generate avatar based on user initials
  return `https://ui-avatars.com/api/?name=${encodeURIComponent(userDisplayName.value)}&background=3B82F6&color=fff&size=200`
})

const memberSince = computed(() => {
  if (!currentUser.value?.created_at) return 'Recently'
  const date = new Date(currentUser.value.created_at)
  return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' })
})

// Profile form data
const profileForm = ref({
  first_name: '',
  second_name: '',
  email: '',
  age: '',
  phone: '',
  address: '',
  bio: ''
})

// Initialize profile form with current user data
const initializeProfileForm = () => {
  if (currentUser.value) {
    profileForm.value = {
      first_name: currentUser.value.first_name || '',
      second_name: currentUser.value.second_name || '',
      email: currentUser.value.email || '',
      age: currentUser.value.age || '',
      phone: currentUser.value.profile?.phone || '',
      address: currentUser.value.profile?.address || '',
      bio: currentUser.value.profile?.bio || ''
    }
  }
}

// Save profile changes
const saveProfile = async () => {
  try {
    // Here you would typically make an API call to update the user profile
    console.log('Saving profile:', profileForm.value)
    
    // Update localStorage with new data (temporary solution)
    const updatedUser = {
      ...currentUser.value,
      first_name: profileForm.value.first_name,
      second_name: profileForm.value.second_name,
      email: profileForm.value.email,
      age: profileForm.value.age,
      profile: {
        ...currentUser.value.profile,
        phone: profileForm.value.phone,
        address: profileForm.value.address,
        bio: profileForm.value.bio
      }
    }
    
    localStorage.setItem('user', JSON.stringify(updatedUser))
    isEditing.value = false
    
    // Show success message (you can implement a toast notification here)
    alert('Profile updated successfully!')
    
  } catch (error) {
    console.error('Error saving profile:', error)
    alert('Error updating profile. Please try again.')
  }
}

// Initialize on mount
onMounted(() => {
  initializeProfileForm()
})
</script>
