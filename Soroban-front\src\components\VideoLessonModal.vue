<template>
  <div v-if="isOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-2xl shadow-2xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <h2 class="text-2xl font-bold text-gray-900">
          {{ lesson ? 'Edit Video Lesson' : 'Create Video Lesson' }}
        </h2>
        <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600 transition-colors">
          <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>

      <!-- Form -->
      <form @submit.prevent="handleSubmit" class="p-6 space-y-6">
        <!-- Basic Information -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Course Selection -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Course *</label>
            <select 
              v-model="form.course_id" 
              @change="onCourseChange"
              required
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select Course</option>
              <option v-for="course in courses" :key="course.id" :value="course.id">
                {{ course.name }}
              </option>
            </select>
          </div>

          <!-- Course Level Selection -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Course Level *</label>
            <select 
              v-model="form.course_level_id" 
              required
              :disabled="!form.course_id"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
            >
              <option value="">Select Level</option>
              <option v-for="level in filteredLevels" :key="level.id" :value="level.id">
                {{ level.title }}
              </option>
            </select>
          </div>

          <!-- Day Number -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Day Number *</label>
            <input 
              v-model.number="form.day_number" 
              type="number" 
              min="1"
              required
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="1"
            />
          </div>

          <!-- Sequence -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Sequence *</label>
            <input 
              v-model.number="form.sequence" 
              type="number" 
              min="1"
              required
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="1"
            />
          </div>
        </div>

        <!-- Title -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Lesson Title *</label>
          <input 
            v-model="form.title" 
            type="text" 
            required
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter lesson title"
          />
        </div>

        <!-- YouTube Playlist -->
        <div class="space-y-4">
          <h3 class="text-lg font-semibold text-gray-900">YouTube Playlist</h3>
          
          <!-- Playlist URL -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">YouTube Playlist URL</label>
            <input 
              v-model="form.youtube_playlist_url" 
              type="url" 
              @blur="extractPlaylistId"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="https://www.youtube.com/playlist?list=..."
            />
            <p class="text-sm text-gray-500 mt-1">
              Paste the full YouTube playlist URL here
            </p>
          </div>

          <!-- Playlist ID (auto-extracted) -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Playlist ID</label>
            <input 
              v-model="form.youtube_playlist_id" 
              type="text" 
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50"
              placeholder="Auto-extracted from URL"
              readonly
            />
          </div>

          <!-- Preview -->
          <div v-if="form.youtube_playlist_id" class="bg-gray-50 rounded-lg p-4">
            <h4 class="text-sm font-medium text-gray-700 mb-2">Preview</h4>
            <div class="aspect-video bg-gray-200 rounded-lg overflow-hidden">
              <iframe
                :src="previewEmbedUrl"
                class="w-full h-full"
                frameborder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowfullscreen
              ></iframe>
            </div>
          </div>
        </div>

        <!-- Access Settings -->
        <div class="space-y-4">
          <h3 class="text-lg font-semibold text-gray-900">Access Settings</h3>
          
          <div class="flex items-center">
            <input 
              v-model="form.requires_payment" 
              type="checkbox" 
              id="requires_payment"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label for="requires_payment" class="ml-2 block text-sm text-gray-900">
              Requires Payment (Premium Content)
            </label>
          </div>
          <p class="text-sm text-gray-500">
            Check this if students need an active subscription to access this video
          </p>
        </div>

        <!-- Additional Attachments -->
        <div class="space-y-4">
          <h3 class="text-lg font-semibold text-gray-900">Additional Resources</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- PDF Attachment -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">PDF Attachment</label>
              <input 
                type="file" 
                accept=".pdf"
                @change="handlePdfUpload"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <p class="text-sm text-gray-500 mt-1">
                Upload a PDF resource for this lesson
              </p>
            </div>

            <!-- Quiz Selection -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Associated Quiz</label>
              <select 
                v-model="form.quiz_id" 
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">No Quiz</option>
                <!-- Quiz options would be loaded from API -->
              </select>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex gap-4 pt-6 border-t border-gray-200">
          <button
            type="button"
            @click="$emit('close')"
            class="flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            :disabled="isSubmitting || !isFormValid"
            class="flex-1 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
          >
            {{ isSubmitting ? 'Saving...' : (lesson ? 'Update Lesson' : 'Create Lesson') }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useVideos, type VideoLesson } from '../composables/useVideos'
import { useCourses } from '../composables/useCourses'

// Props
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  lesson: {
    type: Object as () => VideoLesson | null,
    default: null
  },
  courses: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['close', 'save'])

// Composables
const { createVideoLesson, updateVideoLesson, extractPlaylistId: extractId } = useVideos()
const { courseLevels, fetchCourseLevels } = useCourses()

// State
const isSubmitting = ref(false)
const form = ref({
  course_id: '',
  course_level_id: '',
  day_number: 1,
  sequence: 1,
  title: '',
  youtube_playlist_url: '',
  youtube_playlist_id: '',
  video_attachment_id: null,
  pdf_attachment_id: null,
  quiz_id: '',
  requires_payment: false
})

// Computed
const filteredLevels = computed(() => {
  if (!form.value.course_id) return []
  return courseLevels.value.filter(level => 
    level.course_id.toString() === form.value.course_id.toString()
  )
})

const previewEmbedUrl = computed(() => {
  if (!form.value.youtube_playlist_id) return ''
  return `https://www.youtube.com/embed/videoseries?list=${form.value.youtube_playlist_id}&rel=0&modestbranding=1&showinfo=0`
})

const isFormValid = computed(() => {
  return form.value.course_level_id && 
         form.value.day_number && 
         form.value.sequence && 
         form.value.title.trim()
})

// Methods
const initializeForm = () => {
  if (props.lesson) {
    form.value = {
      course_id: props.lesson.course_level?.course_id?.toString() || '',
      course_level_id: props.lesson.course_level_id.toString(),
      day_number: props.lesson.day_number,
      sequence: props.lesson.sequence,
      title: props.lesson.title,
      youtube_playlist_url: props.lesson.youtube_playlist_url || '',
      youtube_playlist_id: props.lesson.youtube_playlist_id || '',
      video_attachment_id: props.lesson.video_attachment_id,
      pdf_attachment_id: props.lesson.pdf_attachment_id,
      quiz_id: props.lesson.quiz_id?.toString() || '',
      requires_payment: props.lesson.requires_payment
    }
  } else {
    // Reset form for new lesson
    form.value = {
      course_id: '',
      course_level_id: '',
      day_number: 1,
      sequence: 1,
      title: '',
      youtube_playlist_url: '',
      youtube_playlist_id: '',
      video_attachment_id: null,
      pdf_attachment_id: null,
      quiz_id: '',
      requires_payment: false
    }
  }
}

const onCourseChange = () => {
  form.value.course_level_id = '' // Reset level when course changes
}

const extractPlaylistId = () => {
  if (form.value.youtube_playlist_url) {
    const playlistId = extractId(form.value.youtube_playlist_url)
    if (playlistId) {
      form.value.youtube_playlist_id = playlistId
    }
  }
}

const handlePdfUpload = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (file) {
    // Handle PDF upload - would integrate with file upload API
    console.log('PDF file selected:', file.name)
  }
}

const handleSubmit = async () => {
  if (!isFormValid.value) return

  isSubmitting.value = true
  try {
    const lessonData = {
      course_level_id: parseInt(form.value.course_level_id),
      day_number: form.value.day_number,
      sequence: form.value.sequence,
      title: form.value.title,
      youtube_playlist_url: form.value.youtube_playlist_url || undefined,
      youtube_playlist_id: form.value.youtube_playlist_id || undefined,
      video_attachment_id: form.value.video_attachment_id || undefined,
      pdf_attachment_id: form.value.pdf_attachment_id || undefined,
      quiz_id: form.value.quiz_id ? parseInt(form.value.quiz_id) : undefined,
      requires_payment: form.value.requires_payment
    }

    if (props.lesson) {
      await updateVideoLesson(props.lesson.id, lessonData)
    } else {
      await createVideoLesson(lessonData)
    }

    emit('save')
  } catch (error) {
    console.error('Failed to save lesson:', error)
    alert('Failed to save lesson. Please try again.')
  } finally {
    isSubmitting.value = false
  }
}

// Watch for modal open/close
watch(() => props.isOpen, (isOpen) => {
  if (isOpen) {
    initializeForm()
  }
})

// Initialize
onMounted(() => {
  fetchCourseLevels()
})
</script>
