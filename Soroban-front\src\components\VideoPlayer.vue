<template>
  <div class="video-player-container">
    <!-- Loading State -->
    <div v-if="isLoading" class="flex items-center justify-center h-64 bg-gray-100 rounded-lg">
      <div class="text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
        <p class="text-gray-600">Loading video...</p>
      </div>
    </div>

    <!-- Access Denied -->
    <div v-else-if="!hasAccess && requiresSubscription" class="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg p-8 text-center">
      <div class="bg-blue-500 rounded-full p-4 w-16 h-16 mx-auto mb-4">
        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
        </svg>
      </div>
      <h3 class="text-xl font-bold text-gray-900 mb-2">Premium Content</h3>
      <p class="text-gray-600 mb-6">{{ accessMessage || 'This video requires an active subscription to view.' }}</p>
      <button @click="$emit('subscribe')" class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors">
        Subscribe to Access
      </button>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
      <div class="text-red-500 mb-2">
        <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
        </svg>
      </div>
      <h3 class="text-lg font-semibold text-red-800 mb-2">Video Unavailable</h3>
      <p class="text-red-600 mb-4">{{ error }}</p>
      <button @click="retryLoad" class="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors">
        Try Again
      </button>
    </div>

    <!-- Video Player -->
    <div v-else-if="embedUrl" class="relative">
      <!-- Video Info Header -->
      <div v-if="showInfo && lesson" class="bg-white rounded-t-lg border-b p-4">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-semibold text-gray-900">{{ lesson.title }}</h3>
            <p class="text-sm text-gray-600">
              {{ lesson.course_level?.course?.name }} - {{ lesson.course_level?.title }}
            </p>
          </div>
          <div class="flex items-center gap-2">
            <span v-if="lesson.requires_payment" class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">
              Premium
            </span>
            <span v-else class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
              Free
            </span>
          </div>
        </div>
      </div>

      <!-- YouTube Embed -->
      <div class="relative" :class="showInfo ? 'rounded-b-lg overflow-hidden' : 'rounded-lg overflow-hidden'">
        <div class="aspect-video">
          <iframe
            :src="embedUrl"
            :title="lesson?.title || 'Video Player'"
            class="w-full h-full"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowfullscreen
            @load="onVideoLoad"
            @error="onVideoError"
          ></iframe>
        </div>
        
        <!-- Video Controls Overlay -->
        <div v-if="showControls" class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
          <div class="flex items-center justify-between text-white">
            <div class="flex items-center gap-4">
              <button @click="togglePlay" class="hover:text-blue-300 transition-colors">
                <svg v-if="isPlaying" class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
                </svg>
                <svg v-else class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M8 5v14l11-7z"/>
                </svg>
              </button>
              <span class="text-sm">{{ formatTime(currentTime) }} / {{ formatTime(duration) }}</span>
            </div>
            <div class="flex items-center gap-2">
              <button @click="toggleFullscreen" class="hover:text-blue-300 transition-colors">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 1v4m0 0h-4m4 0l-5-5"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Video Progress Bar -->
      <div v-if="showProgress" class="bg-gray-200 h-1 rounded-full mt-2">
        <div 
          class="bg-blue-500 h-1 rounded-full transition-all duration-300"
          :style="{ width: `${progressPercentage}%` }"
        ></div>
      </div>
    </div>

    <!-- No Video Available -->
    <div v-else class="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
      <div class="text-gray-400 mb-4">
        <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
        </svg>
      </div>
      <h3 class="text-lg font-medium text-gray-900 mb-2">No Video Available</h3>
      <p class="text-gray-600">This lesson doesn't have a video yet.</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useVideos, type VideoLesson } from '../composables/useVideos'

// Props
const props = defineProps({
  lessonId: {
    type: Number,
    required: true
  },
  autoplay: {
    type: Boolean,
    default: false
  },
  showInfo: {
    type: Boolean,
    default: true
  },
  showControls: {
    type: Boolean,
    default: true
  },
  showProgress: {
    type: Boolean,
    default: true
  },
  startTime: {
    type: Number,
    default: 0
  },
  endTime: {
    type: Number,
    default: 0
  }
})

// Emits
const emit = defineEmits(['subscribe', 'videoLoad', 'videoError', 'progress'])

// Composables
const { 
  currentVideo: lesson, 
  currentPlaylist, 
  isLoading, 
  error, 
  fetchVideoLesson, 
  getPlaylistEmbed, 
  checkVideoAccess 
} = useVideos()

// State
const hasAccess = ref(false)
const requiresSubscription = ref(false)
const accessMessage = ref('')
const isPlaying = ref(false)
const currentTime = ref(0)
const duration = ref(0)
const progressPercentage = ref(0)

// Computed
const embedUrl = computed(() => {
  if (!currentPlaylist.value?.embed_url) return null
  
  const url = new URL(currentPlaylist.value.embed_url)
  if (props.autoplay) url.searchParams.set('autoplay', '1')
  if (props.startTime) url.searchParams.set('start', props.startTime.toString())
  if (props.endTime) url.searchParams.set('end', props.endTime.toString())
  
  return url.toString()
})

// Methods
const loadVideo = async () => {
  if (!props.lessonId) return

  try {
    // Check access first
    const access = await checkVideoAccess(props.lessonId)
    hasAccess.value = access.has_access
    requiresSubscription.value = access.requires_subscription
    accessMessage.value = access.message || ''

    if (!hasAccess.value) return

    // Load lesson details
    await fetchVideoLesson(props.lessonId)
    
    // Get playlist embed
    await getPlaylistEmbed(props.lessonId, {
      autoplay: props.autoplay,
      start: props.startTime || undefined,
      end: props.endTime || undefined
    })

  } catch (err) {
    console.error('Failed to load video:', err)
  }
}

const retryLoad = () => {
  error.value = null
  loadVideo()
}

const onVideoLoad = () => {
  emit('videoLoad', lesson.value)
}

const onVideoError = () => {
  emit('videoError', 'Failed to load video')
}

const togglePlay = () => {
  isPlaying.value = !isPlaying.value
  // Note: YouTube iframe API would be needed for actual play/pause control
}

const toggleFullscreen = () => {
  const iframe = document.querySelector('iframe')
  if (iframe) {
    if (document.fullscreenElement) {
      document.exitFullscreen()
    } else {
      iframe.requestFullscreen()
    }
  }
}

const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

const updateProgress = () => {
  if (duration.value > 0) {
    progressPercentage.value = (currentTime.value / duration.value) * 100
    emit('progress', {
      currentTime: currentTime.value,
      duration: duration.value,
      percentage: progressPercentage.value
    })
  }
}

// Watch for lesson ID changes
watch(() => props.lessonId, (newId) => {
  if (newId) {
    loadVideo()
  }
}, { immediate: true })

// Lifecycle
onMounted(() => {
  loadVideo()
})

onUnmounted(() => {
  // Cleanup if needed
})
</script>

<style scoped>
.video-player-container {
  @apply w-full;
}

.aspect-video {
  aspect-ratio: 16 / 9;
}

/* Custom scrollbar for video controls */
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}
</style>
