<template>
  <div v-if="isOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-2xl shadow-2xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <div>
          <h2 class="text-2xl font-bold text-gray-900">{{ lesson?.title }}</h2>
          <p class="text-gray-600">
            {{ lesson?.course_level?.course?.name }} - {{ lesson?.course_level?.title }}
          </p>
        </div>
        <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600 transition-colors">
          <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>

      <!-- Content -->
      <div class="p-6">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <!-- Video Player -->
          <div class="lg:col-span-2">
            <VideoPlayer
              v-if="lesson?.id"
              :lessonId="lesson.id"
              :showInfo="false"
              @subscribe="handleSubscribe"
              @videoLoad="onVideoLoad"
              @videoError="onVideoError"
              @progress="onProgress"
            />
          </div>

          <!-- Lesson Details -->
          <div class="space-y-6">
            <!-- Lesson Info -->
            <div class="bg-gray-50 rounded-lg p-4">
              <h3 class="text-lg font-semibold text-gray-900 mb-3">Lesson Details</h3>
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="text-gray-600">Day:</span>
                  <span class="font-medium">{{ lesson?.day_number }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Sequence:</span>
                  <span class="font-medium">{{ lesson?.sequence }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Access:</span>
                  <span :class="lesson?.requires_payment ? 'text-yellow-600' : 'text-green-600'" class="font-medium">
                    {{ lesson?.requires_payment ? 'Premium' : 'Free' }}
                  </span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Created:</span>
                  <span class="font-medium">{{ formatDate(lesson?.created_at) }}</span>
                </div>
              </div>
            </div>

            <!-- Course Progress -->
            <div v-if="courseProgress" class="bg-blue-50 rounded-lg p-4">
              <h3 class="text-lg font-semibold text-gray-900 mb-3">Course Progress</h3>
              <div class="space-y-3">
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Completed Lessons:</span>
                  <span class="font-medium">{{ courseProgress.completed }} / {{ courseProgress.total }}</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    class="bg-blue-500 h-2 rounded-full transition-all duration-300"
                    :style="{ width: `${courseProgress.percentage}%` }"
                  ></div>
                </div>
                <div class="text-center text-sm font-medium text-blue-600">
                  {{ Math.round(courseProgress.percentage) }}% Complete
                </div>
              </div>
            </div>

            <!-- Resources -->
            <div class="bg-gray-50 rounded-lg p-4">
              <h3 class="text-lg font-semibold text-gray-900 mb-3">Resources</h3>
              <div class="space-y-2">
                <!-- PDF Attachment -->
                <div v-if="lesson?.pdf_attachment_id" class="flex items-center gap-3 p-2 bg-white rounded border">
                  <div class="bg-red-100 p-2 rounded">
                    <svg class="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"/>
                    </svg>
                  </div>
                  <div class="flex-1">
                    <p class="text-sm font-medium text-gray-900">Lesson PDF</p>
                    <p class="text-xs text-gray-500">Downloadable resource</p>
                  </div>
                  <button class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                    Download
                  </button>
                </div>

                <!-- Quiz -->
                <div v-if="lesson?.quiz_id" class="flex items-center gap-3 p-2 bg-white rounded border">
                  <div class="bg-purple-100 p-2 rounded">
                    <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  </div>
                  <div class="flex-1">
                    <p class="text-sm font-medium text-gray-900">Lesson Quiz</p>
                    <p class="text-xs text-gray-500">Test your knowledge</p>
                  </div>
                  <button @click="startQuiz" class="text-purple-600 hover:text-purple-800 text-sm font-medium">
                    Start Quiz
                  </button>
                </div>

                <!-- YouTube Playlist -->
                <div v-if="lesson?.youtube_playlist_id" class="flex items-center gap-3 p-2 bg-white rounded border">
                  <div class="bg-red-100 p-2 rounded">
                    <svg class="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                    </svg>
                  </div>
                  <div class="flex-1">
                    <p class="text-sm font-medium text-gray-900">YouTube Playlist</p>
                    <p class="text-xs text-gray-500">Watch on YouTube</p>
                  </div>
                  <a 
                    :href="`https://www.youtube.com/playlist?list=${lesson.youtube_playlist_id}`" 
                    target="_blank"
                    class="text-red-600 hover:text-red-800 text-sm font-medium"
                  >
                    Open
                  </a>
                </div>

                <!-- No Resources -->
                <div v-if="!hasResources" class="text-center py-4 text-gray-500 text-sm">
                  No additional resources available
                </div>
              </div>
            </div>

            <!-- Navigation -->
            <div class="bg-gray-50 rounded-lg p-4">
              <h3 class="text-lg font-semibold text-gray-900 mb-3">Navigation</h3>
              <div class="flex gap-2">
                <button 
                  @click="goToPrevious"
                  :disabled="!hasPrevious"
                  class="flex-1 bg-gray-500 text-white px-3 py-2 rounded-lg hover:bg-gray-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors text-sm"
                >
                  ← Previous
                </button>
                <button 
                  @click="goToNext"
                  :disabled="!hasNext"
                  class="flex-1 bg-blue-500 text-white px-3 py-2 rounded-lg hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed transition-colors text-sm"
                >
                  Next →
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useVideos, type VideoLesson } from '../composables/useVideos'
import VideoPlayer from './VideoPlayer.vue'

// Props
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  lesson: {
    type: Object as () => VideoLesson | null,
    default: null
  }
})

// Emits
const emit = defineEmits(['close', 'navigate'])

// Composables
const { videos, fetchVideoLessons } = useVideos()

// State
const courseProgress = ref({
  completed: 0,
  total: 0,
  percentage: 0
})

// Computed
const hasResources = computed(() => {
  return props.lesson?.pdf_attachment_id || 
         props.lesson?.quiz_id || 
         props.lesson?.youtube_playlist_id
})

const hasPrevious = computed(() => {
  if (!props.lesson) return false
  const currentIndex = videos.value.findIndex(v => v.id === props.lesson?.id)
  return currentIndex > 0
})

const hasNext = computed(() => {
  if (!props.lesson) return false
  const currentIndex = videos.value.findIndex(v => v.id === props.lesson?.id)
  return currentIndex < videos.value.length - 1
})

// Methods
const formatDate = (dateString?: string) => {
  if (!dateString) return 'N/A'
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric' 
  })
}

const handleSubscribe = () => {
  // Handle subscription logic
  console.log('Subscribe clicked')
}

const onVideoLoad = (lesson: VideoLesson) => {
  console.log('Video loaded:', lesson.title)
}

const onVideoError = (error: string) => {
  console.error('Video error:', error)
}

const onProgress = (progressData: any) => {
  console.log('Video progress:', progressData)
}

const startQuiz = () => {
  if (props.lesson?.quiz_id) {
    // Navigate to quiz
    console.log('Starting quiz:', props.lesson.quiz_id)
  }
}

const goToPrevious = () => {
  if (!hasPrevious.value) return
  const currentIndex = videos.value.findIndex(v => v.id === props.lesson?.id)
  const previousLesson = videos.value[currentIndex - 1]
  emit('navigate', previousLesson)
}

const goToNext = () => {
  if (!hasNext.value) return
  const currentIndex = videos.value.findIndex(v => v.id === props.lesson?.id)
  const nextLesson = videos.value[currentIndex + 1]
  emit('navigate', nextLesson)
}

const loadCourseProgress = async () => {
  if (!props.lesson?.course_level_id) return
  
  // This would typically fetch from an API
  // For now, we'll simulate progress data
  courseProgress.value = {
    completed: 5,
    total: 10,
    percentage: 50
  }
}

// Initialize
onMounted(() => {
  if (props.isOpen && props.lesson) {
    loadCourseProgress()
  }
})
</script>
