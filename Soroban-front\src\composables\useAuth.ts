import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { apiService, type User } from '../services/api'

// Global auth state
const user = ref<User | null>(null)
const isAuthenticated = ref(false)
const isLoading = ref(false)

export function useAuth() {
  const router = useRouter()

  // Initialize auth state from localStorage
  const initAuth = () => {
    const token = apiService.getToken()
    const userData = apiService.getCurrentUser()
    
    if (token && userData) {
      user.value = userData
      isAuthenticated.value = true
    }
  }

  // Login function
  const login = async (credentials: { email: string; password: string; remember?: boolean }) => {
    isLoading.value = true
    try {
      const response = await apiService.login(credentials)
      
      if (response.success && response.data) {
        user.value = response.data.user
        isAuthenticated.value = true
        return { success: true, user: response.data.user }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error: any) {
      return { success: false, message: error.message }
    } finally {
      isLoading.value = false
    }
  }

  // Register function
  const register = async (userData: {
    first_name: string
    second_name: string
    email: string
    password: string
    password_confirmation: string
    age: number
    role: 'teacher' | 'student'
    age_group: 'kids' | 'adults'
    user_type?: 'regular' | 'company'
    company_id?: number
  }) => {
    isLoading.value = true
    try {
      const response = await apiService.register(userData)
      
      if (response.success) {
        return { success: true, message: response.message }
      } else {
        return { success: false, message: response.message, errors: response.errors }
      }
    } catch (error: any) {
      return { success: false, message: error.message }
    } finally {
      isLoading.value = false
    }
  }

  // Logout function
  const logout = async () => {
    isLoading.value = true
    try {
      await apiService.logout()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      user.value = null
      isAuthenticated.value = false
      isLoading.value = false
      router.push('/login')
    }
  }

  // Refresh user data
  const refreshUser = async () => {
    if (!isAuthenticated.value) return
    
    try {
      const response = await apiService.getUser()
      if (response.success && response.data) {
        user.value = response.data
      }
    } catch (error) {
      console.error('Failed to refresh user:', error)
      // If token is invalid, logout
      logout()
    }
  }

  // Check if user has specific role
  const hasRole = (role: string | string[]) => {
    if (!user.value) return false
    
    if (Array.isArray(role)) {
      return role.includes(user.value.role)
    }
    
    return user.value.role === role
  }

  // Check if user is admin (superAdmin or admin)
  const isAdmin = computed(() => {
    return hasRole(['superAdmin', 'admin'])
  })

  // Check if user is teacher
  const isTeacher = computed(() => {
    return hasRole('teacher')
  })

  // Check if user is student
  const isStudent = computed(() => {
    return hasRole('student')
  })

  // Get dashboard route based on user role
  const getDashboardRoute = () => {
    if (!user.value) return '/'
    
    switch (user.value.role) {
      case 'superAdmin':
        return '/super-admin-dashboard'
      case 'admin':
        return '/admin-dashboard'
      case 'teacher':
        return '/teacher-dashboard'
      case 'student':
        return '/student-dashboard'
      default:
        return '/'
    }
  }

  // Initialize auth on first load
  if (!user.value && !isAuthenticated.value) {
    initAuth()
  }

  return {
    // State
    user: computed(() => user.value),
    isAuthenticated: computed(() => isAuthenticated.value),
    isLoading: computed(() => isLoading.value),
    isAdmin,
    isTeacher,
    isStudent,
    
    // Methods
    login,
    register,
    logout,
    refreshUser,
    hasRole,
    getDashboardRoute,
    initAuth
  }
}
