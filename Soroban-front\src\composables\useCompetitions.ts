import { ref, computed } from 'vue'
import { apiService, type Competition } from '../services/api'

export function useCompetitions() {
  const competitions = ref<Competition[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Fetch all competitions
  const fetchCompetitions = async () => {
    isLoading.value = true
    error.value = null
    try {
      const response = await apiService.getCompetitions()
      if (response.success && response.data) {
        competitions.value = response.data
      }
    } catch (err: any) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
  }

  // Fetch competition by ID
  const fetchCompetition = async (id: number) => {
    isLoading.value = true
    error.value = null
    try {
      const response = await apiService.getCompetition(id)
      if (response.success && response.data) {
        return response.data
      }
    } catch (err: any) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
    return null
  }

  // Participate in competition
  const participateInCompetition = async (competitionId: number) => {
    isLoading.value = true
    error.value = null
    try {
      const response = await apiService.participateInCompetition(competitionId)
      if (response.success) {
        return true
      }
    } catch (err: any) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
    return false
  }

  // Create competition (admin/teacher only)
  const createCompetition = async (competitionData: Partial<Competition>) => {
    isLoading.value = true
    error.value = null
    try {
      const response = await apiService.createCompetition(competitionData)
      if (response.success && response.data) {
        competitions.value.push(response.data)
        return response.data
      }
    } catch (err: any) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
    return null
  }

  // Computed properties
  const monthlyCompetitions = computed(() => 
    competitions.value.filter(comp => comp.type === 'monthly')
  )

  const seasonalCompetitions = computed(() => 
    competitions.value.filter(comp => comp.type === 'seasonal')
  )

  const ongoingCompetitions = computed(() => 
    competitions.value.filter(comp => comp.status === 'ongoing')
  )

  const upcomingCompetitions = computed(() => 
    competitions.value.filter(comp => comp.status === 'upcoming')
  )

  const completedCompetitions = computed(() => 
    competitions.value.filter(comp => comp.status === 'completed')
  )

  const competitionsByType = computed(() => ({
    monthly: monthlyCompetitions.value,
    seasonal: seasonalCompetitions.value
  }))

  const competitionsByStatus = computed(() => ({
    ongoing: ongoingCompetitions.value,
    upcoming: upcomingCompetitions.value,
    completed: completedCompetitions.value
  }))

  return {
    // State
    competitions,
    isLoading,
    error,
    
    // Computed
    monthlyCompetitions,
    seasonalCompetitions,
    ongoingCompetitions,
    upcomingCompetitions,
    completedCompetitions,
    competitionsByType,
    competitionsByStatus,
    
    // Methods
    fetchCompetitions,
    fetchCompetition,
    participateInCompetition,
    createCompetition
  }
}
