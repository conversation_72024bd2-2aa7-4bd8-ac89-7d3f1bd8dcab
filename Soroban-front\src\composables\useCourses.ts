import { ref, computed } from 'vue'
import { apiService, type Course, type CourseLevel, type Lesson } from '../services/api'

export function useCourses() {
  const courses = ref<Course[]>([])
  const courseLevels = ref<CourseLevel[]>([])
  const lessons = ref<Lesson[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Fetch all courses
  const fetchCourses = async () => {
    isLoading.value = true
    error.value = null
    try {
      const response = await apiService.getCourses()
      if (response.success && response.data) {
        courses.value = response.data
      }
    } catch (err: any) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
  }

  // Fetch course by ID
  const fetchCourse = async (id: number) => {
    isLoading.value = true
    error.value = null
    try {
      const response = await apiService.getCourse(id)
      if (response.success && response.data) {
        return response.data
      }
    } catch (err: any) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
    return null
  }

  // Fetch course levels
  const fetchCourseLevels = async (courseId?: number) => {
    isLoading.value = true
    error.value = null
    try {
      const response = await apiService.getCourseLevels(courseId)
      if (response.success && response.data) {
        courseLevels.value = response.data
      }
    } catch (err: any) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
  }

  // Fetch lessons by level
  const fetchLessonsByLevel = async (levelId: number) => {
    isLoading.value = true
    error.value = null
    try {
      const response = await apiService.getLessonsByLevel(levelId)
      if (response.success && response.data) {
        lessons.value = response.data
      }
    } catch (err: any) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
  }

  // Create course (admin/teacher only)
  const createCourse = async (courseData: Partial<Course>) => {
    isLoading.value = true
    error.value = null
    try {
      const response = await apiService.createCourse(courseData)
      if (response.success && response.data) {
        courses.value.push(response.data)
        return response.data
      }
    } catch (err: any) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
    return null
  }

  // Update course
  const updateCourse = async (id: number, courseData: Partial<Course>) => {
    isLoading.value = true
    error.value = null
    try {
      const response = await apiService.updateCourse(id, courseData)
      if (response.success && response.data) {
        const index = courses.value.findIndex(c => c.id === id)
        if (index !== -1) {
          courses.value[index] = response.data
        }
        return response.data
      }
    } catch (err: any) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
    return null
  }

  // Delete course
  const deleteCourse = async (id: number) => {
    isLoading.value = true
    error.value = null
    try {
      const response = await apiService.deleteCourse(id)
      if (response.success) {
        courses.value = courses.value.filter(c => c.id !== id)
        return true
      }
    } catch (err: any) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
    return false
  }

  // Computed properties
  const activeCourses = computed(() => 
    courses.value.filter(course => course.status === 'active')
  )

  const courseCount = computed(() => courses.value.length)

  return {
    // State
    courses,
    courseLevels,
    lessons,
    isLoading,
    error,
    
    // Computed
    activeCourses,
    courseCount,
    
    // Methods
    fetchCourses,
    fetchCourse,
    fetchCourseLevels,
    fetchLessonsByLevel,
    createCourse,
    updateCourse,
    deleteCourse
  }
}
