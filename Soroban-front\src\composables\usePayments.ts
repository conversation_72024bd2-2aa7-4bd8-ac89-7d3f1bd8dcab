import { ref, computed } from 'vue'
import { apiService } from '../services/api'

export interface Payment {
  id: number
  student_id: number
  level_id: number
  subscription_id: number
  amount: number
  transaction_code?: string
  transaction_pic?: string
  subscription_start_date?: string
  subscription_end_date?: string
  is_confirmed: boolean
  created_at: string
  updated_at: string
  student?: {
    id: number
    first_name: string
    second_name: string
    email: string
    profile?: any
  }
  level?: {
    id: number
    title: string
    price: number
    course?: {
      id: number
      title: string
    }
  }
  subscription?: {
    id: number
    renewal_status: string
  }
}

export interface PaymentStats {
  total_payments: number
  confirmed_payments: number
  pending_payments: number
  total_revenue: number
  revenue_this_month: number
  revenue_today: number
}

export function usePayments() {
  const payments = ref<Payment[]>([])
  const paymentStats = ref<PaymentStats | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Fetch all payments (admin/teacher)
  const fetchPayments = async (filters?: {
    student_id?: number
    confirmed?: boolean
    from_date?: string
    to_date?: string
    per_page?: number
  }) => {
    isLoading.value = true
    error.value = null
    try {
      const response = await apiService.getPayments(filters)
      if (response.success && response.data) {
        payments.value = response.data.data || response.data
      }
    } catch (err: any) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
  }

  // Fetch student's payments
  const fetchStudentPayments = async (studentId: number) => {
    isLoading.value = true
    error.value = null
    try {
      const response = await apiService.getStudentPayments(studentId)
      if (response.success && response.data) {
        payments.value = response.data
      }
    } catch (err: any) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
  }

  // Create new payment
  const createPayment = async (paymentData: {
    student_id: number
    level_id: number
    subscription_id: number
    amount: number
    transaction_code?: string
    transaction_pic?: string
    subscription_start_date?: string
    subscription_end_date?: string
  }) => {
    isLoading.value = true
    error.value = null
    try {
      const response = await apiService.createPayment(paymentData)
      if (response.success && response.data) {
        payments.value.unshift(response.data)
        return response.data
      }
    } catch (err: any) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
    return null
  }

  // Update payment
  const updatePayment = async (paymentId: number, updateData: Partial<Payment>) => {
    isLoading.value = true
    error.value = null
    try {
      const response = await apiService.updatePayment(paymentId, updateData)
      if (response.success && response.data) {
        const index = payments.value.findIndex(p => p.id === paymentId)
        if (index !== -1) {
          payments.value[index] = response.data
        }
        return response.data
      }
    } catch (err: any) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
    return null
  }

  // Confirm payment (admin/teacher)
  const confirmPayment = async (paymentId: number) => {
    isLoading.value = true
    error.value = null
    try {
      const response = await apiService.confirmPayment(paymentId)
      if (response.success && response.data) {
        const index = payments.value.findIndex(p => p.id === paymentId)
        if (index !== -1) {
          payments.value[index] = response.data
        }
        return response.data
      }
    } catch (err: any) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
    return null
  }

  // Fetch payment statistics
  const fetchPaymentStats = async () => {
    isLoading.value = true
    error.value = null
    try {
      const response = await apiService.getPaymentReports()
      if (response.success && response.data) {
        paymentStats.value = response.data
      }
    } catch (err: any) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
  }

  // Upload payment receipt
  const uploadPaymentReceipt = async (file: File) => {
    isLoading.value = true
    error.value = null
    try {
      const response = await apiService.uploadFile(file, 'payment_receipt')
      if (response.success && response.data) {
        return response.data.url || response.data.path
      }
    } catch (err: any) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
    return null
  }

  // Computed properties
  const pendingPayments = computed(() => 
    payments.value.filter(payment => !payment.is_confirmed)
  )

  const confirmedPayments = computed(() => 
    payments.value.filter(payment => payment.is_confirmed)
  )

  const totalRevenue = computed(() => 
    confirmedPayments.value.reduce((sum, payment) => sum + payment.amount, 0)
  )

  const pendingRevenue = computed(() => 
    pendingPayments.value.reduce((sum, payment) => sum + payment.amount, 0)
  )

  return {
    // State
    payments,
    paymentStats,
    isLoading,
    error,
    
    // Computed
    pendingPayments,
    confirmedPayments,
    totalRevenue,
    pendingRevenue,
    
    // Methods
    fetchPayments,
    fetchStudentPayments,
    createPayment,
    updatePayment,
    confirmPayment,
    fetchPaymentStats,
    uploadPaymentReceipt
  }
}
