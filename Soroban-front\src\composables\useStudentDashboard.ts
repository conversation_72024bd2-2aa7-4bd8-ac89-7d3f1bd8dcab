import { ref, computed } from 'vue'
import { apiService, type StudentProgress, type Certificate, type Subscription, type Notification } from '../services/api'

export function useStudentDashboard() {
  const progress = ref<StudentProgress[]>([])
  const certificates = ref<Certificate[]>([])
  const myCourses = ref<Subscription[]>([])
  const notifications = ref<Notification[]>([])
  const dashboardStats = ref<any>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Fetch student dashboard data
  const fetchDashboardData = async () => {
    isLoading.value = true
    error.value = null
    try {
      const [dashboardResponse, progressResponse, certificatesResponse, coursesResponse, notificationsResponse] = await Promise.all([
        apiService.getStudentDashboard(),
        apiService.getMyProgress(),
        apiService.getMyCertificates(),
        apiService.getMyCourses(),
        apiService.getNotifications()
      ])

      if (dashboardResponse.success && dashboardResponse.data) {
        dashboardStats.value = dashboardResponse.data
      }

      if (progressResponse.success && progressResponse.data) {
        progress.value = progressResponse.data
      }

      if (certificatesResponse.success && certificatesResponse.data) {
        certificates.value = certificatesResponse.data
      }

      if (coursesResponse.success && coursesResponse.data) {
        myCourses.value = coursesResponse.data
      }

      if (notificationsResponse.success && notificationsResponse.data) {
        notifications.value = notificationsResponse.data
      }

    } catch (err: any) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
  }

  // Fetch my progress
  const fetchMyProgress = async () => {
    isLoading.value = true
    error.value = null
    try {
      const response = await apiService.getMyProgress()
      if (response.success && response.data) {
        progress.value = response.data
      }
    } catch (err: any) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
  }

  // Fetch my certificates
  const fetchMyCertificates = async () => {
    isLoading.value = true
    error.value = null
    try {
      const response = await apiService.getMyCertificates()
      if (response.success && response.data) {
        certificates.value = response.data
      }
    } catch (err: any) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
  }

  // Fetch my courses
  const fetchMyCourses = async () => {
    isLoading.value = true
    error.value = null
    try {
      const response = await apiService.getMyCourses()
      if (response.success && response.data) {
        myCourses.value = response.data
      }
    } catch (err: any) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
  }

  // Fetch notifications
  const fetchNotifications = async () => {
    isLoading.value = true
    error.value = null
    try {
      const response = await apiService.getNotifications()
      if (response.success && response.data) {
        notifications.value = response.data
      }
    } catch (err: any) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
  }

  // Mark notification as read
  const markNotificationAsRead = async (notificationId: number) => {
    try {
      const response = await apiService.markNotificationAsRead(notificationId)
      if (response.success) {
        const notification = notifications.value.find(n => n.id === notificationId)
        if (notification) {
          notification.read_at = new Date().toISOString()
        }
      }
    } catch (err: any) {
      error.value = err.message
    }
  }

  // Mark all notifications as read
  const markAllNotificationsAsRead = async () => {
    try {
      const response = await apiService.markAllNotificationsAsRead()
      if (response.success) {
        notifications.value.forEach(notification => {
          notification.read_at = new Date().toISOString()
        })
      }
    } catch (err: any) {
      error.value = err.message
    }
  }

  // Subscribe to course
  const subscribeToCourse = async (courseLevelId: number) => {
    isLoading.value = true
    error.value = null
    try {
      const response = await apiService.subscribe(courseLevelId)
      if (response.success && response.data) {
        myCourses.value.push(response.data)
        return response.data
      }
    } catch (err: any) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
    return null
  }

  // Computed properties
  const totalCertificates = computed(() => certificates.value.length)
  
  const activeCourses = computed(() => 
    myCourses.value.filter(course => course.status === 'active')
  )
  
  const completedCourses = computed(() => 
    progress.value.filter(p => p.status === 'completed').length
  )
  
  const unreadNotifications = computed(() => 
    notifications.value.filter(n => !n.read_at)
  )
  
  const unreadNotificationCount = computed(() => unreadNotifications.value.length)
  
  const overallProgress = computed(() => {
    if (progress.value.length === 0) return 0
    const totalProgress = progress.value.reduce((sum, p) => sum + p.progress_percentage, 0)
    return Math.round(totalProgress / progress.value.length)
  })

  // Dashboard stats computed
  const dashboardSummary = computed(() => ({
    totalCourses: activeCourses.value.length,
    completedCourses: completedCourses.value,
    totalCertificates: totalCertificates.value,
    overallProgress: overallProgress.value,
    unreadNotifications: unreadNotificationCount.value
  }))

  return {
    // State
    progress,
    certificates,
    myCourses,
    notifications,
    dashboardStats,
    isLoading,
    error,
    
    // Computed
    totalCertificates,
    activeCourses,
    completedCourses,
    unreadNotifications,
    unreadNotificationCount,
    overallProgress,
    dashboardSummary,
    
    // Methods
    fetchDashboardData,
    fetchMyProgress,
    fetchMyCertificates,
    fetchMyCourses,
    fetchNotifications,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    subscribeToCourse
  }
}
