import { ref, computed } from 'vue'
import { apiService } from '../services/api'

export interface VideoLesson {
  id: number
  course_level_id: number
  day_number: number
  sequence: number
  title: string
  video_attachment_id?: number
  pdf_attachment_id?: number
  quiz_id?: number
  youtube_playlist_url?: string
  youtube_playlist_id?: string
  requires_payment: boolean
  created_at: string
  updated_at: string
  course_level?: {
    id: number
    course_id: number
    level_number: string
    title: string
    course?: {
      id: number
      name: string
      description: string
    }
  }
  video_attachment?: any
  pdf_attachment?: any
  quiz?: any
}

export interface VideoPlaylist {
  lesson_id: number
  lesson_title: string
  playlist_id: string
  embed_url: string
  iframe_html: string
  has_access: boolean
}

export interface VideoAccess {
  has_access: boolean
  requires_subscription: boolean
  message?: string
}

export function useVideos() {
  const videos = ref<VideoLesson[]>([])
  const currentVideo = ref<VideoLesson | null>(null)
  const currentPlaylist = ref<VideoPlaylist | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Fetch all video lessons
  const fetchVideoLessons = async (filters?: {
    course_id?: number
    course_level_id?: number
    requires_payment?: boolean
  }) => {
    isLoading.value = true
    error.value = null
    try {
      const response = await apiService.getVideoLessons(filters)
      if (response.success && response.data) {
        videos.value = response.data
      }
    } catch (err: any) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
  }

  // Fetch single video lesson
  const fetchVideoLesson = async (lessonId: number) => {
    isLoading.value = true
    error.value = null
    try {
      const response = await apiService.getVideoLesson(lessonId)
      if (response.success && response.data) {
        currentVideo.value = response.data
        return response.data
      }
    } catch (err: any) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
    return null
  }

  // Get YouTube playlist embed for a lesson
  const getPlaylistEmbed = async (lessonId: number, options?: {
    autoplay?: boolean
    start?: number
    end?: number
  }) => {
    isLoading.value = true
    error.value = null
    try {
      const response = await apiService.getPlaylistEmbed(lessonId, options)
      if (response.success && response.data) {
        currentPlaylist.value = response.data
        return response.data
      }
    } catch (err: any) {
      error.value = err.message
      if (err.requires_subscription) {
        return { requires_subscription: true, message: err.message }
      }
    } finally {
      isLoading.value = false
    }
    return null
  }

  // Create new video lesson
  const createVideoLesson = async (lessonData: {
    course_level_id: number
    day_number: number
    sequence: number
    title: string
    youtube_playlist_url?: string
    youtube_playlist_id?: string
    video_attachment_id?: number
    pdf_attachment_id?: number
    quiz_id?: number
    requires_payment?: boolean
  }) => {
    isLoading.value = true
    error.value = null
    try {
      const response = await apiService.createVideoLesson(lessonData)
      if (response.success && response.data) {
        videos.value.push(response.data)
        return response.data
      }
    } catch (err: any) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
    return null
  }

  // Update video lesson
  const updateVideoLesson = async (lessonId: number, lessonData: Partial<VideoLesson>) => {
    isLoading.value = true
    error.value = null
    try {
      const response = await apiService.updateVideoLesson(lessonId, lessonData)
      if (response.success && response.data) {
        const index = videos.value.findIndex(v => v.id === lessonId)
        if (index !== -1) {
          videos.value[index] = response.data
        }
        if (currentVideo.value?.id === lessonId) {
          currentVideo.value = response.data
        }
        return response.data
      }
    } catch (err: any) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
    return null
  }

  // Delete video lesson
  const deleteVideoLesson = async (lessonId: number) => {
    isLoading.value = true
    error.value = null
    try {
      const response = await apiService.deleteVideoLesson(lessonId)
      if (response.success) {
        videos.value = videos.value.filter(v => v.id !== lessonId)
        if (currentVideo.value?.id === lessonId) {
          currentVideo.value = null
        }
        return true
      }
    } catch (err: any) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
    return false
  }

  // Check video access
  const checkVideoAccess = async (lessonId: number): Promise<VideoAccess> => {
    try {
      const response = await apiService.checkVideoAccess(lessonId)
      if (response.success) {
        return response.data
      }
    } catch (err: any) {
      return {
        has_access: false,
        requires_subscription: true,
        message: err.message
      }
    }
    return { has_access: false, requires_subscription: false }
  }

  // Extract YouTube playlist ID from URL
  const extractPlaylistId = (url: string): string | null => {
    const regex = /[?&]list=([^#\&\?]*)/
    const match = url.match(regex)
    return match ? match[1] : null
  }

  // Generate YouTube embed URL
  const generateEmbedUrl = (playlistId: string, options?: {
    autoplay?: boolean
    start?: number
    end?: number
    loop?: boolean
  }): string => {
    const baseUrl = 'https://www.youtube.com/embed/videoseries'
    const params = new URLSearchParams({
      list: playlistId,
      rel: '0',
      modestbranding: '1',
      showinfo: '0'
    })

    if (options?.autoplay) params.append('autoplay', '1')
    if (options?.start) params.append('start', options.start.toString())
    if (options?.end) params.append('end', options.end.toString())
    if (options?.loop) params.append('loop', '1')

    return `${baseUrl}?${params.toString()}`
  }

  // Computed properties
  const freeVideos = computed(() => 
    videos.value.filter(video => !video.requires_payment)
  )

  const paidVideos = computed(() => 
    videos.value.filter(video => video.requires_payment)
  )

  const videosByCourse = computed(() => {
    const grouped: Record<string, VideoLesson[]> = {}
    videos.value.forEach(video => {
      const courseKey = video.course_level?.course?.name || 'Unknown Course'
      if (!grouped[courseKey]) {
        grouped[courseKey] = []
      }
      grouped[courseKey].push(video)
    })
    return grouped
  })

  const videosByLevel = computed(() => {
    const grouped: Record<string, VideoLesson[]> = {}
    videos.value.forEach(video => {
      const levelKey = `${video.course_level?.course?.name} - ${video.course_level?.title}` || 'Unknown Level'
      if (!grouped[levelKey]) {
        grouped[levelKey] = []
      }
      grouped[levelKey].push(video)
    })
    return grouped
  })

  return {
    // State
    videos,
    currentVideo,
    currentPlaylist,
    isLoading,
    error,
    
    // Computed
    freeVideos,
    paidVideos,
    videosByCourse,
    videosByLevel,
    
    // Methods
    fetchVideoLessons,
    fetchVideoLesson,
    getPlaylistEmbed,
    createVideoLesson,
    updateVideoLesson,
    deleteVideoLesson,
    checkVideoAccess,
    extractPlaylistId,
    generateEmbedUrl
  }
}
