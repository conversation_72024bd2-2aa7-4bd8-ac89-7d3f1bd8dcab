import { ref } from 'vue'

export type Locale = 'en' | 'ar'

export const translations = {
  en: {
    nav: {
      home: 'Home',
      courses: 'Courses',
      competition: 'Competition',
      login: 'Login',
      register: 'Register',
      guest: 'Log in as Guest',
      dashboard: 'Dashboard',
      profile: 'Profile',
      logout: 'Logout',
    },
    roles: {
      superAdmin: 'Super Admin',
      admin: 'Admin',
      teacher: 'Teacher',
      student: 'Student',
      guest: 'Guest',
    },
    loginPage: {
      title: 'Welcome Back!',
      subtitle: 'Enter your details to access your account.',
      email: 'Email',
      password: 'Password',
      remember: 'Keep me logged in',
      forgot: 'Forgot Password?',
      loginBtn: 'Log In',
      guestBtn: 'Continue as Guest',
      social: 'Or Sign In With',
      noAccount: "Don't have an account?",
      registerLink: 'Sign Up Now',
      haveCode: 'Have an activation code?',
      activateLink: 'Activate Your Account',
      loginLink: 'Log In'
    },
    register: {
      chooseRoleTitle: 'Join Our Community',
      chooseRoleSubtitle: 'Select your role to get started on your learning journey.',
      studentRole: 'Register as a Student',
      studentRoleDesc: 'For young learners and parents.',
      teacherRole: 'Register as a Teacher',
      teacherRoleDesc: 'For educators who want to teach.',
      
      student: {
        title: 'Create a Student Account',
        subtitle: 'Please enter the parent and student details below.',
        parentName: "Parent's Full Name",
        parentEmail: "Parent's Email",
        studentName: "Student's Full Name",
        parentPhone: "Parent's Phone Number",
        phonePlaceholder: 'Phone Number'
      },
      teacher: {
        title: 'Create a Teacher Account',
        subtitle: 'Please enter your details to get started.',
        fullName: 'Full Name',
        phone: 'Phone Number'
      },
      
      password: 'Password',
      confirmPassword: 'Confirm Password',
      iAccept: 'I accept the',
      terms: 'Terms and Conditions',
      searchCountry: 'Search for a country...',
      creatingAccount: 'Creating Account',
      acceptTermsError: 'You must accept the terms and conditions.',
      genericError: 'An unexpected error occurred. Please try again.',
      networkError: 'A network error occurred. Please check your connection and try again.',
      validation: {
        required: 'This field is required.',
        invalidEmail: 'Please enter a valid email address.',
        phoneInvalid: 'Please enter a valid phone number.',
        passwordMismatch: 'Passwords do not match.',
        passwordWeak: 'Password must be at least 8 characters long and include an uppercase letter, a lowercase letter, and a number.',
      },
      createAccountBtn: 'Create Account',
      email: 'Email Address',
    },
    forgotPassword: {
      title: 'Forgot Your Password?',
      subtitle: 'Enter your email and we will send a reset code.',
      email: 'Email',
      verifyBtn: 'Send Code',
      backToLogin: 'Back to Login',
      
      verifyTitle: 'Enter Verification Code',
      verifySubtitle: 'A 6-digit code has been sent to your email.',
      code: 'Verification Code',
      verifyCodeBtn: 'Verify Code',
      resend: "Didn't receive the code?",
      resendBtn: 'Resend',

      resetTitle: 'Reset Your Password',
      resetSubtitle: 'Create a new, strong password.',
      newPassword: 'New Password',
      confirmPassword: 'Confirm New Password',
      resetBtn: 'Confirm Password',
      haveCode: 'Have an activation code?',
      activateLink: 'Activate Your Account',
      loginLink: 'Log In'
    },
    logoutModal: {
      title: 'Are You Sure You Want To Log Out?',
      cancel: 'Cancel',
      logout: 'Log Out'
    },
    activateAccount: {
      title: 'Activate Your Account',
      subtitle: 'Enter your email and the activation code provided by the school.',
      email: 'Email Address',
      code: 'Activation Code',
      activateBtn: 'Activate Account',
      loadingBtn: 'Activating...'
    },
    brand: {
      main: 'Soroban',
      secondary: 'ALBARQ'
    },
    hero: {
      title: 'Develop Your Skills Step With Our Learning Platform!',
      subtitle: 'start your learning journey now with levels designed specifically for you',
      cta: 'Get started'
    },
    video: {
      alt: 'Video background',
      playButton: 'Play video'
    },
    mission: {
      title: 'Our Mission',
      subtitle: 'Empowering Tomorrow\'s Learners',
      description: 'We envision an educational environment where curiosity thrives, challenges are embraced, and every student feels inspired to grow. Our platform is built to support modern learners with interactive content, personalized experiences, and a community that motivates progress - every step of the way.',
      cta: 'Learn More',
      showLess: 'Show Less',
      additionalContent: {
        paragraph1: 'Our innovative approach combines cutting-edge technology with proven educational methodologies. We believe that learning should be accessible, engaging, and tailored to individual needs. Through our comprehensive curriculum, we provide students with the tools they need to succeed in an ever-evolving digital world.',
        paragraph2: 'We foster a collaborative learning environment where students can connect with peers, share knowledge, and build lasting relationships. Our community-driven approach ensures that every learner feels supported and motivated throughout their educational journey.',
        paragraph3: 'With continuous updates and improvements, we stay at the forefront of educational technology. Our commitment to excellence drives us to create the best possible learning experience for students of all ages and backgrounds.'
      },
      stats: {
        teachers: 'Qualified Teachers',
        students: 'Active Students'
      }
    },
    howItWorks: {
      title: 'How It Work',
      steps: {
        signup: {
          title: 'Sign Up Or Explore As Guest',
          description: 'Create an account as a student — or jump in quickly as a guest to try the experience.'
        },
        quiz: {
          title: 'Take The Placement Quiz',
          description: 'Answer a few short questions to help us recommend the right learning level for you'
        },
        learn: {
          title: 'Start Learning',
          description: 'Access your personalized level, watch lessons, and complete tasks at your own pace.'
        },
        challenges: {
          title: 'Join Challenges & Track Progress',
          description: 'Take part in monthly and seasonal challenges, earn badges, and watch your growth in real time.'
        }
      }
    },
    startLearningYoung: {
      title: 'Start Learning As Young',
      subtitle: 'Start your learning journey from scratch. This level is perfect for absolute beginners who want to build strong foundations step by step.',
      cta: 'Join Now',
      levels: {
        level1: 'Level 1',
        level2: 'Level 2',
        level3: 'Level 3',
        basicsExplorer: {
          title: 'Basics Explorer',
          description: 'Start your learning journey from scratch. This level is perfect for absolute beginners who want to build strong foundations step by step.'
        },
        skillBuilder: {
          title: 'Skill Builder',
          description: 'Start your learning journey from scratch. This level is perfect for absolute beginners who want to build strong foundations step by step.'
        },
        futureReady: {
          title: 'Future Ready',
          description: 'Start your learning journey from scratch. This level is perfect for absolute beginners who want to build strong foundations step by step.'
        }
      }
    },
    startLearningTeacher: {
      title: 'Start Learning To Be A Teacher',
      subtitle: 'Our course is structured into three progressive levels – from core fundamentals to advanced challenges. Choose the level that fits your experience and begin your journey with confidence.',
      cta: 'Join Now',
      levels: {
        level1: 'Level 1',
        level2: 'Level 2',
        level3: 'Level 3',
        basicsExplorer: {
          title: 'Basics Explorer',
          description: 'Start your learning journey from scratch. This level is perfect for absolute beginners who want to build strong foundations step by step.'
        },
        skillBuilder: {
          title: 'Skill Builder',
          description: 'Start your learning journey from scratch. This level is perfect for absolute beginners who want to build strong foundations step by step.'
        },
        futureReady: {
          title: 'Future Ready',
          description: 'Start your learning journey from scratch. This level is perfect for absolute beginners who want to build strong foundations step by step.'
        }
      }
    },
    bestTeachers: {
      title: 'Our Teachers',
      subtitle: 'Meet our dedicated and experienced teachers who are passionate about guiding you.',
      titles: {
        leadInstructor: 'Lead Instructor',
        mathSpecialist: 'Mathematics Specialist',
        scienceTeacher: 'Science Teacher',
        artCreativity: 'Art & Creativity'
      }
    },
    bestStudents: {
      title: 'Best Students This Month',
      subtitle: 'Meet our top-performing learners who stood out this month with their dedication, progress and achievements.',
      level: 'Level 1',
      points: 'points'
    },
    joinChallenge: {
      title: 'Join The Challenge - Monthly & Seasonal Competitions',
      subtitle: 'Put your skills to the test! Participate in fun, timed challenges, unlock badges, and see how you rank among other learners.',
      tabs: {
        all: 'All',
        monthly: 'Monthly',
        seasonal: 'Seasonal'
      },
      cta: 'Join Now',
      challenges: {
        focusBoost: {
          title: 'Focus Boost',
          description: 'Complete 5 lessons without skipping.',
          reward: 'Consistency Star'
        },
        marchSkillRush: {
          title: 'March Skill Rush',
          description: 'Earn 100 points in a week.',
          reward: 'Earn 100 Points In A Week'
        },
        springCodeQuest: {
          title: 'Spring Code Quest',
          description: 'Solve 10 logic puzzles in 5 days.',
          reward: 'Badge: Logic Sprinter'
        },
        weeklyAchiever: {
          title: 'Weekly Achiever',
          description: 'Earn 100 points in a week.',
          reward: 'Earn 100 Points In A Week'
        }
      }
    },
    testimonials: {
      title: 'What Our Students Say',
      subtitle: 'Hear directly from our satisfied learners and see how we\'ve made a difference.',
      title1: 'High School Student',
      title2: 'High School Student',
      title3: 'High School Student',
      text: 'I never thought learning could be this fun! The challenges really keep me motivated, and I love how the platform adapts to my level.'
    },
    partners: {
      title: 'Our Trusted Partners',
      companyName: 'Company Name'
    },
    footer: {
      tagline: 'Soroban',
      copyright: 'All rights reserved',
      sections: {
        explore: {
          title: 'Explore',
          links: {
            courseOverview: 'Course Overview',
            levels: 'Levels',
            challenges: 'Challenges',
            signIn: 'Sign In'
          }
        },
        aboutUs: {
          title: 'About Us',
          links: {
            ourVision: 'Our Vision',
            team: 'Team',
            contact: 'Contact',
            careers: 'Careers'
          }
        },
        resources: {
          title: 'Explore Resources',
          links: {
            helpCenter: 'Help Center',
            blog: 'Blog',
            placementTest: 'Placement Test',
            faqs: 'FAQs'
          }
        }
      }
    }
  },
  ar: {
    nav: {
      home: 'الرئيسية',
      courses: 'الدورات',
      competition: 'المسابقات',
      login: 'تسجيل الدخول',
      register: 'إنشاء حساب',
      guest: 'الدخول كزائر',
      dashboard: 'لوحة التحكم',
      profile: 'الملف الشخصي',
      logout: 'تسجيل الخروج',
    },
    roles: {
      superAdmin: 'مدير عام',
      admin: 'مدير',
      teacher: 'معلم',
      student: 'طالب',
      guest: 'زائر',
    },
    loginPage: {
      title: 'أهلاً بعودتك!',
      subtitle: 'أدخل بياناتك للوصول إلى حسابك.',
      email: 'البريد الإلكتروني',
      password: 'كلمة المرور',
      remember: 'تذكرني',
      forgot: 'هل نسيت كلمة المرور؟',
      loginBtn: 'تسجيل الدخول',
      guestBtn: 'المتابعة كزائر',
      social: 'أو سجل الدخول بواسطة',
      noAccount: 'ليس لديك حساب؟',
      registerLink: 'أنشئ حسابك الآن',
      haveCode: 'لديك رمز تفعيل؟',
      activateLink: 'فعّل حسابك الآن',
      loginLink: 'تسجيل الدخول'
    },
    register: {
      chooseRoleTitle: 'انضم إلى مجتمعنا',
      chooseRoleSubtitle: 'اختر دورك لتبدأ رحلتك التعليمية.',
      studentRole: 'التسجيل كطالب',
      studentRoleDesc: 'للطلاب الصغار وأولياء الأمور.',
      teacherRole: 'التسجيل كمعلم',
      teacherRoleDesc: 'للمعلمين الذين يرغبون في التدريس.',
      
      student: {
        title: 'إنشاء حساب طالب',
        subtitle: 'الرجاء إدخال بيانات ولي الأمر والطالب أدناه.',
        parentName: 'اسم ولي الأمر الكامل',
        parentEmail: 'بريد ولي الأمر الإلكتروني',
        studentName: 'اسم الطالب الكامل',
        parentPhone: 'رقم هاتف ولي الأمر',
        phonePlaceholder: 'رقم الهاتف'
      },
      teacher: {
        title: 'إنشاء حساب معلم',
        subtitle: 'يرجى إدخال تفاصيلك للبدء.',
        fullName: 'الاسم الكامل',
        phone: 'رقم الهاتف'
      },
      
      password: 'كلمة المرور',
      confirmPassword: 'تأكيد كلمة المرور',
      iAccept: 'أوافق على',
      terms: 'الشروط والأحكام',
      searchCountry: 'ابحث عن دولة...',
      creatingAccount: 'جاري إنشاء الحساب',
      acceptTermsError: 'يجب عليك قبول الشروط والأحكام.',
      genericError: 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.',
      networkError: 'حدث خطأ في الشبكة. يرجى التحقق من اتصالك والمحاولة مرة أخرى.',
      validation: {
        required: 'هذا الحقل مطلوب.',
        invalidEmail: 'الرجاء إدخال عنوان بريد إلكتروني صالح.',
        phoneInvalid: 'الرجاء إدخال رقم هاتف صالح.',
        passwordMismatch: 'كلمتا المرور غير متطابقتين.',
        passwordWeak: 'يجب أن تتكون كلمة المرور من 8 أحرف على الأقل وتتضمن حرفًا كبيرًا وحرفًا صغيرًا ورقمًا.',
      },
      createAccountBtn: 'إنشاء حساب',
      email: 'البريد الإلكتروني',
    },
    forgotPassword: {
      title: 'هل نسيت كلمة المرور؟',
      subtitle: 'أدخل بريدك الإلكتروني وسنرسل لك رمز إعادة التعيين.',
      email: 'البريد الإلكتروني',
      verifyBtn: 'أرسل الرمز',
      backToLogin: 'العودة لتسجيل الدخول',

      verifyTitle: 'أدخل رمز التحقق',
      verifySubtitle: 'تم إرسال رمز مكون من 6 أرقام إلى بريدك الإلكتروني.',
      code: 'رمز التحقق',
      verifyCodeBtn: 'تحقق من الرمز',
      resend: 'لم تستلم الرمز؟',
      resendBtn: 'إعادة الإرسال',

      resetTitle: 'إعادة تعيين كلمة المرور',
      resetSubtitle: 'أنشئ كلمة مرور جديدة وقوية.',
      newPassword: 'كلمة المرور الجديدة',
      confirmPassword: 'تأكيد كلمة المرور الجديدة',
      resetBtn: 'تأكيد كلمة المرور',
      haveCode: 'لديك رمز تفعيل؟',
      activateLink: 'فعّل حسابك الآن',
      loginLink: 'تسجيل الدخول'
    },
    logoutModal: {
      title: 'هل أنت متأكد من أنك تريد تسجيل الخروج؟',
      cancel: 'إلغاء',
      logout: 'تسجيل الخروج'
    },
    activateAccount: {
      title: 'تفعيل حسابك',
      subtitle: 'أدخل بريدك الإلكتروني ورمز التفعيل الذي استلمته من المدرسة.',
      email: 'البريد الإلكتروني',
      code: 'رمز التفعيل',
      activateBtn: 'تفعيل الحساب',
      loadingBtn: 'جاري التفعيل...'
    },
    brand: {
      main: 'سوروبان',
      secondary: 'البرق'
    },
    hero: {
      title: 'طور مهاراتك خطوة بخطوة مع منصتنا التعليمية!',
      subtitle: 'ابدأ رحلة التعلم الآن بمستويات مصممة خصيصاً لك',
      cta: 'ابدأ الآن'
    },
    video: {
      alt: 'خلفية الفيديو',
      playButton: 'تشغيل الفيديو'
    },
    mission: {
      title: 'مهمتنا',
      subtitle: 'تمكين متعلمي الغد',
      description: 'نتصور بيئة تعليمية حيث يزدهر الفضول، وتُقبل التحديات، ويشعر كل طالب بالإلهام للنمو. تم بناء منصتنا لدعم المتعلمين المعاصرين بمحتوى تفاعلي، وتجارب مخصصة، ومجتمع يحفز التقدم - في كل خطوة على الطريق.',
      cta: 'اعرف المزيد',
      showLess: 'إخفاء',
      additionalContent: {
        paragraph1: 'طريقتنا التكنولوجية المبتكرة هي توحيد تكنولوجيات القطاع التعليمي المعترف بها مع الطرق التعليمية المثبتة. نحن نؤمن بأن التعلم يجب أن يكون متاحًا ومثيرًا ومخصصًا لحاجات الفرد الفردية. من خلال المنهج الموجز الذي نقدمه، نوفر للطلاب أدواتهم اللازمة للنجاح في عالم التكنولوجيا الرقمية المتطور.',
        paragraph2: 'نحن نحفز بيئة تعلم مشترك حيث يمكن للطلاب التواصل مع زملائهم، ومشاركة المعرفة، وبناء علاقات طويلة الأجل. طريقتنا المجتمعية المشاركة هي تضمن أن كل متعلم يشعر بدعمه وتحفيزه طوال رحلته التعليمية.',
        paragraph3: 'مع تحديثات مستمرة وتحسينات، نحن نبقى على رأس تكنولوجيا التعليم. تؤمن بنا التزامنا بالتميز لنحنا أن ننشأ تجربة تعليمية أفضل ممكنة لطلاب جميع الأعمار والخلفاء.'
      },
      stats: {
        teachers: 'معلم مؤهل',
        students: 'طالب نشط'
      }
    },
    howItWorks: {
      title: 'كيف يعمل',
      steps: {
        signup: {
          title: 'سجل أو استكشف كضيف',
          description: 'أنشئ حساباً كطالب — أو انضم بسرعة كضيف لتجربة الخدمة.'
        },
        quiz: {
          title: 'أجب على اختبار التقييم',
          description: 'أجب على بعض الأسئلة القصيرة لمساعدتنا في التوصية بمستوى التعلم المناسب لك'
        },
        learn: {
          title: 'ابدأ التعلم',
          description: 'احصل على مستواك المخصص، شاهد الدروس، وأكمل المهام بوتيرتك الخاصة.'
        },
        challenges: {
          title: 'انضم للتحديات وتتبع التقدم',
          description: 'شارك في التحديات الشهرية والموسمية، اربح الشارات، وتابع نموك في الوقت الفعلي.'
        }
      }
    },
    startLearningYoung: {
      title: 'ابدأ التعلم منذ الصغر',
      subtitle: 'ابدأ رحلة التعلم من الصفر. هذا المستوى مثالي للمبتدئين المطلقين الذين يريدون بناء أسس قوية خطوة بخطوة.',
      cta: 'انضم الآن',
      levels: {
        level1: 'المستوى 1',
        level2: 'المستوى 2',
        level3: 'المستوى 3',
        basicsExplorer: {
          title: 'مستكشف الأساسيات',
          description: 'ابدأ رحلة التعلم من الصفر. هذا المستوى مثالي للمبتدئين المطلقين الذين يريدون بناء أسس قوية خطوة بخطوة.'
        },
        skillBuilder: {
          title: 'باني المهارات',
          description: 'ابدأ رحلة التعلم من الصفر. هذا المستوى مثالي للمبتدئين المطلقين الذين يريدون بناء أسس قوية خطوة بخطوة.'
        },
        futureReady: {
          title: 'جاهز للمستقبل',
          description: 'ابدأ رحلة التعلم من الصفر. هذا المستوى مثالي للمبتدئين المطلقين الذين يريدون بناء أسس قوية خطوة بخطوة.'
        }
      }
    },
    startLearningTeacher: {
      title: 'ابدأ التعلم لتصبح معلم',
      subtitle: 'تم تنظيم دورتنا في ثلاثة مستويات تدريجية – من الأساسيات الأساسية إلى التحديات المتقدمة. اختر المستوى الذي يناسب خبرتك وابدأ رحلتك بثقة.',
      cta: 'انضم الآن',
      levels: {
        level1: 'المستوى 1',
        level2: 'المستوى 2',
        level3: 'المستوى 3',
        basicsExplorer: {
          title: 'مستكشف الأساسيات',
          description: 'ابدأ رحلة التعلم من الصفر. هذا المستوى مثالي للمبتدئين المطلقين الذين يريدون بناء أسس قوية خطوة بخطوة.'
        },
        skillBuilder: {
          title: 'باني المهارات',
          description: 'ابدأ رحلة التعلم من الصفر. هذا المستوى مثالي للمبتدئين المطلقين الذين يريدون بناء أسس قوية خطوة بخطوة.'
        },
        futureReady: {
          title: 'جاهز للمستقبل',
          description: 'ابدأ رحلة التعلم من الصفر. هذا المستوى مثالي للمبتدئين المطلقين الذين يريدون بناء أسس قوية خطوة بخطوة.'
        }
      }
    },
    bestTeachers: {
      title: 'معلمونا',
      subtitle: 'تعرف على معلمينا المتفانين وذوي الخبرة الذين شغوفون بتوجيهك.',
      titles: {
        leadInstructor: 'المدرب الرئيسي',
        mathSpecialist: 'متخصص الرياضيات',
        scienceTeacher: 'معلم العلوم',
        artCreativity: 'الفن والإبداع'
      }
    },
    bestStudents: {
      title: 'أفضل الطلاب هذا الشهر',
      subtitle: 'تعرف على متعلمينا المتفوقين الذين تميزوا هذا الشهر بتفانيهم وتقدمهم وإنجازاتهم.',
      level: 'المستوى 1',
      points: 'نقطة'
    },
    joinChallenge: {
      title: 'انضم للتحدي - مسابقات شهرية وموسمية',
      subtitle: 'اختبر مهاراتك! شارك في تحديات ممتعة ومؤقتة، اربح الشارات، وشاهد ترتيبك بين المتعلمين الآخرين.',
      tabs: {
        all: 'الكل',
        monthly: 'شهري',
        seasonal: 'موسمي'
      },
      cta: 'انضم الآن',
      challenges: {
        focusBoost: {
          title: 'تعزيز التركيز',
          description: 'أكمل 5 دروس دون تخطي.',
          reward: 'نجمة الاتساق'
        },
        marchSkillRush: {
          title: 'سباق المهارات في مارس',
          description: 'اربح 100 نقطة في أسبوع.',
          reward: 'اربح 100 نقطة في أسبوع'
        },
        springCodeQuest: {
          title: 'مهمة البرمجة الربيعية',
          description: 'حل 10 ألغاز منطقية في 5 أيام.',
          reward: 'شارة: عداء المنطق'
        },
        weeklyAchiever: {
          title: 'المحقق الأسبوعي',
          description: 'اربح 100 نقطة في أسبوع.',
          reward: 'اربح 100 نقطة في أسبوع'
        }
      }
    },
    testimonials: {
      title: 'ماذا يقول طلابنا',
      subtitle: 'استمع مباشرة من متعلمينا الراضين وشاهد كيف أحدثنا فرقاً.',
      title1: 'طالب ثانوي',
      title2: 'طالب ثانوي',
      title3: 'طالب ثانوي',
      text: 'لم أعتقد أبداً أن التعلم يمكن أن يكون ممتعاً إلى هذا الحد! التحديات تحفزني حقاً، وأحب كيف تتكيف المنصة مع مستواي.'
    },
    partners: {
      title: 'شركاؤنا الموثوقون',
      companyName: 'اسم الشركة'
    },
    footer: {
      tagline: 'سوروبان',
      copyright: 'جميع الحقوق محفوظة',
      sections: {
        explore: {
          title: 'استكشف',
          links: {
            courseOverview: 'نظرة عامة على الدورات',
            levels: 'المستويات',
            challenges: 'التحديات',
            signIn: 'تسجيل الدخول'
          }
        },
        aboutUs: {
          title: 'من نحن',
          links: {
            ourVision: 'رؤيتنا',
            team: 'الفريق',
            contact: 'اتصل بنا',
            careers: 'الوظائف'
          }
        },
        resources: {
          title: 'استكشف الموارد',
          links: {
            helpCenter: 'مركز المساعدة',
            blog: 'المدونة',
            placementTest: 'اختبار التقييم',
            faqs: 'الأسئلة الشائعة'
          }
        }
      }
    }
  }
}

export const currentLocale = ref<Locale>('en')

export function t(key: string): string {
  const keys = key.split('.')
  let value: any = translations[currentLocale.value]
  
  for (const k of keys) {
    value = value?.[k]
  }
  
  return value || key
}

export function setLocale(locale: Locale) {
  currentLocale.value = locale
  document.documentElement.dir = locale === 'ar' ? 'rtl' : 'ltr'
  document.documentElement.lang = locale
} 