import { createRouter, createWebHistory } from 'vue-router';
import Home from '../screens/Home/Home.vue';
import Login from '../screens/Auth/Login/Login.vue';
import ForgotPassword from '../screens/Auth/ForgotPassword/ForgotPassword.vue';
import VerifyCode from '../screens/Auth/VerifyCode/VerifyCode.vue';
import ResetPassword from '../screens/Auth/ResetPassword/ResetPassword.vue';
import ActivateAccount from '../screens/Auth/ActivateAccount/ActivateAccount.vue';
import ChooseRole from '../screens/Auth/Register/ChooseRole.vue';
import RegisterStudent from '../screens/Auth/Register/RegisterStudent.vue';
import RegisterTeacher from '../screens/Auth/Register/RegisterTeacher.vue';
import VideoCoursePage from '../screens/VideoCoursePage/VideoCoursePage.vue';
import { apiService } from '../services/api';

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
  },
  {
    path: '/courses/:courseId/videos',
    name: 'video-course',
    component: VideoCoursePage,
    meta: { requiresAuth: true }
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
  },
  {
    path: '/register',
    name: 'ChooseRole',
    component: ChooseRole,
  },
  {
    path: '/register/student',
    name: 'RegisterStudent',
    component: RegisterStudent,
  },
  {
    path: '/register/teacher',
    name: 'RegisterTeacher',
    component: RegisterTeacher,
  },
  {
    path: '/forgot-password',
    name: 'ForgotPassword',
    component: ForgotPassword,
  },
  {
    path: '/verify-code',
    name: 'VerifyCode',
    component: VerifyCode,
  },
  {
    path: '/reset-password',
    name: 'ResetPassword',
    component: ResetPassword,
  },
  {
    path: '/activate',
    name: 'ActivateAccount',
    component: ActivateAccount,
  },
  {
    path: '/courses',
    name: 'Courses',
    component: () => import('../screens/Courses/Courses.vue'),
  },
  {
    path: '/courses/:id',
    name: 'CourseDetails',
    component: () => import('../screens/Courses/CourseDetails.vue'),
  },
  {
    path: '/courses/:courseId/lesson/:lessonId',
    name: 'LessonView',
    component: () => import('../screens/Courses/LessonView.vue'),
  },
  {
    path: '/competition',
    name: 'Competition',
    component: () => import('../screens/Competition/Competition.vue'),
  },
  {
    path: '/competition/:id',
    name: 'CompetitionDetails',
    component: () => import('../screens/Competition/CompetitionDetails.vue'),
  },
  {
    path: '/student-dashboard',
    name: 'StudentDashboard',
    component: () => import('../screens/StudentDashboard/StudentDashboard.vue'),
    meta: { requiresAuth: true, roles: ['student'] }
  },
  {
    path: '/teacher-dashboard',
    name: 'TeacherDashboard',
    component: () => import('../screens/TeacherDashboard/TeacherDashboard.vue'),
    meta: { requiresAuth: true, roles: ['teacher'] }
  },
  {
    path: '/admin-dashboard',
    name: 'AdminDashboard',
    component: () => import('../screens/AdminDashboard/AdminDashboard.vue'),
    meta: { requiresAuth: true, roles: ['admin'] }
  },
  {
    path: '/super-admin-dashboard',
    name: 'SuperAdminDashboard',
    component: () => import('../screens/SuperAdminDashboard/SuperAdminDashboard.vue'),
    meta: { requiresAuth: true, roles: ['superAdmin'] }
  },
  // Course Management Routes
  {
    path: '/admin/courses',
    name: 'admin-courses',
    component: () => import('../screens/CourseManagement/CourseManagement.vue'),
    meta: { requiresAuth: true, roles: ['admin', 'superAdmin'] }
  },
  {
    path: '/teacher/courses',
    name: 'teacher-courses',
    component: () => import('../screens/CourseManagement/CourseManagement.vue'),
    meta: { requiresAuth: true, roles: ['teacher'] }
  },
  // Video Management Routes
  {
    path: '/admin/videos',
    name: 'admin-videos',
    component: () => import('../components/CourseVideoManager.vue'),
    meta: { requiresAuth: true, roles: ['admin', 'superAdmin'] }
  },
  {
    path: '/teacher/videos',
    name: 'teacher-videos',
    component: () => import('../components/CourseVideoManager.vue'),
    meta: { requiresAuth: true, roles: ['teacher'] }
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

// Navigation guards
router.beforeEach((to, from, next) => {
  const isAuthenticated = apiService.isAuthenticated();
  const currentUser = apiService.getCurrentUser();

  // Check if route requires authentication
  if (to.meta.requiresAuth && !isAuthenticated) {
    next('/login');
    return;
  }

  // Check role-based access
  if (to.meta.roles && currentUser) {
    const userRole = currentUser.role;
    const allowedRoles = to.meta.roles as string[];

    if (!allowedRoles.includes(userRole)) {
      // Redirect to appropriate dashboard based on user role
      switch (userRole) {
        case 'superAdmin':
          next('/super-admin-dashboard');
          break;
        case 'admin':
          next('/admin-dashboard');
          break;
        case 'teacher':
          next('/teacher-dashboard');
          break;
        case 'student':
          next('/student-dashboard');
          break;
        default:
          next('/');
      }
      return;
    }
  }

  // Redirect authenticated users away from auth pages
  if (isAuthenticated && ['Login', 'ChooseRole', 'RegisterStudent', 'RegisterTeacher'].includes(to.name as string)) {
    if (currentUser) {
      switch (currentUser.role) {
        case 'superAdmin':
          next('/super-admin-dashboard');
          break;
        case 'admin':
          next('/admin-dashboard');
          break;
        case 'teacher':
          next('/teacher-dashboard');
          break;
        case 'student':
          next('/student-dashboard');
          break;
        default:
          next('/');
      }
      return;
    }
  }

  next();
});

export default router;