<template>
  <div class="flex min-h-screen bg-[#E6EFFF]">
    <!-- Sidebar -->
    <aside class="w-64 bg-[#F6F9FF] flex flex-col justify-between border-r border-[#D1D5DC]">
      <div>
        <!-- Logo -->
        <div class="flex items-center gap-2 px-6 py-8">
          <img src="https://c.animaapp.com/mc5ppr8hKB91iA/img/logo-lerning-removebg-preview-1-1.png" alt="ELBARQ Logo" class="h-10 w-10 rounded" />
          <div>
            <div class="font-bold text-[#162456] text-lg leading-4">ELBARQ</div>
            <div class="text-xs text-[#d08700] font-semibold -mt-1">Soroban</div>
          </div>
        </div>
        <nav class="mt-2">
          <ul class="space-y-2 px-2">
            <li><a href="#" @click="activeSection = 'dashboard'" :class="activeSection === 'dashboard' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all"> 
              <span>
                <svg width="18" height="18" fill="#043355" viewBox="0 0 24 24">
                  <path d="M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" stroke="#043355" stroke-width="2" fill="none"/>
                  <path d="M9 22V12h6v10" stroke="#043355" stroke-width="2" fill="none"/>
                </svg>
              </span> 
              Dashboard
            </a></li>
            
            <li><a href="#" @click="activeSection = 'groups'" :class="activeSection === 'groups' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all"> 
              <span>
                <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
                  <path d="M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2" stroke="#043355" stroke-width="2"/>
                  <circle cx="9" cy="7" r="4" stroke="#043355" stroke-width="2"/>
                  <path d="M23 21v-2a4 4 0 00-3-3.87" stroke="#043355" stroke-width="2"/>
                  <path d="M16 3.13a4 4 0 010 7.75" stroke="#043355" stroke-width="2"/>
                </svg>
              </span> 
              Groups
            </a></li>
            
            <li><a href="#" @click="activeSection = 'teams'" :class="activeSection === 'teams' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all"> 
              <span>
                <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
                  <path d="M16 21v-2a4 4 0 00-4-4H6a4 4 0 00-4 4v2" stroke="#043355" stroke-width="2"/>
                  <circle cx="9" cy="7" r="4" stroke="#043355" stroke-width="2"/>
                  <path d="M22 21v-2a4 4 0 00-3-3.87" stroke="#043355" stroke-width="2"/>
                  <path d="M16 3.13a4 4 0 010 7.75" stroke="#043355" stroke-width="2"/>
                </svg>
              </span> 
              Teams
            </a></li>
            
            <li><a href="#" @click="activeSection = 'payment'" :class="activeSection === 'payment' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all"> 
              <span>
                <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
                  <path d="M21 12V7H5a2 2 0 00-2 2v6a2 2 0 002 2h14.01" stroke="#043355" stroke-width="2"/>
                  <path d="M3 10h18" stroke="#043355" stroke-width="2"/>
                </svg>
              </span> 
              Payment
            </a></li>
            
            <li><a href="#" @click="activeSection = 'competitions'" :class="activeSection === 'competitions' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all"> 
              <span>
                <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
                  <path d="M6 9l6 6 6-6" stroke="#043355" stroke-width="2"/>
                </svg>
              </span> 
              Competitions
            </a></li>
            
            <li><a href="#" @click="activeSection = 'partners'" :class="activeSection === 'partners' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all"> 
              <span>
                <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
                  <path d="M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2" stroke="#043355" stroke-width="2"/>
                  <circle cx="9" cy="7" r="4" stroke="#043355" stroke-width="2"/>
                  <path d="M23 21v-2a4 4 0 00-3-3.87" stroke="#043355" stroke-width="2"/>
                  <path d="M16 3.13a4 4 0 010 7.75" stroke="#043355" stroke-width="2"/>
                </svg>
              </span> 
              Partners
            </a></li>
            
            <li><a href="#" @click="activeSection = 'contact'" :class="activeSection === 'contact' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all">
              <span>
                <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
                  <path d="M21 15a2 2 0 01-2 2H7l-4 4V5a2 2 0 012-2h14a2 2 0 012 2v10z" stroke="#043355" stroke-width="2"/>
                  <path d="M13 11h0" stroke="#043355" stroke-width="2"/>
                  <path d="M9 11h0" stroke="#043355" stroke-width="2"/>
                  <path d="M17 11h0" stroke="#043355" stroke-width="2"/>
                </svg>
              </span>
              Contact & Help
            </a></li>

            <!-- Course & Video Management -->
            <li><router-link :to="{ name: 'admin-courses' }" :class="$route.name === 'admin-courses' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all">
              <span>
                <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
                  <path d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" stroke="#043355" stroke-width="2"/>
                </svg>
              </span>
              Course Management
            </router-link></li>

            <li><router-link :to="{ name: 'admin-videos' }" :class="$route.name === 'admin-videos' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all">
              <span>
                <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
                  <path d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" stroke="#043355" stroke-width="2"/>
                </svg>
              </span>
              Video Management
            </router-link></li>
          </ul>
        </nav>
      </div>
      <div class="mb-6 px-2 space-y-2">
        <a href="#" @click="activeSection = 'settings'" :class="activeSection === 'settings' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all">
          <span>
            <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
              <circle cx="12" cy="12" r="3" stroke="#043355" stroke-width="2"/>
              <path d="M19.4 15a1.65 1.65 0 00.33 1.82l.06.06a2 2 0 010 2.83 2 2 0 01-2.83 0l-.06-.06a1.65 1.65 0 00-1.82-.33 1.65 1.65 0 00-1 1.51V21a2 2 0 01-2 2 2 2 0 01-2-2v-.09A1.65 1.65 0 009 19.4a1.65 1.65 0 00-1.82.33l-.06.06a2 2 0 01-2.83 0 2 2 0 010-2.83l.06-.06a1.65 1.65 0 00.33-1.82 1.65 1.65 0 00-1.51-1H3a2 2 0 01-2-2 2 2 0 012-2h.09A1.65 1.65 0 004.6 9a1.65 1.65 0 00-.33-1.82l-.06-.06a2 2 0 010-2.83 2 2 0 012.83 0l.06.06a1.65 1.65 0 001.82.33H9a1.65 1.65 0 001-1.51V3a2 2 0 012-2 2 2 0 012 2v.09a1.65 1.65 0 001 1.51 1.65 1.65 0 001.82-.33l.06-.06a2 2 0 012.83 0 2 2 0 010 2.83l-.06.06a1.65 1.65 0 00-.33 1.82V9a1.65 1.65 0 001.51 1H21a2 2 0 012 2 2 2 0 01-2 2h-.09a1.65 1.65 0 00-1.51 1z" stroke="#043355" stroke-width="2"/>
            </svg>
          </span>
          Setting
        </a>
        <a href="/" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#e7000b] transition-all hover:bg-red-50">
          <span>
            <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
              <path d="M9 21H5a2 2 0 01-2-2V5a2 2 0 012-2h4" stroke="#e7000b" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M16 17l5-5-5-5" stroke="#e7000b" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M21 12H9" stroke="#e7000b" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </span>
          Log Out
        </a>
      </div>
    </aside>

    <!-- Main Content -->
    <main class="flex-1 min-h-screen">
      <!-- Header -->
      <div class="flex justify-end items-center px-10 py-4 bg-white shadow-sm border-b border-[#F3F3F3]" style="min-height:107px;">
        <div class="flex items-center gap-6">
          <span class="text-xl text-black"><svg width="20" height="20" fill="none" viewBox="0 0 24 24"><path d="M21 12.79A9 9 0 1 1 11.21 3a7 7 0 0 0 9.79 9.79Z" stroke="#000" stroke-width="2"/></svg></span>
          <span class="relative">
            <svg class="w-7 h-7 text-[#043355]" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V4a2 2 0 10-4 0v1.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/></svg>
            <span class="absolute -top-2 -right-2 bg-[#BC0000] text-white rounded-full w-5 h-5 flex items-center justify-center text-xs border-2 border-white">1</span>
          </span>
          <div class="flex items-center gap-2 bg-white border border-[#D1D5DC] rounded-full px-4 py-2">
            <img src="https://randomuser.me/api/portraits/men/32.jpg" class="w-8 h-8 rounded-full object-cover border border-[#D1D5DC]" />
            <span class="font-bold text-[#043355]">Gamal Yousef</span>
          </div>
        </div>
      </div>

      <!-- Dashboard Content -->
      <div v-if="activeSection === 'dashboard'" class="px-10 py-8">
        <!-- Analysis Section -->
        <div class="mb-8">
          <div class="flex items-center gap-2 mb-6">
            <svg width="20" height="20" fill="#043355" viewBox="0 0 24 24">
              <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22" stroke="#043355" stroke-width="2" fill="none"/>
            </svg>
            <h2 class="text-2xl font-bold text-[#043355]">Analysis</h2>
          </div>

          <!-- Stats Cards -->
          <div class="grid grid-cols-3 gap-6 mb-8">
            <!-- Count Teachers -->
            <div class="bg-[#2C3E50] text-white rounded-2xl p-6">
              <h3 class="text-lg font-semibold mb-2">Count Teachers</h3>
              <div class="text-4xl font-bold">3</div>
              <div class="text-sm opacity-75">Teachers</div>
            </div>

            <!-- Count Teams -->
            <div class="bg-white rounded-2xl p-6 border border-[#E5E7EB]">
              <h3 class="text-lg font-semibold mb-2 text-[#043355]">Count Teams</h3>
              <div class="text-4xl font-bold text-[#043355]">10</div>
              <div class="text-sm text-gray-600">Teams</div>
            </div>

            <!-- Count Groups -->
            <div class="bg-white rounded-2xl p-6 border border-[#E5E7EB]">
              <h3 class="text-lg font-semibold mb-2 text-[#043355]">Count Groups</h3>
              <div class="text-4xl font-bold text-[#3B82F6]">20</div>
              <div class="text-sm text-gray-600">Groups</div>
            </div>
          </div>

          <!-- Quick Access Cards -->
          <div class="grid grid-cols-2 gap-6 mb-8">
            <!-- Course Management -->
            <router-link :to="{ name: 'admin-courses' }" class="bg-gradient-to-br from-blue-500 to-blue-600 text-white rounded-2xl p-6 hover:from-blue-600 hover:to-blue-700 transition-all transform hover:scale-105 shadow-lg">
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-lg font-semibold mb-2">Course Management</h3>
                  <p class="text-blue-100 text-sm">Create and manage courses, levels, and content</p>
                </div>
                <div class="bg-white bg-opacity-20 rounded-full p-3">
                  <svg width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                  </svg>
                </div>
              </div>
            </router-link>

            <!-- Video Management -->
            <router-link :to="{ name: 'admin-videos' }" class="bg-gradient-to-br from-purple-500 to-purple-600 text-white rounded-2xl p-6 hover:from-purple-600 hover:to-purple-700 transition-all transform hover:scale-105 shadow-lg">
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-lg font-semibold mb-2">Video Management</h3>
                  <p class="text-purple-100 text-sm">Manage YouTube playlists and video lessons</p>
                </div>
                <div class="bg-white bg-opacity-20 rounded-full p-3">
                  <svg width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                  </svg>
                </div>
              </div>
            </router-link>
          </div>

          <!-- Charts Row -->
          <div class="grid grid-cols-3 gap-6 mb-8">
            <!-- Payment Chart -->
            <div class="bg-[#2C3E50] text-white rounded-2xl p-6">
              <h3 class="text-lg font-semibold mb-4">Payment</h3>
              <div class="relative">
                <div class="w-32 h-32 mx-auto relative">
                  <svg class="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
                    <!-- Background circle -->
                    <circle cx="50" cy="50" r="45" fill="none" stroke="#34495e" stroke-width="8"/>
                    <!-- Progress circle for Pending (Red) -->
                    <circle cx="50" cy="50" r="45" fill="none" stroke="#e74c3c" stroke-width="8" 
                            stroke-dasharray="100 283" stroke-dashoffset="0"/>
                    <!-- Progress circle for Accepted (Green) -->
                    <circle cx="50" cy="50" r="45" fill="none" stroke="#27ae60" stroke-width="8" 
                            stroke-dasharray="120 283" stroke-dashoffset="-100"/>
                    <!-- Progress circle for Rejected (Yellow) -->
                    <circle cx="50" cy="50" r="45" fill="none" stroke="#f39c12" stroke-width="8" 
                            stroke-dasharray="63 283" stroke-dashoffset="-220"/>
                  </svg>
                  <div class="absolute inset-0 flex flex-col items-center justify-center">
                    <div class="text-2xl font-bold">700</div>
                    <div class="text-sm opacity-75">Reports</div>
                  </div>
                </div>
                <div class="mt-4 space-y-2">
                  <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                    <span class="text-sm">pending</span>
                  </div>
                  <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span class="text-sm">Accepted</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Monthly Competitions -->
            <div class="bg-white rounded-2xl p-6 border border-[#E5E7EB]">
              <h3 class="text-lg font-semibold mb-4 text-[#043355]">Monthly Competitions</h3>
              <div class="relative">
                <div class="w-32 h-32 mx-auto relative">
                  <svg class="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
                    <!-- Background circle -->
                    <circle cx="50" cy="50" r="45" fill="none" stroke="#e5e7eb" stroke-width="8"/>
                    <!-- Progress circle for Finished (Green) -->
                    <circle cx="50" cy="50" r="45" fill="none" stroke="#10b981" stroke-width="8" 
                            stroke-dasharray="141 283" stroke-dashoffset="0"/>
                    <!-- Progress circle for In Progress (Blue) -->
                    <circle cx="50" cy="50" r="45" fill="none" stroke="#3b82f6" stroke-width="8" 
                            stroke-dasharray="84 283" stroke-dashoffset="-141"/>
                    <!-- Progress circle for Pending (Yellow) -->
                    <circle cx="50" cy="50" r="45" fill="none" stroke="#f59e0b" stroke-width="8" 
                            stroke-dasharray="58 283" stroke-dashoffset="-225"/>
                  </svg>
                  <div class="absolute inset-0 flex flex-col items-center justify-center">
                    <div class="text-2xl font-bold text-[#043355]">20</div>
                    <div class="text-sm text-gray-600">Competitions</div>
                  </div>
                </div>
                <div class="mt-4 space-y-2">
                  <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span class="text-sm text-[#043355]">Finished</span>
                  </div>
                  <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span class="text-sm text-[#043355]">In Progress</span>
                  </div>
                  <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <span class="text-sm text-[#043355]">Pending</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Seasonal Competitions -->
            <div class="bg-white rounded-2xl p-6 border border-[#E5E7EB]">
              <h3 class="text-lg font-semibold mb-4 text-[#043355]">Seasonal Competitions</h3>
              <div class="relative">
                <div class="w-32 h-32 mx-auto relative">
                  <svg class="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
                    <!-- Background circle -->
                    <circle cx="50" cy="50" r="45" fill="none" stroke="#e5e7eb" stroke-width="8"/>
                    <!-- Progress circle for Finished (Green) -->
                    <circle cx="50" cy="50" r="45" fill="none" stroke="#10b981" stroke-width="8" 
                            stroke-dasharray="113 283" stroke-dashoffset="0"/>
                    <!-- Progress circle for In Progress (Blue) -->
                    <circle cx="50" cy="50" r="45" fill="none" stroke="#3b82f6" stroke-width="8" 
                            stroke-dasharray="84 283" stroke-dashoffset="-113"/>
                    <!-- Progress circle for Pending (Yellow) -->
                    <circle cx="50" cy="50" r="45" fill="none" stroke="#f59e0b" stroke-width="8" 
                            stroke-dasharray="86 283" stroke-dashoffset="-197"/>
                  </svg>
                  <div class="absolute inset-0 flex flex-col items-center justify-center">
                    <div class="text-2xl font-bold text-[#043355]">15</div>
                    <div class="text-sm text-gray-600">Competitions</div>
                  </div>
                </div>
                <div class="mt-4 space-y-2">
                  <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span class="text-sm text-[#043355]">Finished</span>
                  </div>
                  <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span class="text-sm text-[#043355]">In Progress</span>
                  </div>
                  <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <span class="text-sm text-[#043355]">Pending</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Soroban Progress Chart -->
          <div class="bg-white rounded-2xl p-8 border border-[#E5E7EB]">
            <div class="flex items-center gap-2 mb-6">
              <svg width="20" height="20" fill="#043355" viewBox="0 0 24 24">
                <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22" stroke="#043355" stroke-width="2" fill="none"/>
              </svg>
              <h3 class="text-xl font-bold text-[#043355]">Soroban Progress</h3>
            </div>
            
            <!-- Chart Area -->
            <div class="h-80 relative">
              <svg class="w-full h-full" viewBox="0 0 800 300">
                <!-- Grid Lines -->
                <defs>
                  <pattern id="grid" width="100" height="50" patternUnits="userSpaceOnUse">
                    <path d="M 100 0 L 0 0 0 50" fill="none" stroke="#f3f4f6" stroke-width="1"/>
                  </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#grid)" />
                
                <!-- Y-axis labels -->
                <text x="30" y="50" font-size="12" fill="#6b7280">100</text>
                <text x="30" y="100" font-size="12" fill="#6b7280">80</text>
                <text x="30" y="150" font-size="12" fill="#6b7280">60</text>
                <text x="30" y="200" font-size="12" fill="#6b7280">40</text>
                <text x="30" y="250" font-size="12" fill="#6b7280">20</text>
                <text x="30" y="290" font-size="12" fill="#6b7280">0</text>
                
                <!-- X-axis labels -->
                <text x="100" y="320" font-size="12" fill="#6b7280">2024</text>
                <text x="250" y="320" font-size="12" fill="#6b7280">2025</text>
                <text x="400" y="320" font-size="12" fill="#6b7280">2026</text>
                <text x="550" y="320" font-size="12" fill="#6b7280">2027</text>
                <text x="700" y="320" font-size="12" fill="#6b7280">2028</text>
                <text x="750" y="320" font-size="12" fill="#6b7280">2029</text>
                
                <!-- Progress Line -->
                <polyline 
                  fill="none" 
                  stroke="#8b5cf6" 
                  stroke-width="3"
                  points="80,120 150,140 220,130 280,110 340,125 400,185 460,120 520,140 580,160 640,180 700,145 750,110"
                />
                
                <!-- Data Points -->
                <circle cx="80" cy="120" r="4" fill="#8b5cf6"/>
                <circle cx="150" cy="140" r="4" fill="#8b5cf6"/>
                <circle cx="220" cy="130" r="4" fill="#8b5cf6"/>
                <circle cx="280" cy="110" r="4" fill="#8b5cf6"/>
                <circle cx="340" cy="125" r="4" fill="#8b5cf6"/>
                <circle cx="400" cy="185" r="4" fill="#8b5cf6"/>
                <circle cx="460" cy="120" r="4" fill="#8b5cf6"/>
                <circle cx="520" cy="140" r="4" fill="#8b5cf6"/>
                <circle cx="580" cy="160" r="4" fill="#8b5cf6"/>
                <circle cx="640" cy="180" r="4" fill="#8b5cf6"/>
                <circle cx="700" cy="145" r="4" fill="#8b5cf6"/>
                <circle cx="750" cy="110" r="4" fill="#8b5cf6"/>
              </svg>
            </div>
          </div>
        </div>
      </div>

      <!-- Groups Section -->
      <div v-if="activeSection === 'groups'" class="px-10 pt-8 pb-10">
        <!-- Tabs -->
        <div class="flex gap-1 mb-8">
          <button 
            @click="groupsActiveTab = 'waiting'" 
            :class="groupsActiveTab === 'waiting' ? 'bg-white text-[#043355] shadow border border-[#E5E7EB]' : 'bg-[#F5F5F5] text-gray-600'"
            class="px-6 py-3 rounded-t-lg font-medium transition-all"
          >
            Waiting List
          </button>
          <button 
            @click="groupsActiveTab = 'groupA1'" 
            :class="groupsActiveTab === 'groupA1' ? 'bg-white text-[#043355] shadow border border-[#E5E7EB]' : 'bg-[#F5F5F5] text-gray-600'"
            class="px-6 py-3 rounded-t-lg font-medium transition-all"
          >
            Group A1
          </button>
          <button 
            @click="groupsActiveTab = 'groupB2'" 
            :class="groupsActiveTab === 'groupB2' ? 'bg-white text-[#043355] shadow border border-[#E5E7EB]' : 'bg-[#F5F5F5] text-gray-600'"
            class="px-6 py-3 rounded-t-lg font-medium transition-all"
          >
            Group B2
          </button>
          <button 
            @click="groupsActiveTab = 'groupC3'" 
            :class="groupsActiveTab === 'groupC3' ? 'bg-white text-[#043355] shadow border border-[#E5E7EB]' : 'bg-[#F5F5F5] text-gray-600'"
            class="px-6 py-3 rounded-t-lg font-medium transition-all"
          >
            Group C3
          </button>
        </div>

        <!-- Waiting List Tab Content -->
        <div v-if="groupsActiveTab === 'waiting'" class="bg-white rounded-lg border border-[#E5E7EB] overflow-hidden">
          <div class="space-y-0">
            <!-- Student Item -->
            <div v-for="(student, index) in waitingListStudents" :key="index" 
                 class="flex items-center justify-between px-6 py-4 border-b border-[#F0F0F0] hover:bg-[#F9F9F9] transition-colors">
              <div class="flex items-center gap-4">
                <img :src="student.avatar" :alt="student.name" class="w-12 h-12 rounded-full object-cover border border-[#E5E7EB]" />
                <div>
                  <h3 class="font-semibold text-[#043355] text-lg">{{ student.name }}</h3>
                  <p class="text-gray-500 text-sm">{{ student.progress }}</p>
                </div>
              </div>
              <div class="flex gap-3">
                <button @click="acceptStudent(student)" class="bg-blue-50 text-blue-600 border border-blue-200 px-6 py-2 rounded-full font-medium hover:bg-blue-100 transition-all">
                  Accept
                </button>
                <button @click="rejectStudent(student)" class="bg-red-50 text-red-600 border border-red-200 px-6 py-2 rounded-full font-medium hover:bg-red-100 transition-all">
                  reject
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Group A1 Tab Content -->
        <div v-if="groupsActiveTab === 'groupA1'" class="bg-white rounded-lg border border-[#E5E7EB] overflow-hidden">
          <div class="space-y-0">
            <!-- Student Item -->
            <div v-for="(student, index) in groupA1Students" :key="index" 
                 class="flex items-center justify-between px-6 py-4 border-b border-[#F0F0F0] hover:bg-[#F9F9F9] transition-colors">
              <div class="flex items-center gap-4">
                <img :src="student.avatar" :alt="student.name" class="w-12 h-12 rounded-full object-cover border border-[#E5E7EB]" />
                <div>
                  <h3 class="font-semibold text-[#043355] text-lg">{{ student.name }}</h3>
                </div>
              </div>
              <div class="flex gap-3">
                <button @click="editStudent(student)" class="bg-yellow-50 text-yellow-600 border border-yellow-200 px-6 py-2 rounded-full font-medium hover:bg-yellow-100 transition-all">
                  Edit
                </button>
                <button @click="deleteStudent(student, 'groupA1')" class="bg-red-50 text-red-600 border border-red-200 px-6 py-2 rounded-full font-medium hover:bg-red-100 transition-all">
                  Delete
                </button>
              </div>
            </div>
          </div>
          
          <!-- Action Buttons -->
          <div class="p-6 bg-[#F9F9F9] border-t border-[#E5E7EB]">
            <div class="flex gap-4 justify-center">
              <button @click="openWhatsApp()" class="bg-[#25D366] text-white px-6 py-3 rounded-lg font-medium hover:bg-[#20c157] transition-all flex items-center gap-2">
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                </svg>
                What's App
              </button>
              <button @click="addGroup()" class="bg-[#3B82F6] text-white px-6 py-3 rounded-lg font-medium hover:bg-[#2563EB] transition-all flex items-center gap-2">
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"/>
                </svg>
                Add Group
              </button>
              <button @click="addTeacher()" class="bg-[#3B82F6] text-white px-6 py-3 rounded-lg font-medium hover:bg-[#2563EB] transition-all flex items-center gap-2">
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"/>
                </svg>
                Add Teacher
              </button>
              <button @click="addStudent()" class="bg-[#3B82F6] text-white px-6 py-3 rounded-lg font-medium hover:bg-[#2563EB] transition-all flex items-center gap-2">
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"/>
                </svg>
                Add Student
              </button>
              <button @click="deleteGroup()" class="bg-[#DC2626] text-white px-6 py-3 rounded-lg font-medium hover:bg-[#B91C1C] transition-all flex items-center gap-2">
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                </svg>
                Delete Group
              </button>
            </div>
          </div>
        </div>

        <!-- Other Group Tabs Content -->
        <div v-if="groupsActiveTab !== 'waiting' && groupsActiveTab !== 'groupA1'" class="bg-white rounded-lg border border-[#E5E7EB] p-8 text-center">
          <p class="text-gray-500 text-lg">{{ getGroupTabTitle() }} content coming soon...</p>
        </div>
      </div>

      <!-- Teams Section -->
      <div v-if="activeSection === 'teams'" class="px-10 pt-8 pb-10">
        <!-- Tabs -->
        <div class="flex gap-1 mb-8">
          <button 
            @click="teamsActiveTab = 'waiting'" 
            :class="teamsActiveTab === 'waiting' ? 'bg-white text-[#043355] shadow border border-[#E5E7EB]' : 'bg-[#F5F5F5] text-gray-600'"
            class="px-6 py-3 rounded-t-lg font-medium transition-all"
          >
            Waiting List
          </button>
          <button 
            @click="teamsActiveTab = 'team1'" 
            :class="teamsActiveTab === 'team1' ? 'bg-white text-[#043355] shadow border border-[#E5E7EB]' : 'bg-[#F5F5F5] text-gray-600'"
            class="px-6 py-3 rounded-t-lg font-medium transition-all"
          >
            Team 1
          </button>
          <button 
            @click="teamsActiveTab = 'team2'" 
            :class="teamsActiveTab === 'team2' ? 'bg-white text-[#043355] shadow border border-[#E5E7EB]' : 'bg-[#F5F5F5] text-gray-600'"
            class="px-6 py-3 rounded-t-lg font-medium transition-all"
          >
            Team 2
          </button>
          <button 
            @click="teamsActiveTab = 'team3'" 
            :class="teamsActiveTab === 'team3' ? 'bg-white text-[#043355] shadow border border-[#E5E7EB]' : 'bg-[#F5F5F5] text-gray-600'"
            class="px-6 py-3 rounded-t-lg font-medium transition-all"
          >
            Team 3
          </button>
        </div>

        <!-- Teams Waiting List Tab Content -->
        <div v-if="teamsActiveTab === 'waiting'" class="bg-white rounded-lg border border-[#E5E7EB] overflow-hidden">
          <div class="space-y-0">
            <!-- Student Item -->
            <div v-for="(student, index) in teamsWaitingListStudents" :key="index" 
                 class="flex items-center justify-between px-6 py-4 border-b border-[#F0F0F0] hover:bg-[#F9F9F9] transition-colors">
              <div class="flex items-center gap-4">
                <img :src="student.avatar" :alt="student.name" class="w-12 h-12 rounded-full object-cover border border-[#E5E7EB]" />
                <div>
                  <h3 class="font-semibold text-[#043355] text-lg">{{ student.name }}</h3>
                  <p class="text-gray-500 text-sm">{{ student.request }}</p>
                </div>
              </div>
              <div class="flex gap-3">
                <button @click="acceptTeamStudent(student)" class="bg-blue-50 text-blue-600 border border-blue-200 px-6 py-2 rounded-full font-medium hover:bg-blue-100 transition-all">
                  Accept
                </button>
                <button @click="rejectTeamStudent(student)" class="bg-red-50 text-red-600 border border-red-200 px-6 py-2 rounded-full font-medium hover:bg-red-100 transition-all">
                  reject
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Team 1 Tab Content -->
        <div v-if="teamsActiveTab === 'team1'" class="bg-white rounded-lg border border-[#E5E7EB] overflow-hidden">
          <div class="space-y-0">
            <!-- Team Member Item -->
            <div v-for="(member, index) in team1Members" :key="index" 
                 class="flex items-center justify-between px-6 py-4 border-b border-[#F0F0F0] hover:bg-[#F9F9F9] transition-colors">
              <div class="flex items-center gap-4">
                <img :src="member.avatar" :alt="member.name" class="w-12 h-12 rounded-full object-cover border border-[#E5E7EB]" />
                <div>
                  <h3 class="font-semibold text-[#043355] text-lg">{{ member.name }}</h3>
                </div>
              </div>
              <div class="flex gap-3">
                <button @click="editTeamMember(member)" class="bg-yellow-50 text-yellow-600 border border-yellow-200 px-6 py-2 rounded-full font-medium hover:bg-yellow-100 transition-all">
                  Edit
                </button>
                <button @click="deleteTeamMember(member, 'team1')" class="bg-red-50 text-red-600 border border-red-200 px-6 py-2 rounded-full font-medium hover:bg-red-100 transition-all">
                  Delete
                </button>
              </div>
            </div>
          </div>
          
          <!-- Team Action Buttons -->
          <div class="p-6 bg-[#F9F9F9] border-t border-[#E5E7EB]">
            <div class="flex gap-4 justify-center">
              <button @click="addNewTeam()" class="bg-[#3B82F6] text-white px-6 py-3 rounded-lg font-medium hover:bg-[#2563EB] transition-all flex items-center gap-2">
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"/>
                </svg>
                Add New Team
              </button>
              <button @click="deleteTeam()" class="bg-[#DC2626] text-white px-6 py-3 rounded-lg font-medium hover:bg-[#B91C1C] transition-all flex items-center gap-2">
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                </svg>
                Delete Team
              </button>
            </div>
          </div>
        </div>

        <!-- Other Team Tabs Content -->
        <div v-if="teamsActiveTab !== 'waiting' && teamsActiveTab !== 'team1'" class="bg-white rounded-lg border border-[#E5E7EB] p-8 text-center">
          <p class="text-gray-500 text-lg">{{ getTeamTabTitle() }} content coming soon...</p>
        </div>
      </div>

      <!-- Payment Section -->
      <div v-if="activeSection === 'payment'" class="px-10 pt-8 pb-10">
        <!-- Payment Tabs -->
        <div class="flex gap-1 mb-8">
          <button 
            @click="paymentActiveTab = 'invoices'" 
            :class="paymentActiveTab === 'invoices' ? 'bg-white text-[#043355] shadow border border-[#E5E7EB]' : 'bg-[#F5F5F5] text-gray-600'"
            class="px-6 py-3 rounded-t-lg font-medium transition-all"
          >
            Invoices
          </button>
          <button 
            @click="paymentActiveTab = 'groups'" 
            :class="paymentActiveTab === 'groups' ? 'bg-white text-[#043355] shadow border border-[#E5E7EB]' : 'bg-[#F5F5F5] text-gray-600'"
            class="px-6 py-3 rounded-t-lg font-medium transition-all"
          >
            Groups
          </button>
        </div>

        <!-- Invoices Tab Content -->
        <div v-if="paymentActiveTab === 'invoices'">
          <!-- Payment Items -->
          <div class="bg-white rounded-lg border border-[#E5E7EB] overflow-hidden">
            <div class="space-y-0">
              <!-- Payment Item -->
              <div v-for="(payment, index) in filteredPayments" :key="index" 
                   class="flex items-center justify-between px-6 py-4 border-b border-[#F0F0F0] hover:bg-[#F9F9F9] transition-colors">
                <div class="flex items-center gap-4">
                  <!-- Payment Icon -->
                  <div class="w-8 h-8 bg-[#E8F4FD] rounded flex items-center justify-center">
                    <svg width="16" height="16" fill="#3B82F6" viewBox="0 0 24 24">
                      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z"/>
                      <path d="M14 2v6h6"/>
                      <path d="M16 13H8"/>
                      <path d="M16 17H8"/>
                      <path d="M10 9H8"/>
                    </svg>
                  </div>
                  <div>
                    <h3 class="font-semibold text-[#043355] text-base">{{ payment.title }}</h3>
                    <p class="text-gray-500 text-sm">{{ payment.submittedBy }}</p>
                  </div>
                </div>
                <div class="flex gap-2">
                  <button @click="acceptPayment(payment)" class="bg-[#10B981] text-white px-4 py-1.5 rounded text-sm font-medium hover:bg-[#059669] transition-all">
                    Accept
                  </button>
                  <button @click="rejectPayment(payment)" class="bg-[#EF4444] text-white px-4 py-1.5 rounded text-sm font-medium hover:bg-[#DC2626] transition-all">
                    Reject
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Groups Tab Content -->
        <div v-if="paymentActiveTab === 'groups'">
          <!-- Payment Status Tabs -->
          <div class="flex gap-2 mb-6">
            <button 
              @click="paymentStatusTab = 'paid'" 
              :class="paymentStatusTab === 'paid' ? 'bg-[#10B981] text-white' : 'bg-gray-200 text-gray-600'"
              class="px-4 py-2 rounded-lg font-medium transition-all text-sm"
            >
              Paid
            </button>
            <button 
              @click="paymentStatusTab = 'notPaid'" 
              :class="paymentStatusTab === 'notPaid' ? 'bg-[#FCA5A5] text-white' : 'bg-gray-200 text-gray-600'"
              class="px-4 py-2 rounded-lg font-medium transition-all text-sm"
            >
              Not Paid
            </button>
          </div>

          <!-- Students List -->
          <div class="bg-white rounded-lg border border-[#E5E7EB] overflow-hidden">
            <div class="space-y-0">
              <!-- Student Item -->
              <div v-for="(student, index) in filteredPaymentStudents" :key="index" 
                   class="flex items-center px-6 py-4 border-b border-[#F0F0F0] hover:bg-[#F9F9F9] transition-colors">
                <div class="flex items-center gap-4">
                  <img :src="student.avatar" :alt="student.name" class="w-12 h-12 rounded-full object-cover border border-[#E5E7EB]" />
                  <div>
                    <h3 class="font-semibold text-[#043355] text-base">{{ student.name }}</h3>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Competitions Section -->
      <div v-if="activeSection === 'competitions'" class="px-10 pt-8 pb-10">
        <!-- Search Bar -->
        <div class="mb-6">
          <div class="relative">
            <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
            <input 
              v-model="competitionSearch" 
              type="text" 
              placeholder="Search for Admin" 
              class="w-full pl-10 pr-4 py-3 border border-[#D1D5DC] rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-[#043355]"
            />
          </div>
        </div>

        <!-- Competition Type Tabs -->
        <div class="flex gap-2 mb-8">
          <button 
            @click="competitionTypeTab = 'monthly'" 
            :class="competitionTypeTab === 'monthly' ? 'bg-[#FEF3C7] text-[#D97706] border border-[#F59E0B]' : 'bg-gray-200 text-gray-600'"
            class="px-6 py-2 rounded-lg font-medium transition-all text-sm"
          >
            Monthly
          </button>
          <button 
            @click="competitionTypeTab = 'seasonal'" 
            :class="competitionTypeTab === 'seasonal' ? 'bg-[#E0E7FF] text-[#3730A3] border border-[#6366F1]' : 'bg-gray-200 text-gray-600'"
            class="px-6 py-2 rounded-lg font-medium transition-all text-sm"
          >
            Seasonal
          </button>
        </div>

        <!-- Competitions List -->
        <div class="space-y-4 mb-8">
          <div v-for="(competition, index) in filteredCompetitions" :key="index" 
               class="bg-white rounded-lg border border-[#E5E7EB] p-6 hover:shadow-md transition-shadow">
            <div class="flex items-start gap-4">
              <!-- Competition Image -->
              <img :src="competition.image" :alt="competition.title" class="w-24 h-20 object-cover rounded-lg" />
              
              <!-- Competition Details -->
              <div class="flex-1">
                <h3 class="text-xl font-bold text-[#043355] mb-2">{{ competition.title }}</h3>
                <p class="text-gray-600 text-sm mb-2">{{ competition.description }}</p>
                <div class="flex items-center gap-4 text-sm text-gray-500 mb-2">
                  <span class="flex items-center gap-1">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    {{ competition.date }}
                  </span>
                </div>
                <div class="flex items-center gap-2">
                  <span class="inline-flex items-center gap-1 bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">
                    <svg width="12" height="12" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                    {{ competition.badge }}
                  </span>
                </div>
              </div>
              
              <!-- Action Buttons -->
              <div class="flex gap-2">
                <button @click="editCompetition(competition)" class="bg-yellow-50 text-yellow-600 border border-yellow-200 px-4 py-2 rounded-lg font-medium hover:bg-yellow-100 transition-all text-sm">
                  Edit
                </button>
                <button @click="deleteCompetition(competition)" class="bg-red-50 text-red-600 border border-red-200 px-4 py-2 rounded-lg font-medium hover:bg-red-100 transition-all text-sm">
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-center gap-4 mb-8">
          <button @click="openAddTrainingRoundModal()" class="bg-[#4F46E5] text-white px-6 py-3 rounded-lg font-medium hover:bg-[#3730A3] transition-all flex items-center gap-2">
            <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"/>
            </svg>
            Add Training Round
          </button>
          <button @click="publishCompetition()" class="bg-[#3B82F6] text-white px-6 py-3 rounded-lg font-medium hover:bg-[#2563EB] transition-all flex items-center gap-2">
            <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"/>
            </svg>
            Publish Competition
          </button>
        </div>
      </div>

      <!-- Partners Section -->
      <div v-if="activeSection === 'partners'" class="space-y-6">
        <div class="bg-white rounded-2xl p-8 border border-[#E5E7EB]">
          <div class="mb-8">
            <h3 class="text-2xl font-bold text-[#043355] text-center">Waiting List</h3>
          </div>
          
          <!-- Partners Waiting List -->
          <div class="space-y-4">
            <div v-for="partner in partnersWaitingList" :key="partner.id" 
                 class="flex items-center justify-between p-4 bg-gray-50 rounded-xl border border-gray-200">
              <div class="flex items-center gap-4">
                <div class="w-12 h-12 rounded-full overflow-hidden flex-shrink-0">
                  <img :src="partner.logo" :alt="partner.name" class="w-full h-full object-cover">
                </div>
                <span class="text-lg font-medium text-[#043355]">{{ partner.name }}</span>
              </div>
              
              <div class="flex gap-3">
                <button @click="acceptPartner(partner.id)"
                        class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium">
                  Accept
                </button>
                <button @click="rejectPartner(partner.id)"
                        class="px-6 py-2 bg-white text-red-500 border border-red-500 rounded-lg hover:bg-red-50 transition-colors font-medium">
                  Reject
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Contact & Help Section -->
      <div v-if="activeSection === 'contact'" class="space-y-6">
        <div class="bg-white rounded-2xl p-8 border border-[#E5E7EB] max-w-2xl mx-auto">
          <div class="text-center">
            <h2 class="text-3xl font-bold text-[#043355] mb-6">Contact Us</h2>
            
            <div class="text-gray-600 text-sm leading-relaxed mb-8 space-y-2">
              <p>Si, vitae amet, massa non, gravida sed eu leo. libero, nulla. Cras</p>
              <p>eu dignissim, tristique nibh diam consequat ultrices elit mollis.</p>
              <p>Molestique ut tristique risam sit</p>
              <p>et, placerat non molestie mattis sit. At tellus, mauris at. Lorem</p>
              <p>amet, ut vel tristique cursus. Ut cenas feu arcu tincidunt. Donec</p>
              <p>duis vultan volutpat. Lorem</p>
            </div>
            
            <button @click="contactCeo()" 
                    class="w-full bg-green-500 text-white py-4 rounded-lg font-medium hover:bg-green-600 transition-all flex items-center justify-center gap-3">
              <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.486"/>
              </svg>
              Contact with Ceo
            </button>
          </div>
        </div>
      </div>

      <!-- Settings Section -->
      <div v-if="activeSection === 'settings'" class="px-10 pt-8 pb-10">
        <div class="bg-white rounded-2xl p-8 border border-[#E5E7EB] max-w-2xl mx-auto">
          <!-- Profile Picture and Name -->
          <div class="text-center mb-8">
            <div class="w-32 h-32 rounded-full overflow-hidden mx-auto mb-4 border-4 border-gray-200">
              <img src="https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=200&h=200&fit=crop&crop=face" 
                   alt="Gamal Yousef" 
                   class="w-full h-full object-cover">
            </div>
            <h2 class="text-2xl font-bold text-[#043355] mb-1">Gamal Yousef</h2>
            <p class="text-gray-500">Super Admin</p>
          </div>

          <!-- Form Fields -->
          <div class="space-y-6">
            <!-- First Name -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">first name</label>
              <input 
                v-model="adminProfile.firstName" 
                type="text" 
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                placeholder="Gamal"
              />
            </div>

            <!-- Last Name -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">last name</label>
              <input 
                v-model="adminProfile.lastName" 
                type="text" 
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                placeholder="Yousef"
              />
            </div>

            <!-- Phone Number -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
              <input 
                v-model="adminProfile.phoneNumber" 
                type="tel" 
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                placeholder="+966457347584993"
              />
            </div>

            <!-- Father Number -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Father Number</label>
              <input 
                v-model="adminProfile.fatherNumber" 
                type="tel" 
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                placeholder="+966457347584993"
              />
            </div>

            <!-- Email -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
              <input 
                v-model="adminProfile.email" 
                type="email" 
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                placeholder="<EMAIL>"
              />
            </div>

            <!-- Password -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
              <input 
                v-model="adminProfile.password" 
                type="password" 
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                placeholder="••••••••••••"
              />
            </div>

            <!-- Save Button -->
            <div class="pt-4">
              <button @click="saveProfileChanges()" 
                      class="w-full bg-yellow-500 text-white py-4 rounded-lg font-medium hover:bg-yellow-600 transition-all">
                Save Changes
              </button>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Add Group Modal -->
    <div v-if="showAddGroupModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-2xl p-8 w-96 max-w-md mx-4">
        <h2 class="text-2xl font-bold text-[#043355] mb-6 text-center">Add New Group</h2>
        
        <div class="space-y-6">
          <!-- Select Student -->
          <div>
            <label class="block text-lg font-semibold text-[#043355] mb-3">select student :</label>
            <select v-model="selectedStudent" class="w-full px-4 py-3 border border-[#D1D5DC] rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-600">
              <option value="">Select...</option>
              <option v-for="student in availableStudents" :key="student.id" :value="student">
                {{ student.name }}
              </option>
            </select>
          </div>
          
          <!-- Buttons -->
          <div class="flex gap-4 justify-end pt-4">
            <button @click="closeAddGroupModal()" class="px-6 py-3 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50 transition-all">
              Cancel
            </button>
            <button @click="confirmAddGroup()" class="bg-[#3B82F6] text-white px-8 py-3 rounded-full font-medium hover:bg-[#2563EB] transition-all">
              Add
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Add Teacher Modal -->
    <div v-if="showAddTeacherModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-2xl p-8 w-96 max-w-md mx-4">
        <h2 class="text-2xl font-bold text-[#043355] mb-6 text-center">Add New Teacher</h2>
        
        <div class="space-y-6">
          <!-- Select Teacher -->
          <div>
            <label class="block text-lg font-semibold text-[#043355] mb-3">select teacher :</label>
            <select v-model="selectedTeacher" class="w-full px-4 py-3 border border-[#D1D5DC] rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-600">
              <option value="">Select...</option>
              <option v-for="teacher in availableTeachers" :key="teacher.id" :value="teacher">
                {{ teacher.name }}
              </option>
            </select>
          </div>
          
          <!-- Buttons -->
          <div class="flex gap-4 justify-end pt-4">
            <button @click="closeAddTeacherModal()" class="px-6 py-3 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50 transition-all">
              Cancel
            </button>
            <button @click="confirmAddTeacher()" class="bg-[#3B82F6] text-white px-8 py-3 rounded-full font-medium hover:bg-[#2563EB] transition-all">
              Add
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Add Student Modal -->
    <div v-if="showAddStudentModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-2xl p-8 w-96 max-w-md mx-4">
        <h2 class="text-2xl font-bold text-[#043355] mb-6 text-center">Add New Student</h2>
        
        <div class="space-y-6">
          <!-- Select Student -->
          <div>
            <label class="block text-lg font-semibold text-[#043355] mb-3">select student :</label>
            <select v-model="selectedNewStudent" class="w-full px-4 py-3 border border-[#D1D5DC] rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-600">
              <option value="">Select...</option>
              <option v-for="student in availableNewStudents" :key="student.id" :value="student">
                {{ student.name }}
              </option>
            </select>
          </div>
          
          <!-- Buttons -->
          <div class="flex gap-4 justify-end pt-4">
            <button @click="closeAddStudentModal()" class="px-6 py-3 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50 transition-all">
              Cancel
            </button>
            <button @click="confirmAddStudent()" class="bg-[#3B82F6] text-white px-8 py-3 rounded-full font-medium hover:bg-[#2563EB] transition-all">
              Add
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Add Training Round Modal -->
    <div v-if="showAddTrainingRoundModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-2xl p-8 w-full max-w-md mx-4">
        <h2 class="text-2xl font-bold text-[#043355] mb-6 text-center">Add Training Round</h2>
        
        <div class="space-y-6">
          <!-- Round Name -->
          <div>
            <label class="block text-base font-medium text-[#043355] mb-2">Round Name</label>
            <input 
              v-model="newTrainingRound.name" 
              type="text" 
              placeholder="First Round"
              class="w-full px-4 py-3 border border-[#E5E7EB] rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-[#F8F9FA]"
            />
          </div>

          <!-- Round ID -->
          <div>
            <label class="block text-base font-medium text-[#043355] mb-2">Round ID</label>
            <input 
              v-model="newTrainingRound.id" 
              type="text" 
              placeholder="1253"
              class="w-full px-4 py-3 border border-[#E5E7EB] rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-[#F8F9FA]"
            />
          </div>

          <!-- Date -->
          <div>
            <label class="block text-base font-medium text-[#043355] mb-2">Date</label>
            <div class="relative">
              <input 
                v-model="newTrainingRound.date" 
                type="date" 
                class="w-full px-4 py-3 border border-[#E5E7EB] rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-[#F8F9FA]"
              />
              <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
            </div>
          </div>

          <!-- Buttons -->
          <div class="flex gap-4 justify-end pt-4">
            <button @click="closeAddTrainingRoundModal()" class="px-6 py-3 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50 transition-all">
              Cancel
            </button>
            <button @click="confirmAddTrainingRound()" class="bg-[#3B82F6] text-white px-8 py-3 rounded-lg font-medium hover:bg-[#2563EB] transition-all">
              Add
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

const activeSection = ref('dashboard')
const groupsActiveTab = ref('waiting')
const teamsActiveTab = ref('waiting')
const paymentActiveTab = ref('invoices')
const paymentStatusTab = ref('paid')
const competitionTypeTab = ref('monthly')
const competitionSearch = ref('')
const showAddGroupModal = ref(false)
const showAddTeacherModal = ref(false)
const showAddStudentModal = ref(false)
const showAddTrainingRoundModal = ref(false)
const selectedStudent = ref('')
const selectedTeacher = ref('')
const selectedNewStudent = ref('')

// Training Round Form Data
const newTrainingRound = ref({
  name: '',
  id: '',
  date: ''
})

// Available students for selection
const availableStudents = ref([
  { id: 1, name: 'Ahmed Ali' },
  { id: 2, name: 'Sara Mohamed' },
  { id: 3, name: 'Omar Hassan' },
  { id: 4, name: 'Fatima Nour' },
  { id: 5, name: 'Khaled Mostafa' },
  { id: 6, name: 'Mona Abdel' },
  { id: 7, name: 'Youssef Ibrahim' },
  { id: 8, name: 'Nada Sameh' }
])

// Available teachers for selection
const availableTeachers = ref([
  { id: 1, name: 'Dr. Hassan Mohamed' },
  { id: 2, name: 'Prof. Amira Saleh' },
  { id: 3, name: 'Dr. Ahmed Farouk' },
  { id: 4, name: 'Prof. Mona Rashad' },
  { id: 5, name: 'Dr. Khaled Nasser' },
  { id: 6, name: 'Prof. Fatima Zaki' }
])

// Available new students for adding to existing groups
const availableNewStudents = ref([
  { id: 9, name: 'Layla Mahmoud' },
  { id: 10, name: 'Karim Adel' },
  { id: 11, name: 'Rana Hosny' },
  { id: 12, name: 'Mahmoud Reda' },
  { id: 13, name: 'Dina Essam' },
  { id: 14, name: 'Tarek Fouad' }
])

// Teams waiting list students data
const teamsWaitingListStudents = ref([
  {
    id: 1,
    name: 'Lila Youssef',
    request: 'Request to join monthly competitions',
    avatar: 'https://randomuser.me/api/portraits/women/1.jpg'
  },
  {
    id: 2,
    name: 'jojo kamal',
    request: 'Request to join monthly competitions',
    avatar: 'https://randomuser.me/api/portraits/women/2.jpg'
  },
  {
    id: 3,
    name: 'jank koko',
    request: 'Request to join monthly competitions',
    avatar: 'https://randomuser.me/api/portraits/men/1.jpg'
  },
  {
    id: 4,
    name: 'Lila Youssef',
    request: 'Request to join monthly competitions',
    avatar: 'https://randomuser.me/api/portraits/women/3.jpg'
  },
  {
    id: 5,
    name: 'jojo kamal',
    request: 'Request to join monthly competitions',
    avatar: 'https://randomuser.me/api/portraits/women/4.jpg'
  },
  {
    id: 6,
    name: 'jank koko',
    request: 'Request to join monthly competitions',
    avatar: 'https://randomuser.me/api/portraits/men/2.jpg'
  }
])

// Team 1 members data
const team1Members = ref([
  {
    id: 1,
    name: 'Lila Youssef',
    avatar: 'https://randomuser.me/api/portraits/women/1.jpg'
  },
  {
    id: 2,
    name: 'Nora Saad',
    avatar: 'https://randomuser.me/api/portraits/women/5.jpg'
  },
  {
    id: 3,
    name: 'Mollar Youssef',
    avatar: 'https://randomuser.me/api/portraits/men/3.jpg'
  },
  {
    id: 4,
    name: 'Lila Youssef',
    avatar: 'https://randomuser.me/api/portraits/women/6.jpg'
  },
  {
    id: 5,
    name: 'Nora Saad',
    avatar: 'https://randomuser.me/api/portraits/women/7.jpg'
  },
  {
    id: 6,
    name: 'Mollar Youssef',
    avatar: 'https://randomuser.me/api/portraits/men/4.jpg'
  }
])

// Payment invoices data
const payments = ref([
  {
    id: 1,
    title: 'payment for level 1',
    submittedBy: 'Nala Mohammad',
    type: 'groups',
    status: 'pending'
  },
  {
    id: 2,
    title: 'payment for competition',
    submittedBy: 'Bolla Mollar',
    type: 'competitions',
    status: 'pending'
  },
  {
    id: 3,
    title: 'payment for competition',
    submittedBy: 'Bolla Mollar',
    type: 'competitions',
    status: 'pending'
  },
  {
    id: 4,
    title: 'payment for competition',
    submittedBy: 'Bolla Mollar',
    type: 'competitions',
    status: 'pending'
  },
  {
    id: 5,
    title: 'payment for competition',
    submittedBy: 'Bolla Mollar',
    type: 'competitions',
    status: 'pending'
  }
])

// Payment students data
const paymentStudents = ref([
  {
    id: 1,
    name: 'Lila Youssef',
    avatar: 'https://randomuser.me/api/portraits/women/1.jpg',
    paymentStatus: 'paid'
  },
  {
    id: 2,
    name: 'Nora Saad',
    avatar: 'https://randomuser.me/api/portraits/women/5.jpg',
    paymentStatus: 'paid'
  },
  {
    id: 3,
    name: 'Mollar Youssef',
    avatar: 'https://randomuser.me/api/portraits/men/3.jpg',
    paymentStatus: 'paid'
  },
  {
    id: 4,
    name: 'Lila Youssef',
    avatar: 'https://randomuser.me/api/portraits/women/6.jpg',
    paymentStatus: 'notPaid'
  },
  {
    id: 5,
    name: 'Nora Saad',
    avatar: 'https://randomuser.me/api/portraits/women/7.jpg',
    paymentStatus: 'notPaid'
  },
  {
    id: 6,
    name: 'Mollar Youssef',
    avatar: 'https://randomuser.me/api/portraits/men/4.jpg',
    paymentStatus: 'notPaid'
  }
])

// Competitions data
const competitions = ref([
  {
    id: 1,
    title: 'May Math Madness',
    description: 'Solve 10 logic puzzles in 5 days...',
    date: '12 May 2025',
    badge: 'Badge: Logic Sprinter',
    image: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=300&h=200&fit=crop',
    type: 'monthly'
  },
  {
    id: 2,
    title: 'May Math Madness',
    description: 'Solve 10 logic puzzles in 5 days...',
    date: '12 May 2025',
    badge: 'Badge: Logic Sprinter',
    image: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=300&h=200&fit=crop',
    type: 'monthly'
  },
  {
    id: 3,
    title: 'May Math Madness',
    description: 'Solve 10 logic puzzles in 5 days...',
    date: '12 May 2025',
    badge: 'Badge: Logic Sprinter',
    image: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=300&h=200&fit=crop',
    type: 'seasonal'
  },
  {
    id: 4,
    title: 'Summer Challenge',
    description: 'Complete advanced math problems in 7 days...',
    date: '15 Jun 2025',
    badge: 'Badge: Summer Champion',
    image: 'https://images.unsplash.com/photo-1509228468518-180dd4864904?w=300&h=200&fit=crop',
    type: 'seasonal'
  }
])

// Waiting list students data
const waitingListStudents = ref([
  {
    id: 1,
    name: 'Lila Youssef',
    progress: '65%',
    avatar: 'https://randomuser.me/api/portraits/women/1.jpg'
  },
  {
    id: 2,
    name: 'jojo kamal',
    progress: '86%',
    avatar: 'https://randomuser.me/api/portraits/women/2.jpg'
  },
  {
    id: 3,
    name: 'jank koko',
    progress: '',
    avatar: 'https://randomuser.me/api/portraits/men/1.jpg'
  },
  {
    id: 4,
    name: 'Lila Youssef',
    progress: '65%',
    avatar: 'https://randomuser.me/api/portraits/women/3.jpg'
  },
  {
    id: 5,
    name: 'jojo kamal',
    progress: '86%',
    avatar: 'https://randomuser.me/api/portraits/women/4.jpg'
  },
  {
    id: 6,
    name: 'jank koko',
    progress: '',
    avatar: 'https://randomuser.me/api/portraits/men/2.jpg'
  }
])

// Group A1 students data
const groupA1Students = ref([
  {
    id: 1,
    name: 'Lila Youssef',
    avatar: 'https://randomuser.me/api/portraits/women/1.jpg'
  },
  {
    id: 2,
    name: 'Nora Saad',
    avatar: 'https://randomuser.me/api/portraits/women/5.jpg'
  },
  {
    id: 3,
    name: 'Mollar Youssef',
    avatar: 'https://randomuser.me/api/portraits/men/3.jpg'
  },
  {
    id: 4,
    name: 'Lila Youssef',
    avatar: 'https://randomuser.me/api/portraits/women/6.jpg'
  },
  {
    id: 5,
    name: 'Nora Saad',
    avatar: 'https://randomuser.me/api/portraits/women/7.jpg'
  },
  {
    id: 6,
    name: 'Mollar Youssef',
    avatar: 'https://randomuser.me/api/portraits/men/4.jpg'
  }
])

// Partners waiting list data
const partnersWaitingList = ref([
  {
    id: 1,
    name: 'Company Name',
    logo: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=100&h=100&fit=crop&crop=entropy'
  },
  {
    id: 2,
    name: 'Company Name',
    logo: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=100&h=100&fit=crop&crop=entropy'
  },
  {
    id: 3,
    name: 'Company Name',
    logo: 'https://images.unsplash.com/photo-1572021335469-31706a17aaef?w=100&h=100&fit=crop&crop=entropy'
  },
  {
    id: 4,
    name: 'Company Name',
    logo: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=100&h=100&fit=crop&crop=entropy'
  },
  {
    id: 5,
    name: 'Company Name',
    logo: 'https://images.unsplash.com/photo-1560472355-536de3962603?w=100&h=100&fit=crop&crop=entropy'
  },
  {
    id: 6,
    name: 'Company Name',
    logo: 'https://images.unsplash.com/photo-1599305445671-ac291c95aaa9?w=100&h=100&fit=crop&crop=entropy'
  }
])

// Admin profile data
const adminProfile = ref({
  firstName: 'Gamal',
  lastName: 'Yousef',
  phoneNumber: '+966457347584993',
  fatherNumber: '+966457347584993',
  email: '<EMAIL>',
  password: '••••••••••••'
})

// Computed properties for filtered competitions
const filteredCompetitions = computed(() => {
  let filtered = competitions.value.filter(comp => comp.type === competitionTypeTab.value)
  
  if (competitionSearch.value) {
    filtered = filtered.filter(comp => 
      comp.title.toLowerCase().includes(competitionSearch.value.toLowerCase()) ||
      comp.description.toLowerCase().includes(competitionSearch.value.toLowerCase())
    )
  }
  
  return filtered
})

const getSectionTitle = () => {
  const titles: Record<string, string> = {
    dashboard: 'Analysis',
    groups: 'Groups Management',
    teams: 'Teams Management', 
    payment: 'Payment Management',
    competitions: 'Competitions Management',
    partners: 'Partners Management',
    contact: 'Contact & Help',
    settings: 'Settings'
  }
  return titles[activeSection.value] || activeSection.value
}

// Computed property for filtered payments (now returns all payments)
const filteredPayments = computed(() => {
  return payments.value
})

// Computed property for filtered payment students
const filteredPaymentStudents = computed(() => {
  return paymentStudents.value.filter(student => student.paymentStatus === paymentStatusTab.value)
})

const getGroupTabTitle = () => {
  const titles: Record<string, string> = {
    groupA1: 'Group A1',
    groupB2: 'Group B2',
    groupC3: 'Group C3'
  }
  return titles[groupsActiveTab.value] || groupsActiveTab.value
}

const getTeamTabTitle = () => {
  const titles: Record<string, string> = {
    team1: 'Team 1',
    team2: 'Team 2',
    team3: 'Team 3'
  }
  return titles[teamsActiveTab.value] || teamsActiveTab.value
}

const acceptStudent = (student: any) => {
  console.log('Accepting student:', student.name)
  // Here you would typically make an API call to accept the student
  // For now, we'll just remove them from the waiting list
  const index = waitingListStudents.value.findIndex(s => s.id === student.id)
  if (index > -1) {
    waitingListStudents.value.splice(index, 1)
  }
}

const rejectStudent = (student: any) => {
  console.log('Rejecting student:', student.name)
  // Here you would typically make an API call to reject the student
  // For now, we'll just remove them from the waiting list
  const index = waitingListStudents.value.findIndex(s => s.id === student.id)
  if (index > -1) {
    waitingListStudents.value.splice(index, 1)
  }
}

const editStudent = (student: any) => {
  console.log('Editing student:', student.name)
  // Here you would typically open an edit modal or navigate to edit page
  alert(`Editing student: ${student.name}`)
}

const deleteStudent = (student: any, groupType: string) => {
  console.log('Deleting student:', student.name, 'from', groupType)
  if (groupType === 'groupA1') {
    const index = groupA1Students.value.findIndex(s => s.id === student.id)
    if (index > -1) {
      groupA1Students.value.splice(index, 1)
    }
  }
}

const openWhatsApp = () => {
  const phoneNumber = "+201234567890"
  const message = "Hello! I need help with group management."
  const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`
  window.open(whatsappUrl, '_blank')
}

const addGroup = () => {
  console.log('Opening add group modal')
  showAddGroupModal.value = true
}

const closeAddGroupModal = () => {
  showAddGroupModal.value = false
  selectedStudent.value = ''
}

const confirmAddGroup = () => {
  if (!selectedStudent.value) {
    alert('Please select a student first!')
    return
  }
  
  console.log('Adding student to new group:', selectedStudent.value.name)
  
  // Here you would typically make an API call to create a new group with the selected student
  alert(`New group created with student: ${selectedStudent.value.name}`)
  
  // Close modal and reset selection
  closeAddGroupModal()
}

const addTeacher = () => {
  console.log('Opening add teacher modal')
  showAddTeacherModal.value = true
}

const addStudent = () => {
  console.log('Opening add student modal')
  showAddStudentModal.value = true
}

const closeAddTeacherModal = () => {
  showAddTeacherModal.value = false
  selectedTeacher.value = ''
}

const closeAddStudentModal = () => {
  showAddStudentModal.value = false
  selectedNewStudent.value = ''
}

const confirmAddTeacher = () => {
  if (!selectedTeacher.value) {
    alert('Please select a teacher first!')
    return
  }
  
  console.log('Adding teacher to group:', selectedTeacher.value.name)
  alert(`Teacher ${selectedTeacher.value.name} has been added to the group!`)
  closeAddTeacherModal()
}

const confirmAddStudent = () => {
  if (!selectedNewStudent.value) {
    alert('Please select a student first!')
    return
  }
  
  console.log('Adding student to group:', selectedNewStudent.value.name)
  
  // Add the student to Group A1
  groupA1Students.value.push({
    id: selectedNewStudent.value.id,
    name: selectedNewStudent.value.name,
    avatar: `https://randomuser.me/api/portraits/${selectedNewStudent.value.id % 2 === 0 ? 'women' : 'men'}/${selectedNewStudent.value.id}.jpg`
  })
  
  alert(`Student ${selectedNewStudent.value.name} has been added to the group!`)
  closeAddStudentModal()
}

// Training Round Modal Functions
const openAddTrainingRoundModal = () => {
  console.log('Opening add training round modal')
  showAddTrainingRoundModal.value = true
}

const closeAddTrainingRoundModal = () => {
  showAddTrainingRoundModal.value = false
  // Reset form data
  newTrainingRound.value = {
    name: '',
    id: '',
    date: ''
  }
}

const confirmAddTrainingRound = () => {
  if (!newTrainingRound.value.name || !newTrainingRound.value.id || !newTrainingRound.value.date) {
    alert('Please fill in all fields!')
    return
  }
  
  console.log('Adding training round:', newTrainingRound.value)
  alert(`Training Round "${newTrainingRound.value.name}" has been added successfully!`)
  closeAddTrainingRoundModal()
}

const acceptTeamStudent = (student: any) => {
  console.log('Accepting team student:', student.name)
  // Here you would typically make an API call to accept the student for competitions
  // For now, we'll just remove them from the teams waiting list
  const index = teamsWaitingListStudents.value.findIndex(s => s.id === student.id)
  if (index > -1) {
    teamsWaitingListStudents.value.splice(index, 1)
  }
  alert(`${student.name} has been accepted for monthly competitions!`)
}

const rejectTeamStudent = (student: any) => {
  console.log('Rejecting team student:', student.name)
  // Here you would typically make an API call to reject the student for competitions
  // For now, we'll just remove them from the teams waiting list
  const index = teamsWaitingListStudents.value.findIndex(s => s.id === student.id)
  if (index > -1) {
    teamsWaitingListStudents.value.splice(index, 1)
  }
  alert(`${student.name} has been rejected for monthly competitions.`)
}

const editTeamMember = (member: any) => {
  console.log('Editing team member:', member.name)
  // Here you would typically open an edit modal or navigate to edit page
  alert(`Editing team member: ${member.name}`)
}

const deleteTeamMember = (member: any, teamType: string) => {
  console.log('Deleting team member:', member.name, 'from', teamType)
  if (teamType === 'team1') {
    const index = team1Members.value.findIndex(m => m.id === member.id)
    if (index > -1) {
      team1Members.value.splice(index, 1)
    }
    alert(`${member.name} has been removed from Team 1`)
  }
}

const addNewTeam = () => {
  console.log('Adding new team')
  alert('Add New Team functionality coming soon!')
}

const deleteTeam = () => {
  console.log('Deleting current team')
  if (confirm('Are you sure you want to delete this team?')) {
    alert('Team deleted successfully!')
    // Here you could also clear the team members or redirect to another tab
  }
}

const acceptPayment = (payment: any) => {
  console.log('Accepting payment:', payment.title)
  // Update payment status
  const index = payments.value.findIndex(p => p.id === payment.id)
  if (index > -1) {
    payments.value[index].status = 'accepted'
    // Remove from list after accepting
    payments.value.splice(index, 1)
  }
  alert(`Payment "${payment.title}" by ${payment.submittedBy} has been accepted!`)
}

const rejectPayment = (payment: any) => {
  console.log('Rejecting payment:', payment.title)
  // Update payment status
  const index = payments.value.findIndex(p => p.id === payment.id)
  if (index > -1) {
    payments.value[index].status = 'rejected'
    // Remove from list after rejecting
    payments.value.splice(index, 1)
  }
  alert(`Payment "${payment.title}" by ${payment.submittedBy} has been rejected.`)
}

const deleteGroup = () => {
  console.log('Deleting group')
  if (confirm('Are you sure you want to delete this group?')) {
    alert('Group deleted successfully!')
  }
}

// Partners management functions
const acceptPartner = (partnerId: number) => {
  console.log('Accepting partner:', partnerId)
  const index = partnersWaitingList.value.findIndex(p => p.id === partnerId)
  if (index > -1) {
    const partner = partnersWaitingList.value[index]
    partnersWaitingList.value.splice(index, 1)
    alert(`${partner.name} has been accepted as a partner!`)
  }
}

const rejectPartner = (partnerId: number) => {
  console.log('Rejecting partner:', partnerId)
  const index = partnersWaitingList.value.findIndex(p => p.id === partnerId)
  if (index > -1) {
    const partner = partnersWaitingList.value[index]
    partnersWaitingList.value.splice(index, 1)
    alert(`${partner.name} has been rejected.`)
  }
}

// Contact functions
const contactCeo = () => {
  console.log('Opening WhatsApp to contact CEO')
  const phoneNumber = '+1234567890' // Replace with actual CEO phone number
  const message = 'Hello, I would like to get in touch regarding administrative matters.'
  const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`
  window.open(whatsappUrl, '_blank')
}

// Settings functions
const saveProfileChanges = () => {
  console.log('Saving profile changes:', adminProfile.value)
  // Here you would typically make an API call to save the profile changes
  alert('Profile changes saved successfully!')
}
</script>
