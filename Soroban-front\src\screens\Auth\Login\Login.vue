<template>
  <div class="w-full min-h-screen [background:linear-gradient(180deg,rgba(239,246,255,0.8)_0%,rgba(219,234,254,0.8)_100%)] flex items-center justify-center p-4 sm:p-8">
    <div class="flex flex-col w-full max-w-md items-center gap-10">
      <!-- Logo and title -->
      <div class="flex flex-col w-72 items-center justify-center gap-10 relative">
        <div class="flex items-center justify-center w-full relative">
          <img
            class="relative w-[60px] h-[60px] object-cover"
            alt="Logo lerning"
            src="https://c.animaapp.com/mc793xiqDBufmA/img/logo-lerning-removebg-preview-1.png"
          />
          <div class="relative w-auto brand-text text-3xl tracking-tight leading-8">
            <span class="font-bold text-[#d08700]">{{ t('brand.main') }}</span>
            <span class="font-bold text-slate-800">&nbsp;{{ t('brand.secondary') }}</span>
          </div>
        </div>

        <!-- Sign in header -->
        <div class="gap-2 flex flex-col items-center relative self-stretch w-full">
          <h2 class="relative self-stretch font-bold text-slate-800 text-3xl text-center leading-normal">
            {{ t('loginPage.title') }}
          </h2>
          <p class="relative w-fit text-gray-500 text-lg tracking-wide whitespace-nowrap">
            {{ t('loginPage.subtitle') }}
          </p>
        </div>
      </div>

      <!-- Form section -->
      <form @submit.prevent="handleLogin" class="flex flex-col items-center justify-center gap-8 relative self-stretch w-full">
        <div class="flex flex-col items-center gap-5 relative self-stretch w-full">
          <!-- Input fields -->
          <div class="relative w-full group">
            <MailIcon class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400 transition-colors group-focus-within:text-blue-600" />
            <input
              v-model="email"
              type="email"
              :placeholder="t('loginPage.email')"
              class="pl-12 pr-5 py-4 w-full h-auto rounded-xl border border-solid border-gray-300 text-base bg-white/60 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition"
            />
          </div>

          <div class="relative w-full group">
            <LockIcon class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400 transition-colors group-focus-within:text-blue-600" />
            <input
              v-model="password"
              type="password"
              :placeholder="t('loginPage.password')"
              class="pl-12 pr-5 py-4 w-full h-auto rounded-xl border border-solid border-gray-300 text-base bg-white/60 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition"
            />
          </div>

          <!-- Remember me and forgot password -->
          <div class="flex items-center justify-between relative self-stretch w-full">
            <div class="inline-flex items-center gap-2 relative">
              <input
                type="checkbox"
                id="remember-me"
                v-model="rememberMe"
                class="w-4 h-4 rounded border-gray-400 text-blue-600 focus:ring-offset-0 focus:ring-blue-500 cursor-pointer"
              />
              <label
                for="remember-me"
                class="relative w-fit text-slate-700 text-sm font-medium tracking-[0] leading-normal whitespace-nowrap cursor-pointer"
              >
                {{ t('loginPage.remember') }}
              </label>
            </div>
            <button @click="goToForgotPassword" type="button" class="relative w-fit text-blue-600 text-sm tracking-[0] leading-normal hover:underline font-semibold">
              {{ t('loginPage.forgot') }}
            </button>
          </div>
        </div>

        <!-- Error message -->
        <div v-if="errorMessage" class="w-full p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg text-sm">
          {{ errorMessage }}
        </div>

        <!-- Login buttons -->
        <div class="flex flex-col sm:flex-row items-center gap-4 relative self-stretch w-full">
          <button
            type="submit"
            :disabled="isLoading"
            class="flex-1 w-full px-7 py-3 h-auto bg-blue-600 rounded-lg text-white text-lg font-bold transition-all duration-300 transform hover:scale-105 hover:bg-blue-700 shadow-lg hover:shadow-xl hover:shadow-blue-500/30 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
          >
            <span v-if="isLoading">جاري تسجيل الدخول...</span>
            <span v-else>{{ t('loginPage.loginBtn') }}</span>
          </button>
          <button
            @click="handleGuestLogin"
            type="button"
            :disabled="isLoading"
            class="flex-1 w-full px-7 py-3 h-auto rounded-lg border-2 border-solid border-slate-800 text-white text-lg font-bold bg-slate-800 hover:bg-slate-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl hover:shadow-gray-500/20 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
          >
            {{ t('loginPage.guestBtn') }}
          </button>
        </div>

        <!-- Social login section -->
        <div class="gap-5 flex flex-col items-center relative self-stretch w-full">
          <div class="flex items-center gap-4 relative self-stretch w-full">
            <hr class="flex-1 border-gray-300" />
            <span class="relative w-fit text-gray-500 text-sm font-medium tracking-[0] leading-normal whitespace-nowrap">
              {{ t('loginPage.social') }}
            </span>
            <hr class="flex-1 border-gray-300" />
          </div>

          <!-- Social icons -->
          <div class="inline-flex items-center gap-5 relative">
            <button @click="handleSocialLogin('google')" type="button" class="social-btn">
              <svg class="w-6 h-6" viewBox="0 0 48 48"><path fill="#FFC107" d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"></path><path fill="#FF3D00" d="M6.306,14.52C9.539,8.227,16.25,4,24,4c5.268,0,10.046,2.053,13.59,5.627l-5.657,5.657C30.652,14.154,27.559,12,24,12c-5.14,0-9.42,3.823-10.99,8.728L6.306,14.52z"></path><path fill="#4CAF50" d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z"></path><path fill="#1976D2" d="M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z"></path></svg>
            </button>
            <button @click="handleSocialLogin('facebook')" type="button" class="social-btn">
              <svg class="w-7 h-7" viewBox="0 0 32 32"><path fill="#0866FF" d="M16,0C7.164,0,0,7.164,0,16c0,8.836,7.164,16,16,16c8.836,0,16-7.164,16-16C32,7.164,24.836,0,16,0z"/><path fill="#FFFFFF" d="M21.938,16.5H18.812v8.812h-4.375V16.5h-2.125v-3.625h2.125v-2.5c0-2.14,1.308-3.312,3.223-3.312 c0.918,0,1.707,0.068,1.938,0.1v3.25h-1.938c-1.039,0-1.24,0.494-1.24,1.219v1.25h3.312L21.938,16.5z"/></svg>
            </button>
             <button @click="handleSocialLogin('linkedin')" type="button" class="social-btn">
               <svg class="w-6 h-6" fill="#0A66C2" viewBox="0 0 24 24"><path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433a2.062 2.062 0 0 1-2.063-2.065 2.064 2.064 0 1 1 2.063 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.225 0z"></path></svg>
             </button>
          </div>
        </div>

        <!-- Activate Account Link -->
        <div class="relative self-stretch text-base text-center tracking-[0] leading-normal pt-4">
          <span class="text-slate-600">
            {{ t('loginPage.haveCode') }}
          </span>
          <button @click="goToActivate" class="text-blue-600 font-semibold hover:underline">
            {{ t('loginPage.activateLink') }}
          </button>
        </div>
      </form>

      <!-- Register link -->
      <div class="relative self-stretch text-base text-center tracking-[0] leading-normal">
        <span class="text-slate-600">
          {{ t('loginPage.noAccount') }}
        </span>
        <button @click="goToRegister" class="text-blue-600 font-semibold hover:underline">
          {{ t('loginPage.registerLink') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { MailIcon, LockIcon } from 'lucide-vue-next'
import { t } from '../../../locales'
import { apiService } from '../../../services/api'

const router = useRouter()

// Form data
const email = ref('')
const password = ref('')
const rememberMe = ref(false)
const isLoading = ref(false)
const errorMessage = ref('')

// Methods
const handleLogin = async () => {
  if (!email.value || !password.value) {
    errorMessage.value = 'يرجى إدخال البريد الإلكتروني وكلمة المرور'
    return
  }

  isLoading.value = true
  errorMessage.value = ''

  try {
    const response = await apiService.login({
      email: email.value,
      password: password.value,
      remember: rememberMe.value
    })

    if (response.success && response.data) {
      console.log('Login successful:', response.data)

      // Redirect based on user role
      const user = response.data.user
      switch (user.role) {
        case 'superAdmin':
          router.push('/super-admin-dashboard')
          break
        case 'admin':
          router.push('/admin-dashboard')
          break
        case 'teacher':
          router.push('/teacher-dashboard')
          break
        case 'student':
          router.push('/student-dashboard')
          break
        default:
          router.push('/')
      }
    } else {
      errorMessage.value = response.message || 'خطأ في البريد الإلكتروني أو كلمة المرور'
    }
  } catch (error: any) {
    console.error('Login error:', error)
    errorMessage.value = error.message || 'حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.'
  } finally {
    isLoading.value = false
  }
}

const handleGuestLogin = () => {
  console.log('Guest login attempt')
  router.push('/')
}

const handleSocialLogin = (provider: string) => {
  // ملاحظة: يجب استبدال هذا الرابط بالرابط الفعلي لتطبيق Laravel الخاص بك
  // ويفضل وضعه في ملفات البيئة (.env)
  const backendUrl = 'http://localhost:8000'; // مثال: رابط Laravel المحلي

  // سيقوم هذا الرابط بتوجيه المستخدم إلى الواجهة الخلفية لبدء عملية المصادقة
  // يجب أن يكون لديك مسار (route) في Laravel يستجيب لهذا الطلب
  // مثل: Route::get('/auth/{provider}/redirect', [AuthController::class, 'redirect']);
  window.location.href = `${backendUrl}/api/auth/${provider}/redirect`;
}

const goToForgotPassword = () => {
  router.push('/forgot-password');
}

const goToActivate = () => {
  router.push('/activate');
}

const goToRegister = () => {
  router.push('/register')
}
</script>

<style scoped>
.brand-text {
  font-family: 'Tajawal', Helvetica, sans-serif;
}
.social-btn {
  @apply h-12 w-12 flex items-center justify-center p-2 bg-white rounded-full border border-gray-300 hover:bg-gray-100 transition-all duration-300 transform hover:scale-110 hover:shadow-lg;
}
</style> 