<template>
  <div class="w-full min-h-screen [background:linear-gradient(180deg,rgba(239,246,255,0.8)_0%,rgba(219,234,254,0.8)_100%)] flex items-center justify-center p-4 sm:p-8">
    <div class="flex flex-col w-full max-w-md items-center gap-10">
      <!-- Logo and title -->
      <div class="flex flex-col w-72 items-center justify-center gap-10 relative">
        <div class="flex items-center justify-center w-full relative">
          <img
            class="relative w-[60px] h-[60px] object-cover"
            alt="Logo lerning"
            src="https://c.animaapp.com/mc793xiqDBufmA/img/logo-lerning-removebg-preview-1.png"
          />
          <div class="relative w-auto brand-text text-3xl tracking-tight leading-8">
            <span class="font-bold text-[#d08700]">{{ t('brand.main') }}</span>
            <span class="font-bold text-slate-800">&nbsp;{{ t('brand.secondary') }}</span>
          </div>
        </div>

        <!-- Header -->
        <div class="gap-4 flex flex-col items-center relative self-stretch w-full">
          <h2 class="relative self-stretch font-bold text-slate-800 text-3xl text-center leading-normal">
            {{ t('register.chooseRoleTitle') }}
          </h2>
          <p class="relative w-fit text-gray-500 text-lg tracking-wide text-center">
            {{ t('register.chooseRoleSubtitle') }}
          </p>
        </div>
      </div>

      <!-- Role Selection Buttons -->
      <div class="flex flex-col items-center justify-center gap-6 relative self-stretch w-full">
        <button @click="goToRegisterStudent" class="w-full text-left p-6 rounded-xl border-2 border-solid border-gray-300 hover:border-blue-500 hover:bg-blue-50/50 transition-all duration-300 group">
          <div class="flex items-center gap-5">
            <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
              <UserIcon class="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h3 class="font-bold text-lg text-slate-800">{{ t('register.studentRole') }}</h3>
              <p class="text-sm text-gray-500">{{ t('register.studentRoleDesc') }}</p>
            </div>
            <ChevronRightIcon class="w-5 h-5 text-gray-400 ml-auto group-hover:text-blue-600 transition-colors" />
          </div>
        </button>

        <button @click="goToRegisterTeacher" class="w-full text-left p-6 rounded-xl border-2 border-solid border-gray-300 hover:border-blue-500 hover:bg-blue-50/50 transition-all duration-300 group">
          <div class="flex items-center gap-5">
            <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
              <BriefcaseIcon class="w-6 h-6 text-green-600" />
            </div>
            <div>
              <h3 class="font-bold text-lg text-slate-800">{{ t('register.teacherRole') }}</h3>
              <p class="text-sm text-gray-500">{{ t('register.teacherRoleDesc') }}</p>
            </div>
            <ChevronRightIcon class="w-5 h-5 text-gray-400 ml-auto group-hover:text-blue-600 transition-colors" />
          </div>
        </button>
      </div>

      <!-- Back to login link -->
      <div class="relative self-stretch text-base text-center tracking-[0] leading-normal">
        <span class="text-slate-600">{{ t('loginPage.noAccount') }}</span>
        <button @click="goToLogin" class="text-blue-600 font-semibold hover:underline">
          {{ t('loginPage.loginLink') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { UserIcon, BriefcaseIcon, ChevronRightIcon } from 'lucide-vue-next';
import { t } from '../../../locales';

const router = useRouter();

const goToRegisterStudent = () => {
  router.push('/register/student');
};

const goToRegisterTeacher = () => {
  router.push('/register/teacher');
};

const goToLogin = () => {
  router.push('/login');
};
</script>

<style scoped>
.brand-text {
  font-family: 'Tajawal', Helvetica, sans-serif;
}
</style> 