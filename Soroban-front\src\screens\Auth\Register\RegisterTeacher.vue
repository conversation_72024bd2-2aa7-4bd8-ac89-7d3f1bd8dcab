<template>
  <div class="w-full min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center p-4 sm:p-8">
    <div class="flex flex-col w-full max-w-3xl items-center gap-8">
      <!-- Logo and title -->
      <div class="flex flex-col items-center justify-center gap-8 relative">
        <div class="flex items-center justify-center relative">
          <img class="w-16 h-16 object-cover" alt="Logo" src="https://c.animaapp.com/mc793xiqDBufmA/img/logo-lerning-removebg-preview-1.png" />
          <div class="relative brand-text text-3xl tracking-tight">
            <span class="font-bold text-[#d08700]">{{ t('brand.main') }}</span>
            <span class="font-bold text-slate-800 dark:text-white">&nbsp;{{ t('brand.secondary') }}</span>
          </div>
        </div>
        <div class="gap-2 flex flex-col items-center relative">
          <h2 class="font-bold text-slate-800 dark:text-white text-3xl text-center">{{ t('register.teacher.title') }}</h2>
          <p class="text-gray-500 dark:text-gray-400 text-lg text-center">{{ t('register.teacher.subtitle') }}</p>
        </div>
      </div>

      <!-- Form section -->
      <div class="w-full bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 dark:border-gray-700/50 p-8 sm:p-10">
        <form @submit.prevent="handleRegister" class="w-full">
          <div class="space-y-4">
            <!-- Name -->
            <div class="form-group">
              <label class="form-label">{{ t('register.teacher.name') }}</label>
              <input v-model="form.name" type="text" :placeholder="t('register.teacher.name')" class="form-input" :class="{ 'border-red-500 dark:border-red-400': errors.name }" />
              <p v-if="errors.name" class="text-red-500 dark:text-red-400 text-xs mt-1">{{ errors.name }}</p>
            </div>

            <!-- Email -->
            <div class="form-group">
              <label class="form-label">{{ t('email') }}</label>
              <input v-model="form.email" type="email" :placeholder="t('email')" class="form-input" :class="{ 'border-red-500 dark:border-red-400': errors.email }" />
              <p v-if="errors.email" class="text-red-500 dark:text-red-400 text-xs mt-1">{{ errors.email }}</p>
            </div>

            <!-- Phone -->
            <div class="form-group">
              <label class="form-label">{{ t('register.teacher.phone') }}</label>
              <div class="flex items-start gap-0">
                <CountryCodePicker v-model="form.countryCode" class="w-1/3" />
                <input v-model="form.phone" type="tel" :placeholder="t('register.teacher.phonePlaceholder')" class="form-input-phone" :class="{ 'border-red-500 dark:border-red-400': errors.phone }" />
              </div>
              <p v-if="errors.phone" class="text-red-500 dark:text-red-400 text-xs mt-1">{{ errors.phone }}</p>
            </div>
            
            <!-- Password -->
            <div class="form-group">
              <label class="form-label">{{ t('register.password') }}</label>
              <input v-model="form.password" type="password" :placeholder="t('register.password')" class="form-input" :class="{ 'border-red-500 dark:border-red-400': errors.password }" />
              <p v-if="errors.password" class="text-red-500 dark:text-red-400 text-xs mt-1">{{ errors.password }}</p>
            </div>

            <!-- Confirm Password -->
            <div class="form-group">
              <label class="form-label">{{ t('register.confirmPassword') }}</label>
              <input v-model="form.passwordConfirmation" type="password" :placeholder="t('register.confirmPassword')" class="form-input" :class="{ 'border-red-500 dark:border-red-400': errors.passwordConfirmation }" />
              <p v-if="errors.passwordConfirmation" class="text-red-500 dark:text-red-400 text-xs mt-1">{{ errors.passwordConfirmation }}</p>
            </div>
          </div>
          
          <!-- General Error Message -->
          <div v-if="errorMessage" class="mt-4 bg-red-100 dark:bg-red-900/30 border border-red-400 dark:border-red-500 text-red-700 dark:text-red-300 px-4 py-3 rounded relative" role="alert">
            <span class="block sm:inline">{{ errorMessage }}</span>
          </div>

          <!-- Terms and Conditions -->
          <div class="mt-6 flex items-center gap-3">
            <input type="checkbox" v-model="form.termsAccepted" id="terms" class="w-4 h-4 rounded border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-blue-600 focus:ring-blue-500 dark:focus:ring-blue-400" />
            <label for="terms" class="text-sm text-slate-600 dark:text-gray-300">
              {{ t('register.iAccept') }}
              <a href="#" class="font-semibold text-blue-600 dark:text-blue-400 hover:underline">{{ t('register.terms') }}</a>
            </label>
          </div>

          <!-- Submit Button -->
          <div class="mt-8">
            <button type="submit" :disabled="isLoading" class="w-full form-submit-btn">
              <span v-if="isLoading">{{ t('register.creatingAccount') }}...</span>
              <span v-else>{{ t('register.createAccountBtn') }}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { t } from '../../../locales';
import CountryCodePicker from '../../../components/CountryCodePicker.vue';

const router = useRouter();
const form = ref({
  name: '',
  email: '',
  countryCode: '+213',
  phone: '',
  password: '',
  passwordConfirmation: '',
  termsAccepted: false,
});

const errors = ref({
  name: '',
  email: '',
  phone: '',
  password: '',
  passwordConfirmation: '',
});

const isLoading = ref(false);
const errorMessage = ref('');

const validateForm = () => {
  // Clear previous errors
  errors.value = {
    name: '', email: '', phone: '', password: '', passwordConfirmation: ''
  };
  let isValid = true;

  // Required fields
  if (!form.value.name) { errors.value.name = t('validation.required'); isValid = false; }
  if (!form.value.email) { errors.value.email = t('validation.required'); isValid = false; }
  if (!form.value.phone) { errors.value.phone = t('validation.required'); isValid = false; }
  if (!form.value.password) { errors.value.password = t('validation.required'); isValid = false; }
  
  // Email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (form.value.email && !emailRegex.test(form.value.email)) {
    errors.value.email = t('validation.invalidEmail');
    isValid = false;
  }
  
  // Phone number format
  const phoneRegex = /^\d{7,15}$/;
  if (form.value.phone && !phoneRegex.test(form.value.phone)) {
    errors.value.phone = t('validation.phoneInvalid');
    isValid = false;
  }

  // Password strength
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/;
  if (form.value.password && !passwordRegex.test(form.value.password)) {
    errors.value.password = t('validation.passwordWeak');
    isValid = false;
  }

  // Password confirmation
  if (form.value.password && form.value.passwordConfirmation !== form.value.password) {
    errors.value.passwordConfirmation = t('validation.passwordMismatch');
    isValid = false;
  }

  // Terms accepted
  if (!form.value.termsAccepted) {
    errorMessage.value = t('register.acceptTermsError'); // Keep this as a general message
    isValid = false;
  }

  return isValid;
};

const handleRegister = async () => {
  errorMessage.value = ''; // Clear general error
  if (!validateForm()) {
    return;
  }

  isLoading.value = true;
  
  try {
    const response = await fetch('/api/auth/register/teacher', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify({
        name: form.value.name,
        email: form.value.email,
        phone: form.value.countryCode + form.value.phone,
        password: form.value.password,
        password_confirmation: form.value.passwordConfirmation,
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      if (response.status === 422 && data.errors) {
        Object.keys(data.errors).forEach(key => {
          if (key === 'name') errors.value.name = data.errors[key][0];
          if (key === 'email') errors.value.email = data.errors[key][0];
          if (key === 'phone') errors.value.phone = data.errors[key][0];
          if (key === 'password') errors.value.password = data.errors[key][0];
        });
      } else {
        errorMessage.value = data.message || t('register.genericError');
      }
      return;
    }

    // On success, redirect to login page
    router.push({ name: 'Login' });

  } catch (error) {
    errorMessage.value = t('register.networkError');
  } finally {
    isLoading.value = false;
  }
};
</script>

<style scoped>
.brand-text { font-family: 'Tajawal', Helvetica, sans-serif; }
.form-group { @apply space-y-2; }
.form-label { @apply block text-sm font-semibold text-gray-700 dark:text-gray-200; }
.form-input { @apply w-full px-4 py-4 rounded-lg border border-solid border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition; }
.form-input-phone { @apply flex-1 min-w-0 w-full px-4 py-4 rounded-r-lg border-l-0 border border-solid border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition; }
.form-submit-btn { @apply w-full px-7 py-4 bg-blue-600 dark:bg-blue-500 rounded-lg text-white text-lg font-bold transition-all hover:bg-blue-700 dark:hover:bg-blue-600 disabled:bg-blue-400 dark:disabled:bg-blue-600/50; }
</style> 