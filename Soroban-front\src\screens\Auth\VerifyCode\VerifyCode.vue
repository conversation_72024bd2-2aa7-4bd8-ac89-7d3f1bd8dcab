<template>
  <div class="w-full min-h-screen [background:linear-gradient(180deg,rgba(239,246,255,0.8)_0%,rgba(219,234,254,0.8)_100%)] flex items-center justify-center p-4 sm:p-8">
    <div class="flex flex-col w-full max-w-md items-center gap-10">
      <!-- Logo and title -->
      <div class="flex flex-col w-72 items-center justify-center gap-10 relative">
        <div class="flex items-center justify-center w-full relative">
          <img
            class="relative w-[60px] h-[60px] object-cover"
            alt="Logo lerning"
            src="https://c.animaapp.com/mc793xiqDBufmA/img/logo-lerning-removebg-preview-1.png"
          />
          <div class="relative w-auto brand-text text-3xl tracking-tight leading-8">
            <span class="font-bold text-[#d08700]">{{ t('brand.main') }}</span>
            <span class="font-bold text-slate-800">&nbsp;{{ t('brand.secondary') }}</span>
          </div>
        </div>

        <!-- Header -->
        <div class="gap-2 flex flex-col items-center relative self-stretch w-full">
          <h2 class="relative self-stretch font-bold text-slate-800 text-3xl text-center leading-normal">
            {{ t('forgotPassword.verifyTitle') }}
          </h2>
          <p class="relative w-fit text-gray-500 text-lg tracking-wide whitespace-nowrap">
            {{ t('forgotPassword.verifySubtitle') }}
          </p>
        </div>
      </div>

      <!-- Form section -->
      <form @submit.prevent="handleVerifyCode" class="flex flex-col items-center justify-center gap-8 relative self-stretch w-full">
        
        <!-- Error message -->
        <div v-if="errorMessage" class="w-full p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg text-sm">
          {{ errorMessage }}
        </div>
        
        <div class="flex flex-col items-center gap-5 relative self-stretch w-full">
          <!-- Input fields -->
          <div class="relative w-full group">
             <input
              v-model="code"
              type="text"
              :placeholder="t('forgotPassword.code')"
              class="px-5 py-4 w-full h-auto rounded-xl border border-solid border-gray-300 text-base bg-white/60 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition text-center tracking-[0.5em]"
            />
          </div>
           <!-- Resend code link -->
          <div class="relative self-stretch text-sm text-center tracking-[0] leading-normal">
            <span class="text-slate-600">{{ t('forgotPassword.resend') }}</span>
            <button type="button" @click="resendCode" class="text-blue-600 font-semibold hover:underline">
                {{ t('forgotPassword.resendBtn') }}
            </button>
          </div>
        </div>

        <!-- Submit button -->
        <div class="flex flex-col sm:flex-row items-center gap-4 relative self-stretch w-full">
          <button
            type="submit"
            :disabled="isLoading"
            class="flex-1 w-full px-7 py-3 h-auto bg-blue-600 rounded-lg text-white text-lg font-bold transition-all duration-300 transform hover:scale-105 hover:bg-blue-700 shadow-lg hover:shadow-xl hover:shadow-blue-500/30 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span v-if="isLoading">جاري التحقق...</span>
            <span v-else>{{ t('forgotPassword.verifyCodeBtn') }}</span>
          </button>
        </div>
      </form>
       <!-- Back to login link -->
      <div class="relative self-stretch text-base text-center tracking-[0] leading-normal">
        <button @click="goToLogin" class="text-slate-600 font-semibold hover:underline">
            &larr; {{ t('forgotPassword.backToLogin') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { t } from '../../../locales'

const router = useRouter()
const route = useRoute()

const code = ref('')
const email = ref('')
const isLoading = ref(false)
const errorMessage = ref('')

onMounted(() => {
  // قراءة الإيميل من الرابط عند تحميل الصفحة
  email.value = (route.query.email as string) || '';
  if (!email.value) {
    // إذا لم يوجد إيميل، أعد المستخدم للصفحة السابقة
    router.push('/forgot-password');
  }
});

const handleVerifyCode = async () => {
  isLoading.value = true;
  errorMessage.value = '';
  try {
    const backendUrl = 'http://localhost:8000';
    const response = await fetch(`${backendUrl}/api/verify-code`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', 'Accept': 'application/json' },
      body: JSON.stringify({ email: email.value, code: code.value }),
    });
    const data = await response.json();
    if (response.ok) {
      router.push({ name: 'ResetPassword', query: { email: email.value, code: code.value } });
    } else {
      errorMessage.value = data.message || 'Verification failed.';
    }
  } catch (error) {
    errorMessage.value = 'Network error.';
  } finally {
    isLoading.value = false;
  }
}

const resendCode = async () => {
    // نفس منطق إرسال الكود في الصفحة السابقة
    console.log('Resending code...')
    // يمكنك هنا استدعاء نفس الـ API المستخدم في forgot-password
}

const goToLogin = () => {
  router.push('/login')
}
</script>

<style scoped>
.brand-text {
  font-family: 'Tajawal', Helvetica, sans-serif;
}
</style> 