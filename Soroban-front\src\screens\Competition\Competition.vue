<template>
  <div>
    <NavigationBarSection />
    <div class="container mx-auto pt-24 pb-10">
      <div class="flex gap-4 mb-8">
        <button
          :class="['px-6 py-2 rounded-t-lg font-semibold', activeTab === 'monthly' ? 'bg-yellow-100 text-yellow-700' : 'bg-gray-100 text-gray-500']"
          @click="activeTab = 'monthly'"
        >
          Monthly
        </button>
        <button
          :class="['px-6 py-2 rounded-t-lg font-semibold', activeTab === 'seasonal' ? 'bg-yellow-100 text-yellow-700' : 'bg-gray-100 text-gray-500']"
          @click="activeTab = 'seasonal'"
        >
          Seasonal
        </button>
      </div>
      <div v-if="activeTab === 'monthly'">
        <div class="flex flex-col gap-6">
          <div v-for="competition in competitions" :key="competition.id" class="flex items-center bg-white rounded-2xl border border-gray-200 shadow-sm p-6 gap-6">
            <img :src="competition.image" class="w-32 h-32 object-cover rounded-xl" />
            <div class="flex-1">
              <h3 class="font-bold text-xl mb-1">{{ competition.title }}</h3>
              <p class="text-gray-600 mb-2">{{ competition.description }}</p>
              <div class="flex items-center gap-4 mb-2">
                <span class="text-gray-500 text-sm flex items-center gap-1"><svg class="w-4 h-4 inline" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/></svg> {{ competition.date }}</span>
                <span class="text-yellow-700 font-bold flex items-center gap-1"><svg class="w-4 h-4 inline text-yellow-500" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.286 3.967a1 1 0 00.95.69h4.178c.969 0 1.371 1.24.588 1.81l-3.385 2.46a1 1 0 00-.364 1.118l1.287 3.966c.3.922-.755 1.688-1.54 1.118l-3.385-2.46a1 1 0 00-1.175 0l-3.385 2.46c-.784.57-1.838-.196-1.54-1.118l1.287-3.966a1 1 0 00-.364-1.118l-3.385-2.46c-.783-.57-.38-1.81.588-1.81h4.178a1 1 0 00.95-.69l1.286-3.967z"/></svg> Badge: {{ competition.badge }}</span>
              </div>
            </div>
            <router-link :to="`/competition/${competition.id}`" class="ml-auto">
              <button class="px-8 py-3 rounded-lg font-bold text-white bg-yellow-400 hover:bg-yellow-500 transition-colors text-lg">Join Now</button>
            </router-link>
          </div>
        </div>
      </div>
      <div v-else>
        <div class="flex flex-col gap-6">
          <div v-for="competition in seasonalCompetitions" :key="competition.id" class="flex items-center bg-white rounded-2xl border border-gray-200 shadow-sm p-6 gap-6">
            <img :src="competition.image" class="w-32 h-32 object-cover rounded-xl" />
            <div class="flex-1">
              <h3 class="font-bold text-xl mb-1">{{ competition.title }}</h3>
              <p class="text-gray-600 mb-2">{{ competition.description }}</p>
              <div class="flex items-center gap-4 mb-2">
                <span class="text-gray-500 text-sm flex items-center gap-1"><svg class="w-4 h-4 inline" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/></svg> {{ competition.date }}</span>
                <span class="text-yellow-700 font-bold flex items-center gap-1"><svg class="w-4 h-4 inline text-yellow-500" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.286 3.967a1 1 0 00.95.69h4.178c.969 0 1.371 1.24.588 1.81l-3.385 2.46a1 1 0 00-.364 1.118l1.287 3.966c.3.922-.755 1.688-1.54 1.118l-3.385-2.46a1 1 0 00-1.175 0l-3.385 2.46c-.784.57-1.838-.196-1.54-1.118l1.287-3.966a1 1 0 00-.364-1.118l-3.385-2.46c-.783-.57-.38-1.81.588-1.81h4.178a1 1 0 00.95-.69l1.286-3.967z"/></svg> Badge: {{ competition.badge }}</span>
              </div>
            </div>
            <router-link :to="`/competition/${competition.id}`" class="ml-auto">
              <button class="px-8 py-3 rounded-lg font-bold text-white bg-yellow-400 hover:bg-yellow-500 transition-colors text-lg">Join Now</button>
            </router-link>
          </div>
        </div>
        <div v-if="seasonalCompetitions.length === 0" class="text-center text-gray-400 py-20 text-xl font-semibold">No seasonal competitions yet.</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import NavigationBarSection from '../Home/sections/NavigationBarSection/NavigationBarSection.vue'
import { ref, computed } from 'vue'
import { competitions as allCompetitions } from '../../lib/competitions'

const activeTab = ref('monthly')
const competitions = computed(() =>
  allCompetitions.filter(c => c.type === 'Monthly')
)
const seasonalCompetitions = computed(() =>
  allCompetitions.filter(c => c.type === 'Seasonal')
)
</script> 