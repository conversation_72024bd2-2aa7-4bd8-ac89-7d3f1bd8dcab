<template>
  <div>
    <NavigationBarSection />
    <div class="container mx-auto pt-24 pb-10">
      <button @click="$router.back()" class="flex items-center text-gray-600 font-semibold mb-6">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path></svg>
        Back To Competitions
      </button>

      <div class="w-full max-w-4xl mx-auto">
        <img :src="competition.image" :alt="competition.title" class="w-full h-80 object-cover rounded-2xl mb-6 shadow-lg" />

        <div class="flex justify-between items-center mb-6">
          <h1 class="text-4xl font-bold text-gray-800">{{ competition.title }}</h1>
          <span class="text-gray-500 font-medium flex items-center gap-2">
            <svg class="w-5 h-5 inline" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg>
            {{ competition.date }}
          </span>
        </div>

        <div class="bg-yellow-50 border border-yellow-200 rounded-2xl p-8 mb-8">
          <h2 class="text-xl font-bold text-yellow-800 mb-3">Description</h2>
          <p class="text-gray-700 leading-relaxed">{{ competition.description }}</p>

          <h2 class="text-xl font-bold text-yellow-800 mt-6 mb-3">Conditions And Rules</h2>
          <ul class="list-disc list-inside text-gray-700 leading-relaxed space-y-1">
            <li>Nam consectetur dolor amet, sodales. In libero, ipsum nisl. eget libero.</li>
            <li>eu facilisis vitae massa elementum non. quis urna dui efficitur. ex elementum id.</li>
            <li>eu viverra Quisque fringilla hendrerit faucibus sapien nec sed.</li>
            <li>orci nibh dui. Cras nec amet, ullamcorper In elementum risus quis tortor.</li>
            <li>ac ex. In vel at eget odio vel turpis varius volutpat laoreet odio non nisl.</li>
          </ul>
        </div>

        <div class="text-center mb-10">
            <span class="text-2xl text-yellow-700 font-bold flex items-center justify-center gap-2">
                <svg class="w-8 h-8 inline text-yellow-500" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.286 3.967a1 1 0 00.95.69h4.178c.969 0 1.371 1.24.588 1.81l-3.385 2.46a1 1 0 00-.364 1.118l1.287 3.966c.3.922-.755 1.688-1.54 1.118l-3.385-2.46a1 1 0 00-1.175 0l-3.385 2.46c-.784.57-1.838-.196-1.54-1.118l1.287-3.966a1 1 0 00-.364-1.118l-3.385-2.46c-.783-.57-.38-1.81.588-1.81h4.178a1 1 0 00.95-.69l1.286-3.967z"></path></svg>
                Badge: {{ competition.badge }}
            </span>
        </div>

        <div class="text-center">
            <button class="px-20 py-4 rounded-full font-bold text-white bg-yellow-400 hover:bg-yellow-500 transition-colors text-xl shadow-lg">Join Now</button>
        </div>

      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import NavigationBarSection from '../Home/sections/NavigationBarSection/NavigationBarSection.vue'
import { useRoute } from 'vue-router'
import { competitions } from '../../lib/competitions'
import { computed } from 'vue';

const route = useRoute();
const competitionId = computed(() => parseInt(route.params.id as string, 10));

const competition = computed(() => {
  return competitions.find(c => c.id === competitionId.value) || competitions[0];
});
</script> 