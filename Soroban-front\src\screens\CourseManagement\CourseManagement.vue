<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-6">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">Course Management</h1>
            <p class="text-gray-600">Manage courses, levels, and video lessons</p>
          </div>
          <div class="flex gap-4">
            <button 
              @click="showCreateCourseModal = true"
              class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors flex items-center gap-2"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
              </svg>
              New Course
            </button>
            <router-link 
              :to="{ name: userRole === 'admin' ? 'admin-videos' : 'teacher-videos' }"
              class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors flex items-center gap-2"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
              </svg>
              Manage Videos
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Stats Cards -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
              <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Total Courses</p>
              <p class="text-2xl font-semibold text-gray-900">{{ stats.totalCourses }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
              <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"/>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Course Levels</p>
              <p class="text-2xl font-semibold text-gray-900">{{ stats.totalLevels }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
              <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Video Lessons</p>
              <p class="text-2xl font-semibold text-gray-900">{{ stats.totalLessons }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
              <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Active Students</p>
              <p class="text-2xl font-semibold text-gray-900">{{ stats.activeStudents }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Courses Grid -->
      <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
          <h2 class="text-xl font-semibold text-gray-900">Courses</h2>
        </div>
        
        <!-- Loading State -->
        <div v-if="isLoading" class="flex justify-center items-center py-12">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>

        <!-- Error State -->
        <div v-else-if="error" class="text-center py-12">
          <div class="text-red-500 text-lg mb-4">{{ error }}</div>
          <button @click="loadData" class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors">
            Try Again
          </button>
        </div>

        <!-- Courses List -->
        <div v-else class="divide-y divide-gray-200">
          <div v-for="course in courses" :key="course.id" class="p-6 hover:bg-gray-50 transition-colors">
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ course.name }}</h3>
                <p class="text-gray-600 mb-4">{{ course.description }}</p>
                
                <!-- Course Levels -->
                <div class="flex flex-wrap gap-2 mb-4">
                  <span 
                    v-for="level in course.levels" 
                    :key="level.id"
                    class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium"
                  >
                    {{ level.title }} ({{ getLessonCount(level.id) }} lessons)
                  </span>
                </div>

                <!-- Course Stats -->
                <div class="flex items-center gap-6 text-sm text-gray-500">
                  <span>{{ course.levels?.length || 0 }} levels</span>
                  <span>{{ getTotalLessonsForCourse(course.id) }} lessons</span>
                  <span>Created {{ formatDate(course.created_at) }}</span>
                </div>
              </div>

              <!-- Actions -->
              <div class="flex items-center gap-2 ml-6">
                <router-link 
                  :to="{ name: 'video-course', params: { courseId: course.id } }"
                  class="bg-green-500 text-white px-3 py-2 rounded-lg hover:bg-green-600 transition-colors text-sm"
                >
                  View Course
                </router-link>
                <button 
                  @click="editCourse(course)"
                  class="bg-blue-500 text-white px-3 py-2 rounded-lg hover:bg-blue-600 transition-colors text-sm"
                >
                  Edit
                </button>
                <button 
                  @click="deleteCourse(course)"
                  class="bg-red-500 text-white px-3 py-2 rounded-lg hover:bg-red-600 transition-colors text-sm"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div v-if="courses.length === 0" class="text-center py-12">
            <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
            </svg>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Courses Found</h3>
            <p class="text-gray-600 mb-4">Get started by creating your first course.</p>
            <button 
              @click="showCreateCourseModal = true"
              class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors"
            >
              Create Course
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Create Course Modal -->
    <CreateCourseModal
      :isOpen="showCreateCourseModal"
      @close="showCreateCourseModal = false"
      @save="handleCourseCreated"
    />

    <!-- Edit Course Modal -->
    <EditCourseModal
      :isOpen="showEditCourseModal"
      :course="selectedCourse"
      @close="showEditCourseModal = false"
      @save="handleCourseUpdated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useCourses } from '../../composables/useCourses'
import { useVideos } from '../../composables/useVideos'
import { useAuth } from '../../composables/useAuth'
import CreateCourseModal from '../../components/CreateCourseModal.vue'
import EditCourseModal from '../../components/EditCourseModal.vue'

const router = useRouter()

// Composables
const { courses, isLoading, error, fetchCourses, deleteCourse: removeCourse } = useCourses()
const { videos } = useVideos()
const { user } = useAuth()

// State
const showCreateCourseModal = ref(false)
const showEditCourseModal = ref(false)
const selectedCourse = ref(null)

const stats = ref({
  totalCourses: 0,
  totalLevels: 0,
  totalLessons: 0,
  activeStudents: 0
})

// Computed
const userRole = computed(() => user.value?.role || 'student')

// Methods
const loadData = async () => {
  await fetchCourses()
  updateStats()
}

const updateStats = () => {
  stats.value.totalCourses = courses.value.length
  stats.value.totalLevels = courses.value.reduce((total, course) => total + (course.levels?.length || 0), 0)
  stats.value.totalLessons = videos.value.length
  stats.value.activeStudents = 150 // This would come from API
}

const getLessonCount = (levelId: number) => {
  return videos.value.filter(video => video.course_level_id === levelId).length
}

const getTotalLessonsForCourse = (courseId: number) => {
  return videos.value.filter(video => 
    video.course_level?.course_id === courseId
  ).length
}

const editCourse = (course: any) => {
  selectedCourse.value = course
  showEditCourseModal.value = true
}

const deleteCourse = async (course: any) => {
  if (confirm(`Are you sure you want to delete "${course.name}"? This action cannot be undone.`)) {
    await removeCourse(course.id)
    loadData()
  }
}

const handleCourseCreated = () => {
  showCreateCourseModal.value = false
  loadData()
}

const handleCourseUpdated = () => {
  showEditCourseModal.value = false
  selectedCourse.value = null
  loadData()
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric' 
  })
}

// Initialize
onMounted(() => {
  loadData()
})
</script>
