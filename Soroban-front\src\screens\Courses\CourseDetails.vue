<template>
  <div>
    <NavigationBarSection />
    <div class="container mx-auto pt-24 pb-10">
      <div class="flex flex-col lg:flex-row gap-8">
        <!-- Main Content -->
        <div class="flex-1">
          <div class="flex items-center gap-2 mb-4">
            <span @click="$router.back()" class="cursor-pointer text-2xl">&lt;</span>
            <h2 class="text-2xl font-bold text-blue-900">Level {{ course.level }}</h2>
          </div>
          <img :src="course.image" :alt="course.title" class="rounded-2xl w-full max-h-96 object-cover mb-6" />
          <h1 class="text-2xl font-bold mb-2 flex items-center gap-2">{{ course.title }} <span class="text-yellow-500 text-base">★</span> <span class="text-base font-semibold text-gray-600">({{ course.rating }})</span></h1>
          <div class="bg-gray-50 rounded-xl p-6 mb-8">
            <h3 class="font-bold text-lg mb-2">Overview</h3>
            <p class="text-gray-700 text-sm leading-relaxed">{{ course.overview }}</p>
          </div>
          <h3 class="font-bold text-xl mb-4">Lessons</h3>
          <div class="bg-gray-50 rounded-xl p-4">
            <div v-for="lesson in course.lessons" :key="lesson.id" class="mb-2">
              <router-link
                v-if="lesson.open && isEnrolled"
                :to="`/courses/${course.id}/lesson/${lesson.id}`"
                class="block"
              >
                <button
                  class="w-full flex items-center justify-between px-4 py-3 rounded-lg font-bold text-left text-lg border border-gray-200 bg-white text-blue-900"
                >
                  <span><span class="mr-2">▶️</span>{{ lesson.title }}</span>
                  <span class="text-gray-400">▼</span>
                </button>
              </router-link>
              <button
                v-else
                class="w-full flex items-center justify-between px-4 py-3 rounded-lg font-bold text-left text-lg border border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed"
                disabled
              >
                <span><span class="mr-2">🔒</span>{{ lesson.title }}</span>
                <span class="text-gray-400">▼</span>
              </button>
            </div>
          </div>
        </div>
        <!-- Sidebar -->
        <div class="w-full lg:w-80 flex-shrink-0">
          <div class="bg-white rounded-2xl shadow p-6 mb-6">
            <div v-if="isEnrolled" class="bg-green-100 text-green-800 rounded-lg px-4 py-3 mb-4 text-center font-bold">
              You're enrolled
              <div class="text-xs font-normal text-green-700">You are currently enrolled in this course</div>
            </div>
            <div v-else class="flex items-center justify-between mb-4">
              <span class="font-bold text-gray-500">Level Price</span>
              <span class="font-bold text-blue-900">${{ course.price }}</span>
            </div>
            <button v-if="!isEnrolled" @click="enroll" class="w-full py-2.5 px-6 rounded-lg font-bold text-red-500 border border-red-400 bg-white hover:bg-red-50 transition-colors mb-4">Enroll</button>
            <div class="flex justify-between text-gray-600 text-sm mb-2">
              <span>Lessons</span>
              <span>{{ course.lessons.length }}</span>
            </div>
            <div class="flex justify-between text-gray-600 text-sm mb-4">
              <span>Duration</span>
              <span>{{ course.duration }}</span>
            </div>
            <div class="flex items-center gap-3 bg-gray-50 rounded-xl p-3">
              <img :src="course.teacher.image" class="w-12 h-12 rounded-full object-cover" />
              <div>
                <div class="font-bold text-gray-900">{{ course.teacher.name }}</div>
                <div class="text-xs text-gray-500">{{ course.teacher.bio }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import NavigationBarSection from '../Home/sections/NavigationBarSection/NavigationBarSection.vue'
import { useRoute } from 'vue-router'
import { ref } from 'vue'

const route = useRoute()
const isEnrolled = ref(false) // يبدأ غير مسجل
function enroll() {
  isEnrolled.value = true
}
// بيانات تجريبية حسب id الكورس
const courseId = route.params.id || 1
const course = {
  id: courseId,
  title: 'Skill Builder',
  level: 1,
  rating: 4.5,
  price: 15.0,
  image: 'https://images.pexels.com/photos/4144222/pexels-photo-4144222.jpeg',
  overview: `ultrices volutpat ullamcorper tincidunt ullamcorper ipsum eget elit. eget Ut tincidunt viverra nec ac leo. varius tincidunt adipiscing tincidunt cursus Ut non
Donec est. tincidunt tincidunt id placerat tincidunt viverra vel scelerisque Nam vitae vitae ex. nulla, adipiscing vitae odio nisl. Vestibulum tincidunt Cras leo. Sed tempor ullamcorper Donec efficitur. vel lacus quam turpis ac elementum tincidunt turpis venenatis eu non. viverra ex viverra Sed quam gravida quam ex
ultrices volutpat ullamcorper tincidunt ullamcorper ipsum eget elit. eget Ut tincidunt viverra nec ac leo. varius tincidunt adipiscing tincidunt cursus Ut non
Donec est. tincidunt tincidunt id placerat tincidunt viverra vel scelerisque Nam vitae vitae ex. nulla, adipiscing vitae odio nisl. Vestibulum tincidunt Cras leo. Sed tempor ullamcorper Donec efficitur. vel lacus quam turpis ac elementum tincidunt turpis venenatis eu non. viverra ex viverra Sed quam gravida quam ex`,
  lessons: [
    { id: 1, title: 'Lesson 1', open: true },
    { id: 2, title: 'Lesson 2', open: false },
    { id: 3, title: 'Lesson 3', open: false },
    { id: 4, title: 'Lesson 4', open: false },
    { id: 5, title: 'Lesson 5', open: false },
    { id: 6, title: 'Lesson 6', open: false },
    { id: 7, title: 'Lesson 7', open: false },
    { id: 8, title: 'Lesson 8', open: false },
  ],
  duration: '14 hours 45 min',
  teacher: {
    name: 'Hassan Rady',
    image: 'https://randomuser.me/api/portraits/men/32.jpg',
    bio: 'ac duis eu dui vitae placerat dui urna. ipsum tincidunt non sollicitudin, faucibus est euismod. cursus eu nisi eu nisl gravida elit estus Quisque sed eget est. dui nulla, tincidunt venenatis est. tincidunt ut Sed Sed',
  },
}
</script> 