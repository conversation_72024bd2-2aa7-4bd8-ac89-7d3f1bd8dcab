<template>
  <div class="min-h-screen bg-white">
    <!-- Navigation -->
    <NavigationBarSection />
    
    <!-- Main Content -->
    <div class="container mx-auto pt-24 pb-10 px-4">
      <!-- Header Section -->
      <div class="text-center mb-8">
        <div class="flex justify-center items-center gap-4 mb-6">
          <button 
            @click="activeTab = 'student'" 
            :class="activeTab === 'student' ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'"
            class="px-6 py-3 rounded-full font-medium transition-all"
          >
            For Student
          </button>
          <button 
            @click="activeTab = 'teacher'" 
            :class="activeTab === 'teacher' ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'"
            class="px-6 py-3 rounded-full font-medium transition-all"
          >
            To Be A Teacher
          </button>
        </div>
      </div>

      <!-- Student Courses Grid -->
      <div v-if="activeTab === 'student'">
        <!-- Loading State -->
        <div v-if="isLoading" class="flex justify-center items-center py-12">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>

        <!-- Error State -->
        <div v-else-if="error" class="text-center py-12">
          <div class="text-red-500 text-lg mb-4">{{ error }}</div>
          <button @click="fetchCourseLevels()" class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors">
            Try Again
          </button>
        </div>

        <!-- Courses Grid -->
        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div v-for="courseLevel in studentCourses" :key="courseLevel.id" class="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100">
            <div class="relative">
              <img
                :src="courseLevel.course?.image || 'https://images.pexels.com/photos/3184307/pexels-photo-3184307.jpeg'"
                :alt="courseLevel.title"
                class="w-full h-48 object-cover"
              />
              <!-- Play Button -->
              <div class="absolute inset-0 flex items-center justify-center">
                <button @click="enrollCourse(courseLevel.id)" class="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center hover:bg-opacity-100 transition-all shadow-lg">
                  <svg width="24" height="24" fill="#3B82F6" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                </button>
              </div>
              <!-- Level Badge -->
              <div class="absolute top-4 left-4">
                <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium">Level {{ courseLevel.level_number }}</span>
              </div>
              <!-- Status Badge -->
              <div class="absolute top-4 right-4">
                <span :class="courseLevel.status === 'active' ? 'bg-green-500' : 'bg-gray-500'" class="text-white px-3 py-1 rounded-full text-sm font-medium">
                  {{ courseLevel.status === 'active' ? 'Available' : 'Unavailable' }}
                </span>
              </div>
            </div>

            <div class="p-6">
              <div class="flex items-center gap-2 mb-2">
                <h3 class="text-xl font-bold text-gray-900">{{ courseLevel.title }}</h3>
                <div class="flex items-center gap-1">
                  <svg width="16" height="16" fill="#FCD34D" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                  <span class="text-sm text-gray-600">(4.5)</span>
                </div>
              </div>

              <p class="text-gray-600 text-sm mb-4">
                {{ courseLevel.description }}
              </p>

              <div class="flex items-center justify-between mb-4">
                <span class="text-2xl font-bold text-gray-900">${{ courseLevel.price }}</span>
                <span class="text-sm text-gray-500">{{ courseLevel.duration }} hours</span>
              </div>

              <button
                @click="openPaymentModal(courseLevel)"
                :disabled="courseLevel.status !== 'active'"
                :class="courseLevel.status === 'active' ? 'bg-blue-500 hover:bg-blue-600 text-white' : 'bg-gray-200 text-gray-500 cursor-not-allowed'"
                class="w-full py-3 px-6 rounded-lg font-medium transition-colors"
              >
                {{ courseLevel.status === 'active' ? 'Enroll Now' : 'Unavailable' }}
              </button>
            </div>
          </div>

          <!-- No courses message -->
          <div v-if="studentCourses.length === 0" class="col-span-full text-center py-12">
            <div class="text-gray-500 text-lg">No courses available at the moment.</div>
          </div>
        </div>
      </div>

      <!-- Teacher Training Courses Grid -->
      <div v-if="activeTab === 'teacher'" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Teacher Course Card 1 -->
        <div class="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100">
          <div class="relative">
            <img 
              src="https://images.pexels.com/photos/3184405/pexels-photo-3184405.jpeg" 
              alt="Teaching Fundamentals" 
              class="w-full h-48 object-cover"
            />
            <!-- Play Button -->
            <div class="absolute inset-0 flex items-center justify-center">
              <button class="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center hover:bg-opacity-100 transition-all shadow-lg">
                <svg width="24" height="24" fill="#3B82F6" viewBox="0 0 24 24">
                  <path d="M8 5v14l11-7z"/>
                </svg>
              </button>
            </div>
            <!-- Level Badge -->
            <div class="absolute top-4 left-4">
              <span class="bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-medium">Teacher Level 1</span>
            </div>
          </div>
          
          <div class="p-6">
            <div class="flex items-center gap-2 mb-2">
              <h3 class="text-xl font-bold text-gray-900">Teaching Fundamentals</h3>
              <div class="flex items-center gap-1">
                <svg width="16" height="16" fill="#FCD34D" viewBox="0 0 24 24">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
                <span class="text-sm text-gray-600">(4.8)</span>
              </div>
            </div>
            
            <p class="text-gray-600 text-sm mb-4">
              Learn the basics of Soroban teaching methodology, classroom management, and how to engage young learners effectively.
            </p>
            
            <button @click="enrollCourse(101)" class="w-full py-3 px-6 bg-white border-2 border-purple-500 text-purple-500 rounded-lg font-medium hover:bg-purple-50 transition-all">
              Enroll
            </button>
          </div>
        </div>

        <!-- Teacher Course Card 2 -->
        <div class="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100">
          <div class="relative">
            <img 
              src="https://images.pexels.com/photos/3184296/pexels-photo-3184296.jpeg" 
              alt="Advanced Pedagogy" 
              class="w-full h-48 object-cover"
            />
            <!-- Play Button -->
            <div class="absolute inset-0 flex items-center justify-center">
              <button class="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center hover:bg-opacity-100 transition-all shadow-lg">
                <svg width="24" height="24" fill="#3B82F6" viewBox="0 0 24 24">
                  <path d="M8 5v14l11-7z"/>
                </svg>
              </button>
            </div>
            <!-- Level Badge -->
            <div class="absolute top-4 left-4">
              <span class="bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-medium">Teacher Level 2</span>
            </div>
            <!-- Locked Badge -->
            <div class="absolute top-4 right-4">
              <span class="bg-gray-500 text-white px-3 py-1 rounded-full text-sm font-medium">Locked</span>
            </div>
          </div>
          
          <div class="p-6">
            <div class="flex items-center gap-2 mb-2">
              <h3 class="text-xl font-bold text-gray-900">Advanced Pedagogy</h3>
              <div class="flex items-center gap-1">
                <svg width="16" height="16" fill="#FCD34D" viewBox="0 0 24 24">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
                <span class="text-sm text-gray-600">(4.7)</span>
              </div>
            </div>
            
            <p class="text-gray-600 text-sm mb-4">
              Master advanced teaching techniques, curriculum development, and assessment methods for Soroban education.
            </p>
            
            <button class="w-full py-3 px-6 bg-gray-200 text-gray-500 rounded-lg font-medium cursor-not-allowed" disabled>
              Start
            </button>
          </div>
        </div>

        <!-- Teacher Course Card 3 -->
        <div class="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100">
          <div class="relative">
            <img 
              src="https://images.pexels.com/photos/3184418/pexels-photo-3184418.jpeg" 
              alt="Master Instructor" 
              class="w-full h-48 object-cover"
            />
            <!-- Play Button -->
            <div class="absolute inset-0 flex items-center justify-center">
              <button class="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center hover:bg-opacity-100 transition-all shadow-lg">
                <svg width="24" height="24" fill="#3B82F6" viewBox="0 0 24 24">
                  <path d="M8 5v14l11-7z"/>
                </svg>
              </button>
            </div>
            <!-- Level Badge -->
            <div class="absolute top-4 left-4">
              <span class="bg-yellow-500 text-white px-3 py-1 rounded-full text-sm font-medium">Teacher Level 3</span>
            </div>
            <!-- Locked Badge -->
            <div class="absolute top-4 right-4">
              <span class="bg-gray-500 text-white px-3 py-1 rounded-full text-sm font-medium">Locked</span>
            </div>
          </div>
          
          <div class="p-6">
            <div class="flex items-center gap-2 mb-2">
              <h3 class="text-xl font-bold text-gray-900">Master Instructor</h3>
              <div class="flex items-center gap-1">
                <svg width="16" height="16" fill="#FCD34D" viewBox="0 0 24 24">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
                <span class="text-sm text-gray-600">(4.9)</span>
              </div>
            </div>
            
            <p class="text-gray-600 text-sm mb-4">
              Become a certified master instructor with skills to train other teachers and lead educational programs.
            </p>
            
            <button class="w-full py-3 px-6 bg-gray-200 text-gray-500 rounded-lg font-medium cursor-not-allowed" disabled>
              Start
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Payment Modal -->
    <PaymentModal
      :isOpen="showPaymentModal"
      :courseLevel="selectedCourseLevel"
      :subscription="pendingSubscription"
      @close="closePaymentModal"
      @success="handlePaymentSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import NavigationBarSection from '../Home/sections/NavigationBarSection/NavigationBarSection.vue'
import PaymentModal from '../../components/PaymentModal.vue'
import { useCourses } from '../../composables/useCourses'
import { useAuth } from '../../composables/useAuth'
import { apiService } from '../../services/api'

const activeTab = ref('student')
const router = useRouter()

// Payment modal state
const showPaymentModal = ref(false)
const selectedCourseLevel = ref(null)
const pendingSubscription = ref(null)

// Use composables
const { courses, courseLevels, isLoading, error, fetchCourses, fetchCourseLevels } = useCourses()
const { isAuthenticated, user, getDashboardRoute } = useAuth()

// Filter courses by target audience
const studentCourses = computed(() =>
  courseLevels.value.filter(level => level.course?.status === 'active')
)

const teacherCourses = computed(() =>
  courses.value.filter(course => course.status === 'active')
)

const openPaymentModal = async (courseLevel: any) => {
  if (!isAuthenticated.value) {
    router.push('/login')
    return
  }

  try {
    // Create a pending subscription first
    const subscriptionData = {
      student_id: user.value.id,
      level_id: courseLevel.id,
      amount: courseLevel.price,
      start_date: new Date().toISOString().split('T')[0],
      end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
      renewal_status: 'pending_renewal'
    }

    const response = await apiService.subscribe(subscriptionData)
    if (response.success && response.data) {
      selectedCourseLevel.value = courseLevel
      pendingSubscription.value = response.data
      showPaymentModal.value = true
    }
  } catch (error) {
    console.error('Failed to create subscription:', error)
    alert('Failed to start enrollment process. Please try again.')
  }
}

const closePaymentModal = () => {
  showPaymentModal.value = false
  selectedCourseLevel.value = null
  pendingSubscription.value = null
}

const handlePaymentSuccess = (payment: any) => {
  console.log('Payment submitted successfully:', payment)
  alert('Payment submitted successfully! Your enrollment is pending confirmation.')

  // Optionally redirect to dashboard
  if (user.value) {
    switch (user.value.role) {
      case 'student':
        router.push('/student-dashboard')
        break
      default:
        router.push('/')
    }
  }
}

const enrollCourse = async (courseLevelId: number) => {
  if (!isAuthenticated.value) {
    router.push('/login')
    return
  }

  // Find the course level
  const courseLevel = courseLevels.value.find(level => level.id === courseLevelId)
  if (!courseLevel) {
    alert('Course not found')
    return
  }

  // Check if user already has access to this course
  try {
    const response = await apiService.checkCourseAccess(courseLevelId)
    if (response.success && response.data?.has_access) {
      // User already has access, redirect to course videos
      router.push(`/courses/${courseLevel.course_id}/videos`)
      return
    }
  } catch (error) {
    console.error('Failed to check course access:', error)
  }

  // User needs to pay, open payment modal
  openPaymentModal(courseLevel)
}

const startCourse = (courseId: number) => {
  if (!isAuthenticated.value) {
    router.push('/login')
    return
  }

  router.push(`/courses/${courseId}/videos`)
}

// Initialize data
onMounted(async () => {
  await Promise.all([
    fetchCourses(),
    fetchCourseLevels()
  ])
})
</script>
