<template>
  <div class="flex min-h-screen bg-gray-50">
    <!-- Sidebar -->
    <aside class="w-64 bg-blue-50 p-6 flex flex-col gap-6">
      <button @click="$router.push('/courses')" class="mb-4 px-4 py-2 rounded-lg bg-white text-blue-700 font-bold border border-blue-200 hover:bg-blue-100 transition">&lt; Back To Courses</button>
      <div>
        <div class="font-bold text-lg mb-2">Videos <span class="ml-2">▼</span></div>
        <ul class="flex flex-col gap-1">
          <li v-for="lesson in lessons" :key="lesson.id">
            <button
              @click="goToLesson(lesson.id)"
              :class="[ 'w-full text-left px-4 py-2 rounded-lg font-semibold transition', lesson.id === currentLesson.id ? 'bg-white text-blue-700 ring-2 ring-blue-400' : 'bg-blue-100 text-blue-900 hover:bg-white' ]"
            >
              {{ lesson.title }}
            </button>
          </li>
        </ul>
      </div>
    </aside>
    <!-- Main Content -->
    <main class="flex-1 flex flex-col items-center justify-start py-12 px-8">
      <div class="w-full max-w-3xl">
        <h2 class="text-2xl font-bold text-blue-900 mb-1">Level 1</h2>
        <div class="text-lg font-semibold text-gray-700 mb-6">{{ currentLesson.title }}</div>
        <div class="rounded-2xl overflow-hidden mb-8 relative">
          <video
            ref="videoRef"
            :src="currentLesson.video"
            class="w-full object-cover max-h-96 bg-black"
            controls
            poster="https://images.pexels.com/photos/4144222/pexels-photo-4144222.jpeg"
          ></video>
        </div>
        <div class="flex justify-between mb-8">
          <button @click="prevLesson" :disabled="currentLessonIndex === 0" class="px-6 py-2 rounded-lg border border-blue-400 text-blue-600 font-bold bg-white hover:bg-blue-50 disabled:opacity-50">Previous</button>
          <button @click="nextLesson" :disabled="currentLessonIndex === lessons.length - 1" class="px-6 py-2 rounded-lg border border-blue-400 text-blue-600 font-bold bg-white hover:bg-blue-50 disabled:opacity-50">Next</button>
        </div>
        <div class="space-y-3">
          <div class="flex items-center bg-blue-100 rounded-lg px-4 py-3 font-semibold text-blue-900 cursor-pointer select-none" @click="download('pdf')">
            <span class="mr-2">📄</span> PDF
            <span class="ml-auto text-blue-400 cursor-pointer">
              <span v-if="loadingType === 'pdf' && isLoading" class="animate-spin inline-block w-5 h-5 border-2 border-blue-400 border-t-transparent rounded-full"></span>
              <span v-else>⤓</span>
            </span>
          </div>
          <div class="flex items-center bg-blue-100 rounded-lg px-4 py-3 font-semibold text-blue-900 cursor-pointer select-none" @click="download('quiz')">
            <span class="mr-2">❓</span> Quiz
            <span class="ml-auto text-blue-400 cursor-pointer">
              <span v-if="loadingType === 'quiz' && isLoading" class="animate-spin inline-block w-5 h-5 border-2 border-blue-400 border-t-transparent rounded-full"></span>
              <span v-else>⤓</span>
            </span>
          </div>
          <div v-if="downloadSuccess" class="mt-2 text-green-600 font-bold">Download completed successfully!</div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const router = useRouter()
const route = useRoute()

const lessons = [
  { id: 1, title: 'Lesson 1', image: 'https://images.pexels.com/photos/4144222/pexels-photo-4144222.jpeg', video: 'https://www.w3schools.com/html/mov_bbb.mp4' },
  { id: 2, title: 'Lesson 2', image: 'https://images.pexels.com/photos/4145197/pexels-photo-4145197.jpeg', video: 'https://www.w3schools.com/html/movie.mp4' },
  { id: 3, title: 'Lesson 3', image: 'https://images.pexels.com/photos/1101730/pexels-photo-1101730.jpeg', video: 'https://www.w3schools.com/html/mov_bbb.mp4' },
  { id: 4, title: 'Lesson 4', image: 'https://images.pexels.com/photos/256401/pexels-photo-256401.jpeg', video: 'https://www.w3schools.com/html/movie.mp4' },
  { id: 5, title: 'Lesson 5', image: 'https://images.pexels.com/photos/1181671/pexels-photo-1181671.jpeg', video: 'https://www.w3schools.com/html/mov_bbb.mp4' },
  { id: 6, title: 'Lesson 6', image: 'https://images.pexels.com/photos/3184307/pexels-photo-3184307.jpeg', video: 'https://www.w3schools.com/html/movie.mp4' },
  { id: 7, title: 'Lesson 7', image: 'https://images.pexels.com/photos/4145197/pexels-photo-4145197.jpeg', video: 'https://www.w3schools.com/html/mov_bbb.mp4' },
]

const currentLessonId = ref(Number(route.params.lessonId) || 1)
const currentLessonIndex = computed(() => lessons.findIndex(l => l.id === currentLessonId.value))
const currentLesson = computed(() => lessons[currentLessonIndex.value] || lessons[0])

function goToLesson(id) {
  currentLessonId.value = id
  router.replace({ params: { ...route.params, lessonId: id } })
}
function prevLesson() {
  if (currentLessonIndex.value > 0) goToLesson(lessons[currentLessonIndex.value - 1].id)
}
function nextLesson() {
  if (currentLessonIndex.value < lessons.length - 1) goToLesson(lessons[currentLessonIndex.value + 1].id)
}

const isLoading = ref(false)
const loadingType = ref('')
const downloadSuccess = ref(false)

function download(type) {
  if (isLoading.value) return
  isLoading.value = true
  loadingType.value = type
  downloadSuccess.value = false
  setTimeout(() => {
    isLoading.value = false
    loadingType.value = ''
    downloadSuccess.value = true
    setTimeout(() => { downloadSuccess.value = false }, 2000)
  }, 1500)
}
</script> 