<template>
  <div
    class="bg-white dark:bg-gray-900 flex flex-col min-h-screen w-full transition-colors"
    data-model-id="107:348"
  >
    <NavigationBarSection />
    <div class="bg-white dark:bg-gray-900 w-full flex flex-col flex-1 px-4 sm:px-6 lg:px-8 pt-16 sm:pt-20 transition-colors">
      <div id="hero">
        <HeroSection />
      </div>
      <div class="w-full max-w-[1400px] mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12">
        <VideoSection />
        <OurMissionSection />
        <HowItWorksSection />
        <div id="start-learning-as-young">
          <StartLearningAsYoungSection />
        </div>
        <StartLearningToBeATeacherSection />
        <BestTeachersSection />
        <BestStudentsSection />
        <div id="join-challenge">
          <JoinChallengeSection />
        </div>
        <TestimonialsSection />
        <PartnersSection />
      </div>
      <FooterSection />
    </div>
  </div>
</template>

<script setup lang="ts">
import FooterSection from "./sections/FooterSection/FooterSection.vue";
import HeroSection from "./sections/HeroSection/HeroSection.vue";
import NavigationBarSection from "./sections/NavigationBarSection/NavigationBarSection.vue";
import OurMissionSection from "./sections/OurMissionSection/OurMissionSection.vue";
import HowItWorksSection from "./sections/HowItWorksSection/HowItWorksSection.vue";
import StartLearningAsYoungSection from "./sections/StartLearningAsYoungSection/StartLearningAsYoungSection.vue";
import StartLearningToBeATeacherSection from "./sections/StartLearningToBeATeacherSection/StartLearningToBeATeacherSection.vue";
import BestTeachersSection from "./sections/BestTeachersSection/BestTeachersSection.vue";
import VideoSection from "./sections/VideoSection/VideoSection.vue";
import BestStudentsSection from "./sections/BestStudentsSection/BestStudentsSection.vue";
import JoinChallengeSection from "./sections/JoinChallengeSection/JoinChallengeSection.vue";
import TestimonialsSection from "./sections/TestimonialsSection/TestimonialsSection.vue";
import PartnersSection from "./sections/PartnersSection/PartnersSection.vue";
</script> 