<template>
  <section class="py-12 sm:py-16 lg:py-20 bg-white dark:bg-gray-900 transition-colors">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-8 sm:mb-12">
        <h2 class="text-3xl sm:text-4xl font-bold text-gray-900 dark:text-white mb-3 transition-colors">
          {{ t('bestStudents.title') }}
        </h2>
        <p class="text-base sm:text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto text-right rtl:text-right transition-colors">
          {{ t('bestStudents.subtitle') }}
        </p>
      </div>
      <div class="relative group/carousel">
        <div ref="studentScrollContainer" class="flex space-x-6 sm:space-x-8 overflow-x-auto pb-4 -mx-4 sm:-mx-6 lg:-mx-8 px-4 sm:px-6 lg:px-8 scroll-smooth rtl:space-x-reverse" style="scrollbar-width: none;">
          <div v-for="student in bestStudents" :key="student.name" class="flex-shrink-0 w-64 sm:w-72 snap-start">
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:-translate-y-2 hover:shadow-xl">
              <div class="relative">
                <img :src="student.image" :alt="`${student.name} - طالب في ${student.level} مع ${student.points} نقطة`" class="w-full h-60 object-cover" loading="lazy">
                <div class="absolute top-3 right-3 rtl:left-3 rtl:right-auto">
                  <Star class="text-yellow-400 w-7 h-7" fill="currentColor" />
                </div>
              </div>
              <div class="p-4">
                <h3 class="text-lg font-bold text-gray-900 dark:text-white truncate text-right rtl:text-right transition-colors">{{ student.name }}</h3>
                <div class="flex justify-between items-center mt-2">
                  <span class="bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 text-xs font-semibold px-3 py-1.5 rounded-lg transition-colors">{{ t('bestStudents.level') }}</span>
                  <div class="flex items-center space-x-1 text-gray-500 dark:text-gray-400 rtl:space-x-reverse transition-colors">
                    <Award class="w-5 h-5 text-yellow-500" />
                    <span class="text-xs font-medium">({{ student.points }} {{ t('bestStudents.points') }})</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
         <!-- Navigation Buttons -->
        <button @click="scrollStudents('left')" class="absolute top-1/2 -translate-y-1/2 left-2 sm:left-4 z-10 bg-white/80 dark:bg-gray-700/80 backdrop-blur-sm rounded-full p-2 shadow-lg hover:bg-white dark:hover:bg-gray-600 transition-opacity opacity-0 group-hover/carousel:opacity-100 disabled:opacity-0 disabled:cursor-not-allowed rtl:right-2 rtl:sm:right-4 rtl:left-auto">
          <ChevronLeft class="w-6 h-6 text-gray-800 dark:text-gray-200" />
        </button>
        <button @click="scrollStudents('right')" class="absolute top-1/2 -translate-y-1/2 right-2 sm:right-4 z-10 bg-white/80 dark:bg-gray-700/80 backdrop-blur-sm rounded-full p-2 shadow-lg hover:bg-white dark:hover:bg-gray-600 transition-opacity opacity-0 group-hover/carousel:opacity-100 disabled:opacity-0 disabled:cursor-not-allowed rtl:left-2 rtl:sm:left-4 rtl:right-auto">
          <ChevronRight class="w-6 h-6 text-gray-800 dark:text-gray-200" />
        </button>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Star, Award, ChevronLeft, ChevronRight } from 'lucide-vue-next'
import { t } from '../../../../locales'

// Best Students data
const bestStudents = ref([
  { name: 'Jaky Jan', level: 'Level 1', points: 1500, image: 'https://c.animaapp.com/mc5ppr8hKB91iA/img/component-2-2.png' },
  { name: 'Michael Brown', level: 'Level 1', points: 1500, image: 'https://c.animaapp.com/mc5ppr8hKB91iA/img/component-2-2.png' },
  { name: 'Emily Jones', level: 'Level 1', points: 1500, image: 'https://c.animaapp.com/mc5ppr8hKB91iA/img/component-2-2.png' },
  { name: 'David Miller', level: 'Level 1', points: 1500, image: 'https://c.animaapp.com/mc5ppr8hKB91iA/img/component-2-2.png' },
  { name: 'Sarah Wilson', level: 'Level 1', points: 1500, image: 'https://c.animaapp.com/mc5ppr8hKB91iA/img/component-2-2.png' },
  // Duplicated for scrolling test
  { name: 'Jaky Jan', level: 'Level 1', points: 1500, image: 'https://c.animaapp.com/mc5ppr8hKB91iA/img/component-2-2.png' },
  { name: 'Michael Brown', level: 'Level 1', points: 1500, image: 'https://c.animaapp.com/mc5ppr8hKB91iA/img/component-2-2.png' },
  { name: 'Emily Jones', level: 'Level 1', points: 1500, image: 'https://c.animaapp.com/mc5ppr8hKB91iA/img/component-2-2.png' },
  { name: 'David Miller', level: 'Level 1', points: 1500, image: 'https://c.animaapp.com/mc5ppr8hKB91iA/img/component-2-2.png' },
  ])

const studentScrollContainer = ref<HTMLElement | null>(null);
const scrollStudents = (direction: 'left' | 'right') => {
  if (studentScrollContainer.value) {
    const scrollAmount = direction === 'left' ? -300 : 300;
    studentScrollContainer.value.scrollBy({ left: scrollAmount, behavior: 'smooth' });
  }
};
</script> 