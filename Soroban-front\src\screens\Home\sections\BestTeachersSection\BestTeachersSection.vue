<template>
  <section class="py-12 sm:py-16 lg:py-20 bg-gray-50 dark:bg-gray-800 transition-colors">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-8 sm:mb-12">
        <h2 class="text-3xl sm:text-4xl font-bold text-gray-900 dark:text-white mb-3 transition-colors">
          {{ t('bestTeachers.title') }}
        </h2>
        <p class="text-base sm:text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto text-right rtl:text-right transition-colors">
          {{ t('bestTeachers.subtitle') }}
        </p>
      </div>
      <div class="relative group/carousel">
        <div ref="teacherScrollContainer" class="flex space-x-6 sm:space-x-8 overflow-x-auto pb-4 -mx-4 sm:-mx-6 lg:-mx-8 px-4 sm:px-6 lg:px-8 scroll-smooth rtl:space-x-reverse" style="scrollbar-width: none;">
          <div v-for="teacher in bestTeachers" :key="teacher.name" class="flex-shrink-0 w-64 sm:w-72 snap-start">
            <div class="bg-white dark:bg-gray-700 rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:-translate-y-2 hover:shadow-xl">
              <img :src="teacher.image" :alt="teacher.name" class="w-full h-60 object-cover">
              <div class="p-4 text-center">
                <h3 class="text-lg font-bold text-gray-900 dark:text-white truncate transition-colors">{{ teacher.name }}</h3>
                <p class="text-sm text-blue-600 dark:text-blue-400 font-medium transition-colors">{{ t(`bestTeachers.titles.${teacher.titleKey}`) }}</p>
              </div>
            </div>
          </div>
        </div>
        <!-- Navigation Buttons -->
        <button @click="scrollTeachers('left')" class="absolute top-1/2 -translate-y-1/2 left-2 sm:left-4 z-10 bg-white/80 dark:bg-gray-700/80 backdrop-blur-sm rounded-full p-2 shadow-lg hover:bg-white dark:hover:bg-gray-600 transition-opacity opacity-0 group-hover/carousel:opacity-100 disabled:opacity-0 disabled:cursor-not-allowed rtl:right-2 rtl:sm:right-4 rtl:left-auto">
          <ChevronLeft class="w-6 h-6 text-gray-800 dark:text-gray-200" />
        </button>
        <button @click="scrollTeachers('right')" class="absolute top-1/2 -translate-y-1/2 right-2 sm:right-4 z-10 bg-white/80 dark:bg-gray-700/80 backdrop-blur-sm rounded-full p-2 shadow-lg hover:bg-white dark:hover:bg-gray-600 transition-opacity opacity-0 group-hover/carousel:opacity-100 disabled:opacity-0 disabled:cursor-not-allowed rtl:left-2 rtl:sm:left-4 rtl:right-auto">
          <ChevronRight class="w-6 h-6 text-gray-800 dark:text-gray-200" />
        </button>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ChevronLeft, ChevronRight } from 'lucide-vue-next'
import { t } from '../../../../locales'

const bestTeachers = ref([
  {
    name: "John Doe",
    titleKey: "leadInstructor",
    image: "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=870&q=80",
  },
  {
    name: "Jane Smith",
    titleKey: "mathSpecialist",
    image: "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=871&q=80",
  },
  {
    name: "Sam Wilson",
    titleKey: "scienceTeacher",
    image: "https://c.animaapp.com/mc5ppr8hKB91iA/img/component-2.png",
  },
  {
    name: "Maria Garcia",
    titleKey: "artCreativity",
    image: "https://c.animaapp.com/mc5ppr8hKB91iA/img/component-2.png",
  },
]);

const teacherScrollContainer = ref<HTMLElement | null>(null);
const scrollTeachers = (direction: 'left' | 'right') => {
  if (teacherScrollContainer.value) {
    const scrollAmount = direction === 'left' ? -300 : 300;
    teacherScrollContainer.value.scrollBy({ left: scrollAmount, behavior: 'smooth' });
  }
};
</script> 