<template>
  <footer class="text-slate-800 dark:text-slate-200 pt-12 sm:pt-16 bg-slate-100 dark:bg-gray-800 transition-colors">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <!-- Logo and Brand -->
        <div class="md:col-span-1 mb-6 md:mb-0">
            <div class="flex flex-col items-start">
                <img class="h-10 w-auto mb-2" src="https://c.animaapp.com/mc5ppr8hKB91iA/img/logo-lerning-removebg-preview-1.png" alt="ELBARQ Logo - منصة التعلم التفاعلية" loading="lazy">
                <div class="brand-text text-lg">
                  <span class="font-bold text-[#d08700]">{{ t('brand.main') }}</span>
                  <span class="font-bold text-slate-800 dark:text-slate-200">&nbsp;{{ t('brand.secondary') }}</span>
                </div>
            </div>
        </div>

        <!-- Footer Links -->
        <div class="md:col-span-3 grid grid-cols-2 sm:grid-cols-3 gap-8">
          <div v-for="section in footerLinks" :key="section.title">
            <h3 class="font-bold text-slate-800 dark:text-white tracking-wider mb-4 text-right rtl:text-right transition-colors">{{ t(`footer.sections.${section.key}.title`) }}</h3>
            <ul class="space-y-3">
              <li v-for="link in section.links" :key="link.key">
                <a href="#" class="text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white transition-colors text-right rtl:text-right block">{{ t(`footer.sections.${section.key}.links.${link.key}`) }}</a>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Bottom Bar -->
      <div class="mt-12 sm:mt-16 pt-8 border-t border-slate-300 dark:border-gray-600 text-center transition-colors">
        <p class="text-sm text-slate-500 dark:text-slate-400 transition-colors">&copy; {{ new Date().getFullYear() }} {{ t('brand.main') }} {{ t('brand.secondary') }}. {{ t('footer.copyright') }}</p>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { t } from '../../../../locales'

const footerLinks = ref([
  {
    title: "Explore",
    key: "explore",
    links: [
      { key: "courseOverview" },
      { key: "levels" },
      { key: "challenges" },
      { key: "signIn" }
    ],
  },
  {
    title: "About Us",
    key: "aboutUs",
    links: [
      { key: "ourVision" },
      { key: "team" },
      { key: "contact" },
      { key: "careers" }
    ],
  },
  {
    title: "Explore Resources",
    key: "resources",
    links: [
      { key: "helpCenter" },
      { key: "blog" },
      { key: "placementTest" },
      { key: "faqs" }
    ],
  },
]);
</script> 