<template>
  <div class="w-full max-w-[1400px] h-auto py-6 sm:py-8 mx-auto bg-blue-50 dark:bg-gray-800 rounded-3xl sm:rounded-[48px] overflow-hidden shadow-lg mt-8 sm:mt-12 transition-colors">
    <div class="relative w-full max-w-[1300px] mx-auto px-4 sm:px-6 lg:px-8 flex flex-col lg:flex-row items-center justify-between gap-6 sm:gap-8">
      <div class="flex flex-col items-start gap-6 sm:gap-8 w-full lg:w-[60%] rtl:items-end">
        <div class="flex flex-col items-start gap-2 w-full rtl:items-end">
          <h1 class="text-[#162456] dark:text-white text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold leading-tight [font-family:'Tajawal',Helvetica] max-w-full text-right rtl:text-right transition-colors">
            {{ t('hero.title') }}
          </h1>

          <p class="text-[#99a1af] dark:text-gray-300 text-base sm:text-lg lg:text-xl [font-family:'Tajawal',Helvetica] font-normal text-right rtl:text-right transition-colors">
            {{ t('hero.subtitle') }}
          </p>
        </div>

        <button 
          @click="scrollToStartLearning"
          class="h-12 sm:h-14 px-6 sm:px-8 py-3 sm:py-4 bg-[#155dfc] hover:bg-[#1550e0] rounded-2xl sm:rounded-3xl text-white text-lg sm:text-xl [font-family:'Tajawal',Helvetica] font-bold transition-colors"
        >
          {{ t('hero.cta') }}
        </button>
      </div>

      <div class="w-full lg:w-[40%]">
        <img
          class="w-full max-w-md mx-auto lg:max-w-none lg:w-auto h-auto max-h-[400px] lg:max-h-[450px] object-contain"
          alt="Learning illustration"
          src="https://c.animaapp.com/mc5ppr8hKB91iA/img/learning-bro-1.png"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { t } from '../../../../locales'

const scrollToStartLearning = () => {
  const element = document.getElementById('start-learning-as-young');
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });
  }
}
</script> 