<template>
  <div class="mb-12 sm:mb-16">
    <h2 class="text-2xl sm:text-3xl lg:text-4xl font-bold text-center mb-12 sm:mb-16 text-gray-900 dark:text-white transition-colors">
      {{ t('howItWorks.title') }}
    </h2>
    <div class="relative max-w-2xl mx-auto">
      <!-- Vertical Line -->
      <div class="absolute left-4 lg:left-1/2 top-0 h-full w-0.5 bg-gray-200 dark:bg-gray-600 rtl:right-4 rtl:lg:right-1/2 rtl:left-auto transition-colors" aria-hidden="true"></div>

      <!-- Steps -->
      <div class="relative space-y-8">
        <div 
          v-for="(step, index) in howItWorksSteps" 
          :key="step.title" 
          class="timeline-step flex items-start"
          :ref="el => { if (el) stepRefs[index] = el }"
        >
          <!-- Checkpoint -->
          <div class="absolute left-4 lg:left-1/2 top-1 -translate-x-1/2 w-8 h-8 rounded-full border-2 flex items-center justify-center z-10 bg-white dark:bg-gray-800 rtl:right-4 rtl:lg:right-1/2 rtl:left-auto rtl:translate-x-1/2 transition-colors" :class="step.colors.border">
            <Check class="w-5 h-5" :class="step.colors.text" />
          </div>

          <!-- Card -->
          <div :class="['w-full ml-12 lg:ml-0 p-4 rounded-lg transition-all duration-300 ease-in-out hover:shadow-xl hover:-translate-y-1 rtl:mr-12 rtl:lg:mr-0', step.colors.bg, index % 2 === 0 ? 'lg:w-[calc(50%-2rem)] lg:mr-auto rtl:lg:ml-auto rtl:lg:mr-0' : 'lg:w-[calc(50%-2rem)] lg:ml-auto rtl:lg:mr-auto rtl:lg:ml-0']">
            <div class="flex items-center gap-3">
              <component :is="step.icon" :class="['w-6 h-6', step.colors.text]" />
              <h3 :class="['font-semibold text-right rtl:text-right', step.colors.text]">{{ t(`howItWorks.steps.${step.key}.title`) }}</h3>
            </div>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-300 text-right rtl:text-right transition-colors">{{ t(`howItWorks.steps.${step.key}.description`) }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUpdate } from 'vue'
import { UserPlus, ClipboardCheck, PlayCircle, Trophy, Check } from 'lucide-vue-next'
import { t } from '../../../../locales'

// --- Animation for "How It Work" section ---
const stepRefs = ref<any[]>([])

onBeforeUpdate(() => {
  stepRefs.value = []
})

onMounted(() => {
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add('is-visible')
          observer.unobserve(entry.target)
        }
      })
    },
    {
      threshold: 0.2,
    }
  )
  const elementsToObserve = [
    ...stepRefs.value,
  ]
  elementsToObserve.forEach((el) => {
    if (el) observer.observe(el)
  })
})
// -----------------------------------------

// How it works steps data
const howItWorksSteps = ref([
  {
    title: "Sign Up Or Explore As Guest",
    key: "signup",
    icon: UserPlus,
    colors: {
      bg: 'bg-green-50',
      text: 'text-green-600',
      border: 'border-green-600'
    }
  },
  {
    title: "Take The Placement Quiz",
    key: "quiz",
    icon: ClipboardCheck,
    colors: {
      bg: 'bg-red-50',
      text: 'text-red-600',
      border: 'border-red-600'
    }
  },
  {
    title: "Start Learning",
    key: "learn",
    icon: PlayCircle,
    colors: {
      bg: 'bg-blue-50',
      text: 'text-blue-600',
      border: 'border-blue-600'
    }
  },
  {
    title: "Join Challenges & Track Progress",
    key: "challenges",
    icon: Trophy,
    colors: {
      bg: 'bg-yellow-50',
      text: 'text-yellow-600',
      border: 'border-yellow-600'
    }
  },
])
</script>

<style scoped>
.timeline-step {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.5s ease-out, transform 0.5s ease-out;
}

.timeline-step.is-visible {
  opacity: 1;
  transform: translateY(0);
}
</style> 