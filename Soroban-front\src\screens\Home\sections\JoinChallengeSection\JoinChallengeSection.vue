<template>
  <section class="py-12 sm:py-16 lg:py-20 bg-amber-50 dark:bg-amber-900/20 transition-colors">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-8 sm:mb-12">
        <h2 class="text-3xl sm:text-4xl font-bold text-gray-900 dark:text-white mb-3 transition-colors">
          {{ t('joinChallenge.title') }}
        </h2>
        <p class="text-base sm:text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto text-right rtl:text-right transition-colors">
          {{ t('joinChallenge.subtitle') }}
        </p>
      </div>

      <!-- Filter Tabs -->
      <div class="flex justify-center space-x-2 sm:space-x-4 mb-8 rtl:space-x-reverse">
        <button
          v-for="tab in challengeTabs"
          :key="tab"
          @click="activeChallengeTab = tab"
          :class="[
            'px-4 py-2 rounded-full text-sm sm:text-base font-medium transition-colors',
            activeChallengeTab === tab ? 'bg-amber-400 dark:bg-amber-500 text-gray-800 dark:text-gray-900 shadow' : 'bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-amber-100 dark:hover:bg-amber-800/30'
          ]"
        >
          {{ t(`joinChallenge.tabs.${tab.toLowerCase()}`) }}
        </button>
      </div>

      <!-- Challenges Grid -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8">
        <div
          v-for="challenge in filteredChallenges"
          :key="challenge.title"
          class="bg-white dark:bg-gray-800 rounded-2xl shadow-md overflow-hidden border border-gray-200 dark:border-gray-700 hover:shadow-xl hover:-translate-y-1 transition-all duration-300"
        >
          <img :src="challenge.image" :alt="challenge.title" class="w-full h-40 object-cover">
          <div class="p-5">
            <span
              :class="[
                'inline-block px-2 py-1 text-xs font-semibold rounded-md mb-2',
                challenge.type === 'Monthly' ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-300' : 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300'
              ]"
            >
              {{ challenge.type === 'Monthly' ? t('joinChallenge.tabs.monthly') : t('joinChallenge.tabs.seasonal') }}
            </span>
            <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-2 truncate text-right rtl:text-right transition-colors">{{ challenge.title }}</h3>
            <p class="text-sm text-gray-600 dark:text-gray-300 mb-3 h-10 text-right rtl:text-right transition-colors">{{ challenge.description }}</p>
            <div class="flex items-center text-sm text-amber-600 dark:text-amber-400 font-medium mb-4 rtl:flex-row-reverse transition-colors">
              <Trophy class="w-4 h-4 mr-2 rtl:ml-2 rtl:mr-0" />
              <span>{{ challenge.badge }}</span>
            </div>
            <button class="w-full py-2.5 px-6 rounded-lg font-bold text-gray-800 dark:text-gray-900 bg-amber-400 dark:bg-amber-500 hover:bg-amber-500 dark:hover:bg-amber-600 transition-colors">
              {{ t('joinChallenge.cta') }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Trophy } from 'lucide-vue-next'
import { t } from '../../../../locales'
import { competitions as allCompetitions } from '../../../../lib/competitions'

const challengeTabs = ['All', 'Monthly', 'Seasonal'];
const activeChallengeTab = ref('All');

const filteredChallenges = computed(() => {
  if (activeChallengeTab.value === 'All') {
    return allCompetitions;
  }
  return allCompetitions.filter(c => c.type === activeChallengeTab.value);
});
</script> 