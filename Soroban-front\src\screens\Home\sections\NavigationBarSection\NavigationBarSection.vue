<template>
  <header class="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm shadow-sm fixed top-0 left-0 w-full z-50 transition-colors">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16">
        <!-- Logo section -->
        <router-link to="/" class="flex-shrink-0 flex items-center gap-2 hover:opacity-80 transition-opacity cursor-pointer">
          <img class="h-9 w-auto" src="https://c.animaapp.com/mc5ppr8hKB91iA/img/logo-lerning-removebg-preview-1-1.png" alt="ELBARQ Logo - منصة التعلم التفاعلية" loading="eager">
          <div class="brand-text text-2xl">
            <span class="font-bold text-[#d08700]">{{ t('brand.main') }}</span>
            <span class="font-bold text-slate-800 dark:text-slate-200">&nbsp;{{ t('brand.secondary') }}</span>
          </div>
        </router-link>

        <!-- Navigation menu -->
        <nav class="hidden md:block">
          <ul class="flex items-center space-x-2 rtl:space-x-reverse">
            <li v-for="item in navItems" :key="item.name">
              <router-link
                v-if="item.name === 'Home'"
                to="/"
                :class="[
                  'px-4 py-2 rounded-lg text-sm font-medium transition-colors',
                  activeLink === item.name
                    ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300'
                    : 'text-slate-600 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900/50'
                ]"
              >
                {{ t(`nav.${item.key}`) }}
              </router-link>
              <router-link
                v-else-if="item.name === 'Courses'"
                to="/courses"
                :class="[
                  'px-4 py-2 rounded-lg text-sm font-medium transition-colors',
                  activeLink === item.name
                    ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300'
                    : 'text-slate-600 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900/50'
                ]"
              >
                {{ t(`nav.${item.key}`) }}
              </router-link>
              <router-link
                v-else-if="item.name === 'Competition'"
                to="/competition"
                :class="[
                  'px-4 py-2 rounded-lg text-sm font-medium transition-colors',
                  activeLink === item.name
                    ? 'bg-yellow-100 text-yellow-700' // تمييز Competition
                    : 'text-slate-600 dark:text-gray-300 hover:bg-yellow-50 dark:hover:bg-yellow-900/50'
                ]"
              >
                {{ t(`nav.${item.key}`) }}
              </router-link>
            </li>
          </ul>
        </nav>

        <!-- User section -->
        <div class="flex items-center gap-4">
          <button @click="toggleDarkMode" class="p-2 rounded-lg bg-transparent transition-colors hover:bg-blue-100 dark:hover:bg-blue-100 focus:outline-none focus:ring-0">
            <Moon v-if="!isDarkMode" class="w-5 h-5 text-slate-600 dark:text-gray-300" />
            <Sun v-else class="w-5 h-5 text-slate-600 dark:text-gray-300" />
          </button>
          
          <!-- Language toggle button -->
          <button 
            @click="toggleLanguage"
            class="flex items-center gap-2 px-3 py-2 rounded-lg bg-transparent transition-colors hover:bg-blue-100 dark:hover:bg-blue-100 focus:outline-none focus:ring-0"
          >
            <span class="text-sm font-medium text-slate-700 dark:text-gray-300">{{ currentLocale.toUpperCase() }}</span>
            <Globe class="w-4 h-4 text-slate-500 dark:text-gray-400" />
          </button>
          
          <!-- Auth Buttons -->
          <div v-if="isLoggedIn" class="relative">
            <!-- User Dashboard Dropdown -->
            <button
              @click="toggleUserDropdown"
              class="flex items-center gap-2 p-2 rounded-lg bg-blue-500 text-white transition-colors hover:bg-blue-600 focus:outline-none focus:ring-0"
            >
              <User class="w-5 h-5" />
              <span class="hidden sm:inline text-sm font-medium">{{ userDisplayName }}</span>
              <ChevronDown class="w-4 h-4" />
            </button>

            <!-- User Dropdown Menu -->
            <div v-if="isUserDropdownOpen" class="absolute right-0 mt-2 w-56 bg-white dark:bg-gray-800 rounded-lg shadow-lg py-2 rtl:left-0 rtl:right-auto border border-gray-200 dark:border-gray-700">
              <!-- User Info -->
              <div class="px-4 py-2 border-b border-gray-200 dark:border-gray-700">
                <p class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ userDisplayName }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ userEmail }}</p>
                <span class="inline-block px-2 py-1 mt-1 text-xs font-medium rounded-full" :class="userRoleBadgeClass">
                  {{ userRoleDisplay }}
                </span>
              </div>

              <!-- Dashboard Link -->
              <router-link
                :to="userDashboardRoute"
                @click="closeUserDropdown"
                class="flex items-center gap-3 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900/50 transition-colors"
              >
                <LayoutDashboard class="w-4 h-4" />
                {{ t('nav.dashboard') }}
              </router-link>

              <!-- Profile Link -->
              <router-link
                to="/profile"
                @click="closeUserDropdown"
                class="flex items-center gap-3 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900/50 transition-colors"
              >
                <Settings class="w-4 h-4" />
                {{ t('nav.profile') }}
              </router-link>

              <!-- Admin/Teacher Management Links -->
              <template v-if="user?.role === 'admin' || user?.role === 'superAdmin'">
                <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>
                <router-link
                  :to="{ name: 'admin-courses' }"
                  @click="closeUserDropdown"
                  class="flex items-center gap-3 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900/50 transition-colors"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                  </svg>
                  Course Management
                </router-link>
                <router-link
                  :to="{ name: 'admin-videos' }"
                  @click="closeUserDropdown"
                  class="flex items-center gap-3 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900/50 transition-colors"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                  </svg>
                  Video Management
                </router-link>
              </template>

              <template v-if="user?.role === 'teacher'">
                <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>
                <router-link
                  :to="{ name: 'teacher-courses' }"
                  @click="closeUserDropdown"
                  class="flex items-center gap-3 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900/50 transition-colors"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                  </svg>
                  Course Management
                </router-link>
                <router-link
                  :to="{ name: 'teacher-videos' }"
                  @click="closeUserDropdown"
                  class="flex items-center gap-3 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900/50 transition-colors"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                  </svg>
                  Video Management
                </router-link>
              </template>

              <!-- Divider -->
              <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>

              <!-- Logout -->
              <button
                @click="showLogoutModal = true; closeUserDropdown()"
                class="flex items-center gap-3 w-full px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/50 transition-colors"
              >
                <LogOut class="w-4 h-4" />
                {{ t('nav.logout') }}
              </button>
            </div>
          </div>
          <div v-else class="relative">
            <button class="flex items-center gap-2 p-2 rounded-lg bg-transparent transition-colors hover:bg-blue-100 dark:hover:bg-blue-100 focus:outline-none focus:ring-0" @click="toggleDropdown">
              <div class="w-8 h-8 rounded-full flex items-center justify-center text-slate-600 dark:text-gray-300">
                <User class="w-5 h-5" />
              </div>
              <ChevronDown class="w-4 h-4 text-slate-500 dark:text-gray-400" />
            </button>
            <div v-if="isDropdownOpen" class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 rtl:left-0 rtl:right-auto">
              <router-link to="/login" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-700">{{ t('nav.login') }}</router-link>
              <router-link to="/register" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-700">{{ t('nav.register') }}</router-link>
            </div>
          </div>
        </div>

        <!-- Mobile menu button -->
        <div class="md:hidden flex items-center">
          <button @click="isMobileMenuOpen = !isMobileMenuOpen" class="inline-flex items-center justify-center p-2 rounded-md bg-transparent transition-colors hover:bg-blue-100 dark:hover:bg-blue-100 focus:outline-none focus:ring-0">
            <Menu class="h-6 w-6 text-gray-400 dark:text-gray-500" />
          </button>
        </div>
      </div>
    </div>

    <!-- Mobile menu -->
    <div v-if="isMobileMenuOpen" class="md:hidden bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm">
      <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
        <router-link
          v-for="item in navItems"
          :key="item.name"
          v-if="item.name === 'Home'"
          to="/"
          @click.native="isMobileMenuOpen = false"
          :class="['block px-3 py-2 rounded-md text-base font-medium', activeLink === item.name ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300' : 'text-gray-600 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900/50']"
        >
          {{ t(`nav.${item.key}`) }}
        </router-link>
        <router-link
          v-for="item in navItems"
          :key="item.name + '-courses'"
          v-if="item.name === 'Courses'"
          to="/courses"
          @click.native="isMobileMenuOpen = false"
          :class="['block px-3 py-2 rounded-md text-base font-medium', activeLink === item.name ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300' : 'text-gray-600 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900/50']"
        >
          {{ t(`nav.${item.key}`) }}
        </router-link>
        <router-link
          v-for="item in navItems"
          :key="item.name + '-competition'"
          v-if="item.name === 'Competition'"
          to="/competition"
          @click.native="isMobileMenuOpen = false"
          :class="['block px-3 py-2 rounded-md text-base font-medium', activeLink === item.name ? 'bg-yellow-100 text-yellow-700' : 'text-gray-600 dark:text-gray-300 hover:bg-yellow-50 dark:hover:bg-yellow-900/50']"
        >
          {{ t(`nav.${item.key}`) }}
        </router-link>
      </div>
    </div>
    
    <LogoutModal :show="showLogoutModal" :isLoading="isLoggingOut" @close="showLogoutModal = false" @confirm="handleLogout" />
  </header>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Menu, Moon, User, ChevronDown, Globe, Sun, LogOut, LayoutDashboard, Settings } from 'lucide-vue-next'
import { t, currentLocale, setLocale, type Locale } from '../../../../locales'
import LogoutModal from '../../../../components/LogoutModal.vue'
import { useAuth } from '../../../../composables/useAuth'

const router = useRouter()
const route = useRoute()

// Auth composable
const { isAuthenticated, logout, user } = useAuth()

// --- Auth State ---
const isLoggedIn = computed(() => isAuthenticated.value)
const showLogoutModal = ref(false)
const isUserDropdownOpen = ref(false)
const isLoggingOut = ref(false)

// Get current user data
const currentUser = computed(() => user.value)

const userDisplayName = computed(() => {
  if (!currentUser.value) return ''
  return `${currentUser.value.first_name} ${currentUser.value.second_name}`
})

const userEmail = computed(() => {
  return currentUser.value?.email || ''
})

const userRoleDisplay = computed(() => {
  if (!currentUser.value) return ''
  const roleMap = {
    'superAdmin': t('roles.superAdmin'),
    'admin': t('roles.admin'),
    'teacher': t('roles.teacher'),
    'student': t('roles.student'),
    'guest': t('roles.guest')
  }
  return roleMap[currentUser.value.role] || currentUser.value.role
})

const userRoleBadgeClass = computed(() => {
  if (!currentUser.value) return ''
  const roleClasses = {
    'superAdmin': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
    'admin': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    'teacher': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    'student': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    'guest': 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
  }
  return roleClasses[currentUser.value.role] || 'bg-gray-100 text-gray-800'
})

const userDashboardRoute = computed(() => {
  if (!currentUser.value) return '/'
  const dashboardRoutes = {
    'superAdmin': '/super-admin-dashboard',
    'admin': '/admin-dashboard',
    'teacher': '/teacher-dashboard',
    'student': '/student-dashboard',
    'guest': '/'
  }
  return dashboardRoutes[currentUser.value.role] || '/'
})

const toggleUserDropdown = () => {
  isUserDropdownOpen.value = !isUserDropdownOpen.value
}

const closeUserDropdown = () => {
  isUserDropdownOpen.value = false
}

const handleLogout = async () => {
  isLoggingOut.value = true

  try {
    await logout()
    showLogoutModal.value = false
    // The logout composable handles clearing localStorage and redirecting
  } catch (error) {
    console.error('Logout failed:', error)
    // Even if logout fails on server, clear local storage and redirect
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user')
    showLogoutModal.value = false
    router.push('/')
  } finally {
    isLoggingOut.value = false
  }
}
// --- End Auth State ---

interface NavItem {
  name: string;
  key: string;
  sectionId: string;
}

const navItems = ref<NavItem[]>([
  { name: "Home", key: "home", sectionId: "hero" },
  { name: "Courses", key: "courses", sectionId: "start-learning-as-young" },
  { name: "Competition", key: "competition", sectionId: "join-challenge" },
]);

const activeLink = ref('Home');
const isDropdownOpen = ref(false);
const isMobileMenuOpen = ref(false);
const isDarkMode = ref(false);

const toggleDropdown = () => {
  isDropdownOpen.value = !isDropdownOpen.value
  // Close user dropdown if it's open
  if (isDropdownOpen.value) {
    isUserDropdownOpen.value = false
  }
}

const toggleLanguage = () => {
  const newLocale: Locale = currentLocale.value === 'en' ? 'ar' : 'en'
  setLocale(newLocale)
}

const toggleDarkMode = () => {
  isDarkMode.value = !isDarkMode.value
  // Save to localStorage
  localStorage.setItem('darkMode', isDarkMode.value.toString())
  // Apply dark mode to document
  if (isDarkMode.value) {
    document.documentElement.classList.add('dark')
  } else {
    document.documentElement.classList.remove('dark')
  }
}

// Initialize dark mode from localStorage
const initializeDarkMode = () => {
  const savedDarkMode = localStorage.getItem('darkMode')
  if (savedDarkMode !== null) {
    isDarkMode.value = savedDarkMode === 'true'
    if (isDarkMode.value) {
      document.documentElement.classList.add('dark')
    }
  }
}

const scrollToSection = (sectionId: string) => {
  const element = document.getElementById(sectionId);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });
    // Update active link
    const navItem = navItems.value.find(item => item.sectionId === sectionId);
    if (navItem) {
      activeLink.value = navItem.name;
    }
  }
}

// Update active link on scroll
const updateActiveLinkOnScroll = () => {
  const sections = navItems.value.map(item => ({
    id: item.sectionId,
    name: item.name
  }));
  
  const scrollPosition = window.scrollY + 100; // Offset for fixed header
  
  for (const section of sections) {
    const element = document.getElementById(section.id);
    if (element) {
      const { offsetTop, offsetHeight } = element;
      if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {
        activeLink.value = section.name;
        break;
      }
    }
  }
}

// تحديث activeLink تلقائياً عند تغيير المسار
watch(
  () => route.path,
  (newPath) => {
    if (newPath === '/') {
      activeLink.value = 'Home'
    } else if (newPath.startsWith('/courses')) {
      activeLink.value = 'Courses'
    } else if (newPath.includes('join-challenge')) {
      activeLink.value = 'Competition'
    } else {
      activeLink.value = ''
    }
  },
  { immediate: true }
)

// Click outside handler to close dropdowns
const handleClickOutside = (event: Event) => {
  const target = event.target as Element
  if (!target.closest('.relative')) {
    isDropdownOpen.value = false
    isUserDropdownOpen.value = false
  }
}

// Add scroll listener when component is mounted
onMounted(() => {
  window.addEventListener('scroll', updateActiveLinkOnScroll);
  window.addEventListener('click', handleClickOutside);
  initializeDarkMode();
});

onUnmounted(() => {
  window.removeEventListener('scroll', updateActiveLinkOnScroll);
  window.removeEventListener('click', handleClickOutside);
});
</script> 