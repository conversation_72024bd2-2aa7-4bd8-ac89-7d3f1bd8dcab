<template>
  <div class="mb-12 sm:mb-16">
    <h2 class="text-2xl sm:text-3xl lg:text-4xl font-bold text-center mb-8 sm:mb-12 text-gray-900 dark:text-white transition-colors">
      {{ t('mission.title') }}
    </h2>
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
      <!-- Grid for images and stats -->
      <div class="grid grid-cols-2 gap-4">
        <img :src="missionData.images.topLeft" alt="Child learning" class="rounded-xl object-cover w-full h-full aspect-square">
        
        <div :class="[missionData.stats[0].bgClass, 'p-6 rounded-xl flex flex-col items-center justify-center text-center aspect-square']">
          <component :is="missionData.stats[0].icon" :class="['w-8 h-8 mb-2', missionData.stats[0].iconClass]" /> 
          <div class="text-2xl font-bold text-gray-900 dark:text-white transition-colors">{{ missionData.stats[0].count }}</div>
          <div class="text-lg text-gray-600 dark:text-gray-300 transition-colors">{{ t('mission.stats.teachers') }}</div>
        </div>

        <div :class="[missionData.stats[1].bgClass, 'p-6 rounded-xl flex flex-col items-center justify-center text-center aspect-square']">
          <component :is="missionData.stats[1].icon" :class="['w-8 h-8 mb-2', missionData.stats[1].iconClass]" />
          <div class="text-2xl font-bold text-gray-900 dark:text-white transition-colors">{{ missionData.stats[1].count }}</div>
          <div class="text-lg text-gray-600 dark:text-gray-300 transition-colors">{{ t('mission.stats.students') }}</div>
        </div>

        <img :src="missionData.images.bottomRight" alt="Students collaborating" class="rounded-xl object-cover w-full h-full aspect-square">
      </div>

      <!-- Text Content -->
      <div class="text-center lg:text-left rtl:lg:text-right">
          <h3 class="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-4 transition-colors">{{ t('mission.subtitle') }}</h3>
          <div class="text-gray-600 dark:text-gray-300 mb-6 text-right rtl:text-right transition-colors">
            <p>{{ t('mission.description') }}</p>
            <div v-if="showMoreContent" class="mt-4 space-y-3">
              <p>{{ t('mission.additionalContent.paragraph1') }}</p>
              <p>{{ t('mission.additionalContent.paragraph2') }}</p>
              <p>{{ t('mission.additionalContent.paragraph3') }}</p>
            </div>
          </div>
          <button 
            @click="toggleMoreContent"
            class="bg-white dark:bg-gray-800 text-blue-600 dark:text-blue-400 font-semibold py-2 px-5 border border-blue-600 dark:border-blue-400 rounded-lg hover:bg-blue-50 dark:hover:bg-gray-700 transition-colors mb-4"
          >
            {{ showMoreContent ? t('mission.showLess') : t('mission.cta') }}
          </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Users, GraduationCap } from 'lucide-vue-next'
import { t } from '../../../../locales'

const showMoreContent = ref(false)

const toggleMoreContent = () => {
  showMoreContent.value = !showMoreContent.value
}

const missionData = ref({
  images: {
    topLeft: "https://c.animaapp.com/mc5ppr8hKB91iA/img/component-2.png",
    bottomRight: "https://c.animaapp.com/mc5ppr8hKB91iA/img/component-2.png"
  },
  stats: [
    {
      icon: Users,
      count: "1k+",
      label: "Qualified Teachers",
      bgClass: "bg-blue-50",
      iconClass: "text-blue-600"
    },
    {
      icon: GraduationCap,
      count: "9k+",
      label: "Active Student",
      bgClass: "bg-green-50",
      iconClass: "text-green-600"
    }
  ]
})
</script> 