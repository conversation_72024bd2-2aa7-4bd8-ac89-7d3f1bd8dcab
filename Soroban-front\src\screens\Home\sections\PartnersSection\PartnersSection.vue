<template>
  <section class="py-12 sm:py-16 lg:py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <h2 class="text-3xl font-bold text-gray-900 dark:text-white text-center mb-10 sm:mb-12 transition-colors">
        {{ t('partners.title') }}
      </h2>
      <div class="flex justify-center items-center flex-wrap gap-x-12 sm:gap-x-16 lg:gap-x-24 gap-y-8">
        <div v-for="(partner, index) in partners" :key="index" class="flex flex-col items-center text-center w-24">
          <div class="w-16 h-16 rounded-full bg-slate-100 dark:bg-gray-800 flex items-center justify-center mb-3 transition-colors">
            <component :is="partner.icon" class="w-8 h-8 text-gray-500 dark:text-gray-400" />
          </div>
          <span class="font-medium text-gray-600 dark:text-gray-300 transition-colors">{{ t('partners.companyName') }}</span>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Building2, GraduationCap, Lightbulb, Briefcase, Globe } from 'lucide-vue-next'
import { t } from '../../../../locales'

// Partners data
const partners = ref([
    { name: 'Company Name', icon: Building2 },
    { name: 'Company Name', icon: GraduationCap },
    { name: 'Company Name', icon: Lightbulb },
    { name: 'Company Name', icon: Briefcase },
    { name: 'Company Name', icon: Globe }
]);
</script> 