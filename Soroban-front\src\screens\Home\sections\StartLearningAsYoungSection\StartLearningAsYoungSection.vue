<template>
  <div class="mb-12 sm:mb-16">
    <h2 class="text-2xl sm:text-3xl lg:text-4xl font-bold text-center mb-2 text-gray-900 dark:text-white transition-colors">
      {{ t('startLearningYoung.title') }}
    </h2>
    <p class="text-center text-gray-600 dark:text-gray-300 mb-8 sm:mb-12 max-w-2xl mx-auto text-right rtl:text-right transition-colors">
      {{ t('startLearningYoung.subtitle') }}
    </p>
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
      <div
        v-for="(level, index) in learningLevels"
        :key="index"
        class="animated-card group bg-white dark:bg-gray-800 rounded-xl sm:rounded-2xl shadow-lg overflow-hidden transition-all duration-300 ease-in-out hover:shadow-xl hover:scale-105 hover:bg-sky-50 dark:hover:bg-gray-700"
        :ref="el => { if (el) youngLearnerCardRefs[index] = el }"
      >
        <img :src="level.image" :alt="level.title" class="w-full h-40 sm:h-48 object-cover" />
        <div class="p-4 sm:p-6">
          <div class="flex items-center justify-start mb-3 sm:mb-4">
            <span :class="['px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium', level.labelBg, level.labelText]">
              {{ t(`startLearningYoung.levels.${level.levelKey}`) }}
            </span>
          </div>
          <h3 class="text-lg sm:text-xl font-semibold mb-2 text-gray-900 dark:text-white text-right rtl:text-right transition-colors">{{ t(`startLearningYoung.levels.${level.key}.title`) }}</h3>
          <p class="text-sm sm:text-base text-gray-600 dark:text-gray-300 mb-4 text-right rtl:text-right transition-colors">{{ t(`startLearningYoung.levels.${level.key}.description`) }}</p>
          <button
            class="w-full py-2 sm:py-3 px-4 sm:px-6 rounded-lg font-medium transition-colors text-sm sm:text-base bg-white dark:bg-gray-700 text-sky-600 dark:text-sky-400 border border-sky-600 dark:border-sky-400 group-hover:bg-sky-600 dark:group-hover:bg-sky-500 group-hover:text-white"
          >
            {{ t('startLearningYoung.cta') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUpdate } from 'vue'
import { t } from '../../../../locales'

// Animation logic
const youngLearnerCardRefs = ref<any[]>([])

onBeforeUpdate(() => {
  youngLearnerCardRefs.value = []
})

onMounted(() => {
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add('is-visible')
          observer.unobserve(entry.target)
        }
      })
    },
    {
      threshold: 0.2,
    }
  )
  youngLearnerCardRefs.value.forEach((el) => {
    if (el) observer.observe(el)
  })
})
// -----------------------------------------

// Learning levels data
const learningLevels = ref([
  {
    title: "Basics Explorer",
    key: "basicsExplorer",
    levelKey: "level1",
    image: "https://c.animaapp.com/mc5ppr8hKB91iA/img/component-2.png",
    labelBg: "bg-sky-100",
    labelText: "text-gray-900",
  },
  {
    title: "Skill Builder",
    key: "skillBuilder",
    levelKey: "level2",
    image: "https://c.animaapp.com/mc5ppr8hKB91iA/img/component-2-1.png",
    labelBg: "bg-red-100",
    labelText: "text-red-800",
  },
  {
    title: "Future Ready",
    key: "futureReady",
    levelKey: "level3",
    image: "https://c.animaapp.com/mc5ppr8hKB91iA/img/component-2-2.png",
    labelBg: "bg-green-100",
    labelText: "text-green-800",
  },
])
</script>

<style scoped>
.animated-card {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.5s ease-out, transform 0.5s ease-out;
}

.animated-card.is-visible {
  opacity: 1;
  transform: translateY(0);
}
</style> 