<template>
  <section class="py-12 sm:py-16 lg:py-20 bg-white dark:bg-gray-900 transition-colors">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-10 sm:mb-14">
        <h2 class="text-3xl sm:text-4xl font-bold text-gray-900 dark:text-white mb-3 transition-colors">
          {{ t('testimonials.title') }}
        </h2>
        <p class="text-base sm:text-lg text-gray-600 dark:text-gray-300 max-w-xl mx-auto text-right rtl:text-right transition-colors">
          {{ t('testimonials.subtitle') }}
        </p>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div v-for="(testimonial, index) in testimonials" :key="index" class="bg-slate-100/60 dark:bg-gray-800/60 rounded-2xl p-6 transition-colors">
          <div class="flex items-center mb-4">
            <Star v-for="i in 5" :key="i" class="w-5 h-5 text-yellow-400" fill="currentColor" />
          </div>
          <p class="text-gray-700 dark:text-gray-300 mb-6 text-right rtl:text-right transition-colors">"{{ t('testimonials.text') }}"</p>
          <div class="flex items-center rtl:flex-row-reverse">
            <img :src="testimonial.image" :alt="testimonial.name" class="w-12 h-12 rounded-full object-cover">
            <div class="ml-4 rtl:mr-4 rtl:ml-0">
              <h4 class="font-semibold text-gray-900 dark:text-white text-right rtl:text-right transition-colors">{{ testimonial.name }}</h4>
              <p class="text-sm text-gray-600 dark:text-gray-400 text-right rtl:text-right transition-colors">{{ t(`testimonials.title${index + 1}`) }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Star } from 'lucide-vue-next'
import { t } from '../../../../locales'

// Testimonials data
const testimonials = ref([
  {
    name: "Nour Jan",
    image: "https://c.animaapp.com/mc5ppr8hKB91iA/img/<EMAIL>",
    rating: 5
  },
  {
    name: "Holly Jack",
    image: "https://c.animaapp.com/mc5ppr8hKB91iA/img/<EMAIL>",
    rating: 5
  },
  {
    name: "Anna May",
    image: "https://c.animaapp.com/mc5ppr8hKB91iA/img/<EMAIL>", // Using a different image for variety
    rating: 5
  }
]);
</script> 