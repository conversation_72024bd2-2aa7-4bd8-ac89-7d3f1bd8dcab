<template>
  <div class="relative w-full h-[300px] sm:h-[350px] lg:h-[400px] rounded-xl sm:rounded-2xl overflow-hidden mb-12 sm:mb-16 transition-colors">
    <img
      :src="videoBackground"
      :alt="t('video.alt')"
      class="w-full h-full object-cover"
    />
    <div class="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
      <button 
        class="bg-white bg-opacity-20 dark:bg-opacity-30 backdrop-blur-sm rounded-full p-3 sm:p-4 hover:bg-opacity-30 dark:hover:bg-opacity-40 transition-all"
        :aria-label="t('video.playButton')"
      >
        <svg class="w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 text-white" fill="currentColor" viewBox="0 0 24 24">
          <path d="M8 5v14l11-7z"/>
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { t } from '../../../../locales'

// Video section data
const videoBackground = ref("https://c.animaapp.com/mc5ppr8hKB91iA/img/video.png")
</script> 