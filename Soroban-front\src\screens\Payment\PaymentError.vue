<template>
  <div class="min-h-screen bg-gray-50 flex items-center justify-center px-4">
    <div class="max-w-md w-full">
      <!-- Error Card -->
      <div class="bg-white rounded-2xl shadow-xl p-8 text-center">
        <!-- Error Icon -->
        <div class="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <svg class="w-10 h-10 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </div>

        <!-- Error Message -->
        <h1 class="text-2xl font-bold text-gray-900 mb-2">Payment Failed</h1>
        <p class="text-gray-600 mb-6">
          {{ errorMessage || 'We encountered an issue processing your payment. Please try again.' }}
        </p>

        <!-- Error Details -->
        <div v-if="errorDetails" class="bg-red-50 rounded-lg p-4 mb-6 text-left">
          <h3 class="font-semibold text-red-800 mb-2">Error Details</h3>
          <p class="text-sm text-red-700">{{ errorDetails }}</p>
        </div>

        <!-- Common Issues -->
        <div class="bg-gray-50 rounded-lg p-4 mb-6 text-left">
          <h3 class="font-semibold text-gray-900 mb-3">Common Issues</h3>
          <ul class="text-sm text-gray-600 space-y-1">
            <li>• Insufficient funds in your account</li>
            <li>• Payment method declined by bank</li>
            <li>• Network connection issues</li>
            <li>• Expired payment session</li>
          </ul>
        </div>

        <!-- Action Buttons -->
        <div class="space-y-3">
          <button 
            @click="retryPayment"
            class="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
          
          <router-link 
            to="/courses" 
            class="w-full bg-gray-100 text-gray-700 py-3 px-6 rounded-lg hover:bg-gray-200 transition-colors inline-block"
          >
            Browse Courses
          </router-link>
        </div>

        <!-- Support Info -->
        <div class="mt-6 pt-6 border-t border-gray-200">
          <p class="text-sm text-gray-500 mb-2">
            Still having trouble?
          </p>
          <div class="flex justify-center space-x-4 text-sm">
            <a href="/contact" class="text-blue-600 hover:text-blue-700">Contact Support</a>
            <span class="text-gray-300">|</span>
            <a href="/help" class="text-blue-600 hover:text-blue-700">Help Center</a>
          </div>
        </div>
      </div>

      <!-- Alternative Payment Methods -->
      <div class="mt-6 bg-white rounded-lg shadow p-6">
        <h3 class="font-semibold text-gray-900 mb-3 text-center">Alternative Payment Methods</h3>
        <div class="grid grid-cols-2 gap-3">
          <button 
            @click="selectPaymentMethod('bank_transfer')"
            class="flex items-center justify-center gap-2 p-3 border border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors"
          >
            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
            </svg>
            <span class="text-sm font-medium">Bank Transfer</span>
          </button>
          
          <button 
            @click="selectPaymentMethod('mobile_wallet')"
            class="flex items-center justify-center gap-2 p-3 border border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors"
          >
            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"/>
            </svg>
            <span class="text-sm font-medium">Mobile Wallet</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// State
const errorMessage = ref('')
const errorDetails = ref('')

// Methods
const loadErrorDetails = () => {
  errorMessage.value = route.query.message as string || ''
  errorDetails.value = route.query.details as string || ''
}

const retryPayment = () => {
  // Go back to courses page to retry payment
  router.push('/courses')
}

const selectPaymentMethod = (method: string) => {
  // Redirect to courses with selected payment method
  router.push({
    path: '/courses',
    query: { payment_method: method }
  })
}

// Initialize
onMounted(() => {
  loadErrorDetails()
})
</script>
