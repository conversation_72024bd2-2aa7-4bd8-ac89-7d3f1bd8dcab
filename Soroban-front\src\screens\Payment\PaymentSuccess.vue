<template>
  <div class="min-h-screen bg-gray-50 flex items-center justify-center px-4">
    <div class="max-w-md w-full">
      <!-- Success Card -->
      <div class="bg-white rounded-2xl shadow-xl p-8 text-center">
        <!-- Success Icon -->
        <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <svg class="w-10 h-10 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
          </svg>
        </div>

        <!-- Success Message -->
        <h1 class="text-2xl font-bold text-gray-900 mb-2">Payment Successful!</h1>
        <p class="text-gray-600 mb-6">
          Your payment has been processed successfully. You now have access to your course content.
        </p>

        <!-- Payment Details -->
        <div v-if="paymentDetails" class="bg-gray-50 rounded-lg p-4 mb-6 text-left">
          <h3 class="font-semibold text-gray-900 mb-3">Payment Details</h3>
          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span class="text-gray-600">Order ID:</span>
              <span class="font-medium">{{ paymentDetails.order_id }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Amount:</span>
              <span class="font-medium">${{ paymentDetails.amount }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Payment Method:</span>
              <span class="font-medium">PayPal</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Date:</span>
              <span class="font-medium">{{ formatDate(paymentDetails.date) }}</span>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="space-y-3">
          <router-link 
            :to="courseUrl" 
            class="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors inline-block"
          >
            Start Learning
          </router-link>
          
          <router-link 
            to="/student-dashboard" 
            class="w-full bg-gray-100 text-gray-700 py-3 px-6 rounded-lg hover:bg-gray-200 transition-colors inline-block"
          >
            Go to Dashboard
          </router-link>
        </div>

        <!-- Support Info -->
        <div class="mt-6 pt-6 border-t border-gray-200">
          <p class="text-sm text-gray-500">
            Need help? <a href="/contact" class="text-blue-600 hover:text-blue-700">Contact Support</a>
          </p>
        </div>
      </div>

      <!-- Additional Info -->
      <div class="mt-6 text-center">
        <p class="text-sm text-gray-500">
          A confirmation email has been sent to your registered email address.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { apiService } from '../../services/api'

const route = useRoute()
const router = useRouter()

// State
const paymentDetails = ref(null)
const isLoading = ref(true)
const error = ref('')

// Computed
const courseUrl = computed(() => {
  if (paymentDetails.value?.course_id) {
    return `/courses/${paymentDetails.value.course_id}/videos`
  }
  return '/courses'
})

// Methods
const loadPaymentDetails = async () => {
  const orderId = route.query.order_id
  
  if (!orderId) {
    error.value = 'No order ID provided'
    isLoading.value = false
    return
  }

  try {
    // You can fetch payment details from your API if needed
    paymentDetails.value = {
      order_id: orderId,
      amount: route.query.amount || 'N/A',
      date: new Date().toISOString(),
      course_id: route.query.course_id
    }
  } catch (err) {
    console.error('Failed to load payment details:', err)
    error.value = 'Failed to load payment details'
  } finally {
    isLoading.value = false
  }
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Initialize
onMounted(() => {
  loadPaymentDetails()
})
</script>
