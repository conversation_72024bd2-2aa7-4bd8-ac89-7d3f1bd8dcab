<template>
  <div class="flex min-h-screen bg-[#F9FAFB]">
    <!-- Sidebar -->
    <aside class="w-64 bg-[#F6F9FF] flex flex-col justify-between border-r border-[#D1D5DC]">
      <div>
        <router-link to="/" class="flex items-center gap-2 px-6 py-8 hover:opacity-80 transition-opacity">
          <img src="https://c.animaapp.com/mc5ppr8hKB91iA/img/logo-lerning-removebg-preview-1-1.png" alt="ELBARQ Logo" class="h-10 w-10 rounded" />
          <div>
            <div class="font-bold text-[#162456] text-lg leading-4">ELBARQ</div>
            <div class="text-xs text-[#d08700] font-semibold -mt-1">Soroban</div>
          </div>
        </router-link>
        <nav class="mt-2">
          <ul class="space-y-2 px-2">
            <li>
              <router-link to="/" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all hover:bg-white hover:shadow-md">
                <span>
                  <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
                    <path d="M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" stroke="#043355" stroke-width="2"/>
                    <path d="M9 22V12h6v10" stroke="#043355" stroke-width="2"/>
                  </svg>
                </span>
                Home
              </router-link>
            </li>

            <li>
              <a href="#" @click="activeSection = 'dashboard'" :class="activeSection === 'dashboard' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all">
                <span>
                  <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
                    <path d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" stroke="#043355" stroke-width="2"/>
                  </svg>
                </span>
                Dashboard
              </a>
            </li>

            <li>
              <a href="#" @click="activeSection = 'courses'" :class="activeSection === 'courses' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all">
                <span>
                  <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
                    <path d="M2 3h6a4 4 0 014 4v14a3 3 0 00-3-3H2V3z" stroke="#043355" stroke-width="2"/>
                    <path d="M22 3h-6a4 4 0 00-4 4v14a3 3 0 013-3h7V3z" stroke="#043355" stroke-width="2"/>
                  </svg>
                </span>
                Courses
              </a>
            </li>
            
            <li>
              <a href="#" @click="activeSection = 'competition'" :class="activeSection === 'competition' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all">
                <span>
                  <svg width="18" height="18" fill="#043355" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </span>
                Competition
              </a>
            </li>

            <li>
              <a href="#" @click="activeSection = 'certificates'" :class="activeSection === 'certificates' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all">
                <span>
                  <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
                    <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8l-6-6z" stroke="#043355" stroke-width="2"/>
                    <path d="M14 2v6h6" stroke="#043355" stroke-width="2"/>
                    <path d="M16 13H8" stroke="#043355" stroke-width="2"/>
                    <path d="M16 17H8" stroke="#043355" stroke-width="2"/>
                    <path d="M10 9H8" stroke="#043355" stroke-width="2"/>
                  </svg>
                </span>
                Certificates
              </a>
            </li>
            
            <li>
              <a href="#" @click="activeSection = 'payment'" :class="activeSection === 'payment' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all">
                <span>
                  <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
                    <rect x="2" y="3" width="20" height="14" rx="2" ry="2" stroke="#043355" stroke-width="2"/>
                    <path d="M8 21l4-7 4 7" stroke="#043355" stroke-width="2"/>
                  </svg>
                </span>
                Payment
              </a>
            </li>

            <li>
              <a href="#" @click="activeSection = 'contact'" :class="activeSection === 'contact' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all">
                <span>
                  <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
                    <path d="M21 15a2 2 0 01-2 2H7l-4 4V5a2 2 0 012-2h14a2 2 0 012 2v10z" stroke="#043355" stroke-width="2"/>
                    <path d="M13 11h0" stroke="#043355" stroke-width="2"/>
                    <path d="M9 11h0" stroke="#043355" stroke-width="2"/>
                    <path d="M17 11h0" stroke="#043355" stroke-width="2"/>
                  </svg>
                </span>
                Contact & Help
              </a>
            </li>
          </ul>
        </nav>
      </div>
      <div class="mb-6 px-2 space-y-2">
        <a href="#" @click="activeSection = 'profile'" :class="activeSection === 'profile' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all">
          <span>
            <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
              <path d="M20 21v-2a4 4 0 00-4-4H8a4 4 0 00-4 4v2" stroke="#043355" stroke-width="2"/>
              <circle cx="12" cy="7" r="4" stroke="#043355" stroke-width="2"/>
            </svg>
          </span>
          Profile
        </a>

        <a href="#" @click="activeSection = 'settings'" :class="activeSection === 'settings' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all">
          <span>
            <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
              <circle cx="12" cy="12" r="3" stroke="#043355" stroke-width="2"/>
              <path d="M19.4 15a1.65 1.65 0 00.33 1.82l.06.06a2 2 0 010 2.83 2 2 0 01-2.83 0l-.06-.06a1.65 1.65 0 00-1.82-.33 1.65 1.65 0 00-1 1.51V21a2 2 0 01-2 2 2 2 0 01-2-2v-.09A1.65 1.65 0 009 19.4a1.65 1.65 0 00-1.82.33l-.06.06a2 2 0 01-2.83 0 2 2 0 010-2.83l.06-.06a1.65 1.65 0 00.33-1.82 1.65 1.65 0 00-1.51-1H3a2 2 0 01-2-2 2 2 0 012-2h.09A1.65 1.65 0 004.6 9a1.65 1.65 0 00-.33-1.82l-.06-.06a2 2 0 010-2.83 2 2 0 012.83 0l.06.06a1.65 1.65 0 001.82.33H9a1.65 1.65 0 001-1.51V3a2 2 0 012-2 2 2 0 012 2v.09a1.65 1.65 0 001 1.51 1.65 1.65 0 001.82-.33l.06-.06a2 2 0 012.83 0 2 2 0 010 2.83l-.06.06a1.65 1.65 0 00-.33 1.82V9a1.65 1.65 0 001.51 1H21a2 2 0 012 2 2 2 0 01-2 2h-.09a1.65 1.65 0 00-1.51 1z" stroke="#043355" stroke-width="2"/>
            </svg>
          </span>
          Settings
        </a>
        <a href="#" @click="showLogoutConfirm = true" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#e7000b] hover:bg-red-50 hover:shadow-md transition-all">
          <span>
            <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
              <path d="M9 21H5a2 2 0 01-2-2V5a2 2 0 012-2h4" stroke="#e7000b" stroke-width="2"/>
              <path d="M16 17l5-5-5-5" stroke="#e7000b" stroke-width="2"/>
              <path d="M21 12H9" stroke="#e7000b" stroke-width="2"/>
            </svg>
          </span>
          Log Out
        </a>
      </div>
    </aside>

    <!-- Main Content -->
    <main class="flex-1 min-h-screen">
      <!-- Header -->
      <div class="flex justify-end items-center px-10 py-4 bg-white shadow-sm border-b border-[#F3F3F3]" style="min-height:107px;">
        <div class="flex items-center gap-6">
          <span class="text-xl text-black"><svg width="20" height="20" fill="none" viewBox="0 0 24 24"><path d="M21 12.79A9 9 0 1 1 11.21 3a7 7 0 0 0 9.79 9.79Z" stroke="#000" stroke-width="2"/></svg></span>
          <span class="relative">
            <svg class="w-7 h-7 text-[#043355]" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V4a2 2 0 10-4 0v1.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/></svg>
            <span class="absolute -top-2 -right-2 bg-[#BC0000] text-white rounded-full w-5 h-5 flex items-center justify-center text-xs border-2 border-white">+1</span>
          </span>
          <div class="flex items-center gap-2 bg-white border border-[#D1D5DC] rounded-full px-4 py-2">
            <img :src="userAvatar" :alt="userDisplayName" class="w-8 h-8 rounded-full object-cover border border-[#D1D5DC]" />
            <span class="font-bold text-[#043355]">{{ userDisplayName }}</span>
          </div>
        </div>
      </div>

      <!-- Dashboard Content -->
      <div v-if="activeSection === 'dashboard'">
        <!-- Dashboard Title -->
        <div class="px-10 pt-8">
          <h1 class="text-4xl font-extrabold text-[#043355] mb-8">Dashboard</h1>
          <div class="flex gap-8 mb-10">
            <!-- Certificates Card -->
            <div
              @click="activeCard = 'certificates'"
              :class="activeCard === 'certificates' ? 'bg-[#043355]' : 'bg-[#F6F9FF] hover:bg-[#E8F2FF]'"
              class="flex-1 rounded-[24px] flex flex-col items-center justify-center py-8 min-w-[220px] cursor-pointer transition-all duration-300 hover:shadow-lg transform hover:scale-105"
            >
              <span :class="activeCard === 'certificates' ? 'text-white' : 'text-[#043355]'" class="text-lg font-bold mb-2">Count Certificates</span>
              <span :class="activeCard === 'certificates' ? 'text-white' : 'text-[#1447E6]'" class="text-4xl font-extrabold">
                {{ dashboardSummary.totalCertificates }}<span class="text-base font-normal ml-1">Certificates</span>
              </span>
            </div>

            <!-- Courses Card -->
            <div
              @click="activeCard = 'courses'"
              :class="activeCard === 'courses' ? 'bg-[#043355]' : 'bg-[#F6F9FF] hover:bg-[#E8F2FF]'"
              class="flex-1 rounded-[24px] flex flex-col items-center justify-center py-8 min-w-[220px] cursor-pointer transition-all duration-300 hover:shadow-lg transform hover:scale-105"
            >
              <span :class="activeCard === 'courses' ? 'text-white' : 'text-[#043355]'" class="text-lg font-bold mb-2">My Courses</span>
              <span :class="activeCard === 'courses' ? 'text-white' : 'text-[#1447E6]'" class="text-4xl font-extrabold">
                {{ dashboardSummary.totalCourses }}<span class="text-base font-normal ml-1">Courses</span>
              </span>
            </div>

            <!-- Progress Card -->
            <div
              @click="activeCard = 'progress'"
              :class="activeCard === 'progress' ? 'bg-[#043355]' : 'bg-[#F6F9FF] hover:bg-[#E8F2FF]'"
              class="flex-1 rounded-[24px] flex flex-col items-center justify-center py-8 min-w-[220px] cursor-pointer transition-all duration-300 hover:shadow-lg transform hover:scale-105"
            >
              <span :class="activeCard === 'progress' ? 'text-white' : 'text-[#043355]'" class="text-lg font-bold mb-2">Overall Progress</span>
              <span :class="activeCard === 'progress' ? 'text-white' : 'text-[#1447E6]'" class="text-4xl font-extrabold">
                {{ dashboardSummary.overallProgress }}%<span :class="activeCard === 'progress' ? 'text-white' : 'text-[#043355]'" class="text-base font-normal ml-1">Complete</span>
              </span>
            </div>
          </div>
        </div>

        <!-- Progress Chart -->
        <div class="bg-white rounded-2xl shadow p-6 mb-8 mx-10">
          <h2 class="text-xl font-bold text-[#162456] mb-6 flex items-center gap-2">
            <span>
              <svg width="20" height="20" fill="none" viewBox="0 0 24 24">
                <path d="M3 3h7v7H3V3zM14 3h7v7h-7V3zM14 14h7v7h-7v-7zM3 14h7v7H3v-7z" stroke="#162456" stroke-width="2" fill="none"/>
              </svg>
            </span>
            Progress In Course
          </h2>
          
          <!-- Chart Container with gradient background -->
          <div class="relative bg-gradient-to-br from-[#EFF6FF] to-[#DBEAFE] rounded-xl p-6 h-64">
            <svg viewBox="0 0 600 200" class="w-full h-full">
              <!-- Grid lines -->
              <defs>
                <pattern id="grid" width="60" height="40" patternUnits="userSpaceOnUse">
                  <path d="M 60 0 L 0 0 0 40" fill="none" stroke="#E5E7EB" stroke-width="1" opacity="0.3"/>
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#grid)" />
              
              <!-- Area under curve -->
              <defs>
                <linearGradient id="areaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                  <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:0.3" />
                  <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:0.1" />
                </linearGradient>
              </defs>
              <path d="M 60 160 L 120 140 L 180 120 L 240 130 L 300 100 L 360 110 L 420 80 L 480 100 L 540 60 L 540 180 L 60 180 Z" fill="url(#areaGradient)" />
              
              <!-- Main line -->
              <polyline fill="none" stroke="#3B82F6" stroke-width="3"
                        points="60,160 120,140 180,120 240,130 300,100 360,110 420,80 480,100 540,60" />
              
              <!-- Data points -->
              <circle cx="60" cy="160" r="4" fill="#3B82F6" />
              <circle cx="120" cy="140" r="4" fill="#3B82F6" />
              <circle cx="180" cy="120" r="4" fill="#3B82F6" />
              <circle cx="240" cy="130" r="4" fill="#3B82F6" />
              <circle cx="300" cy="100" r="4" fill="#3B82F6" />
              <circle cx="360" cy="110" r="4" fill="#3B82F6" />
              <circle cx="420" cy="80" r="4" fill="#3B82F6" />
              <circle cx="480" cy="100" r="4" fill="#3B82F6" />
              <circle cx="540" cy="60" r="4" fill="#3B82F6" />
              
              <!-- X-axis labels -->
              <g font-size="12" fill="#6B7280" text-anchor="middle">
                <text x="60" y="195">1 May</text>
                <text x="120" y="195">5 May</text>
                <text x="180" y="195">10 May</text>
                <text x="240" y="195">15 May</text>
                <text x="300" y="195">20 May</text>
                <text x="360" y="195">25 May</text>
                <text x="420" y="195">25 May</text>
                <text x="480" y="195">30 May</text>
              </g>
            </svg>
          </div>
        </div>

        <!-- Notifications -->
        <div class="px-10 pb-10">
          <h2 class="text-xl font-bold text-[#162456] mb-6 flex items-center gap-2">
            <span>
              <svg width="20" height="20" fill="none" viewBox="0 0 24 24">
                <path d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V4a2 2 0 10-4 0v1.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" stroke="#162456" stroke-width="2" fill="none"/>
              </svg>
            </span>
            Notification
          </h2>
          <div class="space-y-4">
            <div class="bg-[#FFF7E6] rounded-xl p-4 flex items-start gap-4 border border-[#FFE2B3] hover:shadow-md transition-shadow">
              <div class="flex-shrink-0 w-10 h-10 bg-[#d08700] rounded-full flex items-center justify-center">
                <svg width="20" height="20" fill="white" viewBox="0 0 24 24">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
              </div>
              <div class="flex-1">
                <div class="font-semibold text-[#d08700] text-sm">A new seasonal competition has been announced for next summer.</div>
                <div class="text-xs text-[#99a1af] mt-2">09-05-2026</div>
              </div>
            </div>
            
            <div class="bg-[#DBFCE7] rounded-xl p-4 flex items-start gap-4 border border-[#B6F5D3] hover:shadow-md transition-shadow">
              <div class="flex-shrink-0 w-10 h-10 bg-[#00A63E] rounded-full flex items-center justify-center">
                <svg width="16" height="16" fill="white" viewBox="0 0 24 24">
                  <path d="M20.285 2l-11.285 11.567-5.286-5.011-3.714 3.716 9 8.728 15-15.285z"/>
                </svg>
              </div>
              <div class="flex-1">
                <div class="font-semibold text-[#00A63E] text-sm">congratulations , You passed the quiz</div>
                <div class="text-xs text-[#99a1af] mt-2">10-02-2024</div>
              </div>
            </div>
            
            <div class="bg-[#F8FAFC] rounded-xl p-4 flex items-start gap-4 border border-[#E2E8F0] hover:shadow-md transition-shadow">
              <div class="flex-shrink-0 w-10 h-10 bg-[#162456] rounded-full flex items-center justify-center">
                <svg width="16" height="16" fill="white" viewBox="0 0 24 24">
                  <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                </svg>
              </div>
              <div class="flex-1">
                <div class="font-semibold text-[#162456] text-sm">A new lesson has been added to the level that you can listen to.</div>
                <div class="text-xs text-[#99a1af] mt-2">09-02-2024</div>
              </div>
            </div>
            
            <div class="bg-[#DBFCE7] rounded-xl p-4 flex items-start gap-4 border border-[#B6F5D3] hover:shadow-md transition-shadow">
              <div class="flex-shrink-0 w-10 h-10 bg-[#00A63E] rounded-full flex items-center justify-center">
                <svg width="16" height="16" fill="white" viewBox="0 0 24 24">
                  <path d="M20.285 2l-11.285 11.567-5.286-5.011-3.714 3.716 9 8.728 15-15.285z"/>
                </svg>
              </div>
              <div class="flex-1">
                <div class="font-semibold text-[#00A63E] text-sm">You have been accepted into the first level. You can now pay.</div>
                <div class="text-xs text-[#99a1af] mt-2">08-02-2024</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Courses Content -->
      <div v-if="activeSection === 'courses'" class="px-10 pt-8 pb-10">
        <div class="flex items-center justify-between mb-8">
          <h1 class="text-4xl font-extrabold text-[#043355]">Courses</h1>
          <!-- WhatsApp Contact Button -->
          <a 
            href="https://wa.me/1234567890?text=Hello%2C%20I%20need%20help%20with%20the%20courses" 
            target="_blank"
            class="bg-green-500 text-white px-6 py-3 rounded-full font-semibold hover:bg-green-600 transition-all duration-300 flex items-center gap-3 shadow-lg hover:shadow-xl transform hover:scale-105"
          >
            <svg width="20" height="20" fill="white" viewBox="0 0 24 24">
              <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.465 3.516z"/>
            </svg>
            Contact Support
          </a>
        </div>
        
        <div v-if="dashboardLoading" class="flex justify-center items-center py-12">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>

        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <!-- My Courses -->
          <div v-for="course in myCourses" :key="course.id" class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:scale-105">
            <div class="relative">
              <img :src="course.course_level?.course?.image || 'https://images.pexels.com/photos/3184307/pexels-photo-3184307.jpeg'" :alt="course.course_level?.title" class="w-full h-48 object-cover">
              <div class="absolute top-4 left-4">
                <span :class="getStatusBadgeClass(course.status)" class="px-3 py-1 rounded-full text-sm font-semibold">
                  {{ getStatusText(course.status) }}
                </span>
              </div>
              <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <button class="bg-white bg-opacity-80 rounded-full p-3 hover:bg-opacity-100 transition-all">
                  <svg width="24" height="24" fill="#043355" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                </button>
              </div>
            </div>
            <div class="p-6">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-orange-600">Level {{ course.course_level?.level_number }}</span>
                <div class="flex items-center gap-1">
                  <span class="text-yellow-500">★</span>
                  <span class="text-sm text-gray-600">(4.5)</span>
                </div>
              </div>
              <h3 class="text-xl font-bold text-gray-900 mb-3">{{ course.course_level?.title }}</h3>
              <p class="text-gray-600 text-sm mb-4">{{ course.course_level?.description }}</p>
              <div class="flex items-center justify-between">
                <span class="text-2xl font-bold text-gray-900">${{ course.course_level?.price }}</span>
                <button class="bg-yellow-500 text-white px-6 py-2 rounded-lg font-semibold hover:bg-yellow-600 transition-colors">
                  {{ course.status === 'active' ? 'Continue' : 'Start' }}
                </button>
              </div>
            </div>
          </div>

          <!-- No courses message -->
          <div v-if="myCourses.length === 0" class="col-span-full text-center py-12">
            <div class="text-gray-500 text-lg mb-4">You haven't enrolled in any courses yet.</div>
            <router-link to="/courses" class="bg-blue-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-600 transition-colors">
              Browse Courses
            </router-link>
          </div>

          <!-- Level 2 Course -->
          <div class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:scale-105">
            <div class="relative">
              <img src="https://images.pexels.com/photos/1181671/pexels-photo-1181671.jpeg" alt="Level 2" class="w-full h-48 object-cover">
              <div class="absolute top-4 left-4">
                <span class="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold">Locked</span>
              </div>
              <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <button class="bg-white bg-opacity-80 rounded-full p-3 hover:bg-opacity-100 transition-all">
                  <svg width="24" height="24" fill="#043355" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                </button>
              </div>
            </div>
            <div class="p-6">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-blue-600">Level 2</span>
                <div class="flex items-center gap-1">
                  <span class="text-yellow-500">★</span>
                  <span class="text-sm text-gray-600">(4.5)</span>
                </div>
              </div>
              <h3 class="text-xl font-bold text-gray-900 mb-3">Skill Builder</h3>
              <p class="text-gray-600 text-sm mb-4">Start your learning journey from scratch. This level is perfect for absolute beginners who want to build strong foundations step by step.</p>
              <div class="flex items-center justify-between">
                <span class="text-2xl font-bold text-gray-900">$15.00</span>
                <button class="bg-gray-400 text-white px-6 py-2 rounded-lg font-semibold cursor-not-allowed">
                  Start
                </button>
              </div>
            </div>
          </div>

          <!-- Level 3 Course -->
          <div class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:scale-105">
            <div class="relative">
              <img src="https://images.pexels.com/photos/4145197/pexels-photo-4145197.jpeg" alt="Level 3" class="w-full h-48 object-cover">
              <div class="absolute top-4 left-4">
                <span class="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold">Locked</span>
              </div>
              <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <button class="bg-white bg-opacity-80 rounded-full p-3 hover:bg-opacity-100 transition-all">
                  <svg width="24" height="24" fill="#043355" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                </button>
              </div>
            </div>
            <div class="p-6">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-green-600">Level 3</span>
                <div class="flex items-center gap-1">
                  <span class="text-yellow-500">★</span>
                  <span class="text-sm text-gray-600">(4.5)</span>
                </div>
              </div>
              <h3 class="text-xl font-bold text-gray-900 mb-3">Future Ready</h3>
              <p class="text-gray-600 text-sm mb-4">Start your learning journey from scratch. This level is perfect for absolute beginners who want to build strong foundations step by step.</p>
              <div class="flex items-center justify-between">
                <span class="text-2xl font-bold text-gray-900">$15.00</span>
                <button class="bg-gray-400 text-white px-6 py-2 rounded-lg font-semibold cursor-not-allowed">
                  Start
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Competition Content -->
      <div v-if="activeSection === 'competition'" class="px-10 pt-8 pb-10">
        <div class="flex items-center justify-between mb-8">
          <h1 class="text-4xl font-extrabold text-[#043355]">Competition</h1>
          <!-- Competition Filter Tabs -->
          <div class="flex bg-gray-100 rounded-lg p-1">
            <button 
              @click="activeCompetitionType = 'monthly'" 
              :class="activeCompetitionType === 'monthly' ? 'bg-yellow-400 text-white' : 'text-gray-600 hover:bg-white hover:shadow-sm'"
              class="px-6 py-2 rounded-lg font-semibold transition-all"
            >
              Monthly
            </button>
            <button 
              @click="activeCompetitionType = 'seasonal'" 
              :class="activeCompetitionType === 'seasonal' ? 'bg-yellow-400 text-white' : 'text-gray-600 hover:bg-white hover:shadow-sm'"
              class="px-6 py-2 rounded-lg font-semibold transition-all"
            >
              Seasonal
            </button>
          </div>
        </div>

        <!-- Monthly Competitions -->
        <div v-if="activeCompetitionType === 'monthly'" class="space-y-4">
          <div v-if="competitionsByType.monthly.length === 0" class="text-center py-8">
            <p class="text-gray-500">No monthly competitions available.</p>
          </div>

          <div v-for="competition in competitionsByType.monthly" :key="competition.id" class="block">
            <div class="bg-yellow-50 border border-yellow-200 rounded-xl p-4 flex items-center justify-between hover:shadow-md transition-shadow cursor-pointer">
              <div class="flex items-center gap-4">
                <div class="bg-yellow-400 rounded-full p-3">
                  <svg width="24" height="24" fill="white" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </div>
                <div>
                  <h3 class="text-lg font-bold text-gray-900">{{ competition.title }}</h3>
                  <p class="text-sm text-gray-600">{{ formatDate(competition.start_date) }}</p>
                  <p class="text-xs text-gray-500 mt-1">{{ competition.description }}</p>
                </div>
              </div>
              <div class="text-right">
                <span :class="getCompetitionStatusClass(competition.status)" class="px-3 py-1 rounded-full text-sm font-semibold">
                  {{ getCompetitionStatusText(competition.status) }}
                </span>
                <div class="text-xs text-gray-500 mt-1">Prize: {{ competition.prize }}</div>
              </div>
            </div>
          </div>

          <!-- Competition Item 2 -->
          <router-link to="/competition" class="block">
            <div class="bg-yellow-50 border border-yellow-200 rounded-xl p-4 flex items-center justify-between hover:shadow-md transition-shadow cursor-pointer">
              <div class="flex items-center gap-4">
                <div class="bg-yellow-400 rounded-full p-3">
                  <svg width="24" height="24" fill="white" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </div>
                <div>
                  <h3 class="text-lg font-bold text-gray-900">Monthly changes</h3>
                  <p class="text-sm text-gray-600">09-02-2024</p>
                </div>
              </div>
              <div class="text-right">
                <span class="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm font-semibold">Ongoing</span>
              </div>
            </div>
          </router-link>

          <!-- Competition Item 3 -->
          <router-link to="/competition" class="block">
            <div class="bg-yellow-50 border border-yellow-200 rounded-xl p-4 flex items-center justify-between hover:shadow-md transition-shadow cursor-pointer">
              <div class="flex items-center gap-4">
                <div class="bg-yellow-400 rounded-full p-3">
                  <svg width="24" height="24" fill="white" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </div>
                <div>
                  <h3 class="text-lg font-bold text-gray-900">Monthly changes</h3>
                  <p class="text-sm text-gray-600">09-02-2024</p>
                </div>
              </div>
              <div class="text-right">
                <span class="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm font-semibold">Ongoing</span>
              </div>
            </div>
          </router-link>

          <!-- Competition Item 4 -->
          <router-link to="/competition" class="block">
            <div class="bg-green-50 border border-green-200 rounded-xl p-4 flex items-center justify-between hover:shadow-md transition-shadow cursor-pointer">
              <div class="flex items-center gap-4">
                <div class="bg-yellow-400 rounded-full p-3">
                  <svg width="24" height="24" fill="white" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </div>
                <div>
                  <h3 class="text-lg font-bold text-gray-900">Monthly changes</h3>
                  <p class="text-sm text-gray-600">09-02-2024</p>
                </div>
              </div>
              <div class="text-right">
                <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-semibold">Finished</span>
              </div>
            </div>
          </router-link>

          <!-- Competition Item 5 -->
          <router-link to="/competition" class="block">
            <div class="bg-green-50 border border-green-200 rounded-xl p-4 flex items-center justify-between hover:shadow-md transition-shadow cursor-pointer">
              <div class="flex items-center gap-4">
                <div class="bg-yellow-400 rounded-full p-3">
                  <svg width="24" height="24" fill="white" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </div>
                <div>
                  <h3 class="text-lg font-bold text-gray-900">Monthly changes</h3>
                  <p class="text-sm text-gray-600">09-02-2024</p>
                </div>
              </div>
              <div class="text-right">
                <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-semibold">Finished</span>
              </div>
            </div>
          </router-link>

          <!-- Competition Item 6 -->
          <router-link to="/competition" class="block">
            <div class="bg-green-50 border border-green-200 rounded-xl p-4 flex items-center justify-between hover:shadow-md transition-shadow cursor-pointer">
              <div class="flex items-center gap-4">
                <div class="bg-yellow-400 rounded-full p-3">
                  <svg width="24" height="24" fill="white" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </div>
                <div>
                  <h3 class="text-lg font-bold text-gray-900">Monthly changes</h3>
                  <p class="text-sm text-gray-600">09-02-2024</p>
                </div>
              </div>
              <div class="text-right">
                <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-semibold">Finished</span>
              </div>
            </div>
          </router-link>
        </div>

        <!-- Seasonal Competitions -->
        <div v-if="activeCompetitionType === 'seasonal'" class="space-y-4">
          <div v-if="competitionsByType.seasonal.length === 0" class="text-center py-8">
            <p class="text-gray-500">No seasonal competitions available.</p>
          </div>

          <div v-for="competition in competitionsByType.seasonal" :key="competition.id" class="block">
            <div class="bg-blue-50 border border-blue-200 rounded-xl p-4 flex items-center justify-between hover:shadow-md transition-shadow cursor-pointer">
              <div class="flex items-center gap-4">
                <div class="bg-blue-500 rounded-full p-3">
                  <svg width="24" height="24" fill="white" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </div>
                <div>
                  <h3 class="text-lg font-bold text-gray-900">{{ competition.title }}</h3>
                  <p class="text-sm text-gray-600">{{ formatDate(competition.start_date) }}</p>
                  <p class="text-xs text-gray-500 mt-1">{{ competition.description }}</p>
                </div>
              </div>
              <div class="text-right">
                <span :class="getCompetitionStatusClass(competition.status)" class="px-3 py-1 rounded-full text-sm font-semibold">
                  {{ getCompetitionStatusText(competition.status) }}
                </span>
                <div class="text-xs text-gray-500 mt-1">Prize: {{ competition.prize }}</div>
              </div>
            </div>
          </div>

          <!-- Seasonal Competition 2 -->
          <router-link to="/competition" class="block">
            <div class="bg-purple-50 border border-purple-200 rounded-xl p-4 flex items-center justify-between hover:shadow-md transition-shadow cursor-pointer">
              <div class="flex items-center gap-4">
                <div class="bg-purple-500 rounded-full p-3">
                  <svg width="24" height="24" fill="white" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </div>
                <div>
                  <h3 class="text-lg font-bold text-gray-900">Winter Challenge</h3>
                  <p class="text-sm text-gray-600">12-12-2023</p>
                </div>
              </div>
              <div class="text-right">
                <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-semibold">Finished</span>
              </div>
            </div>
          </router-link>

          <!-- Seasonal Competition 3 -->
          <router-link to="/competition" class="block">
            <div class="bg-green-50 border border-green-200 rounded-xl p-4 flex items-center justify-between hover:shadow-md transition-shadow cursor-pointer">
              <div class="flex items-center gap-4">
                <div class="bg-green-500 rounded-full p-3">
                  <svg width="24" height="24" fill="white" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </div>
                <div>
                  <h3 class="text-lg font-bold text-gray-900">Spring Tournament</h3>
                  <p class="text-sm text-gray-600">20-03-2024</p>
                </div>
              </div>
              <div class="text-right">
                <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-semibold">Finished</span>
              </div>
            </div>
          </router-link>
        </div>

        <!-- View All Competitions Button -->
        <div class="mt-8 text-center">
          <router-link to="/competition" class="inline-block">
            <button class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
              View All Competitions
            </button>
          </router-link>
        </div>
      </div>

      <!-- Payment Content -->
      <div v-if="activeSection === 'payment'" class="px-10 pt-8 pb-10">
        <h1 class="text-4xl font-extrabold text-[#043355] mb-8">Payment</h1>
        
        <!-- Payment Status Tabs -->
        <div class="flex mb-8">
          <button 
            @click="activePaymentStatus = 'paid'"
            :class="activePaymentStatus === 'paid' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'"
            class="px-6 py-3 rounded-l-lg font-semibold transition-all"
          >
            Paid
          </button>
          <button 
            @click="activePaymentStatus = 'not-paid'"
            :class="activePaymentStatus === 'not-paid' ? 'bg-gray-100 text-gray-800' : 'bg-gray-50 text-gray-500'"
            class="px-6 py-3 rounded-r-lg font-semibold transition-all"
          >
            Not Paid
          </button>
        </div>

        <!-- Payment Items -->
        <div class="space-y-4">
          <!-- Paid Items -->
          <div v-if="activePaymentStatus === 'paid'">
            <!-- Level 1 -->
            <div class="bg-green-50 border border-green-200 rounded-xl p-6 flex items-center justify-between">
              <div class="flex items-center gap-4">
                <div class="bg-green-100 rounded-lg p-3">
                  <svg width="24" height="24" fill="none" viewBox="0 0 24 24">
                    <path d="M2 3h6a4 4 0 014 4v14a3 3 0 00-3-3H2V3z" stroke="#22c55e" stroke-width="2"/>
                    <path d="M22 3h-6a4 4 0 00-4 4v14a3 3 0 013-3h7V3z" stroke="#22c55e" stroke-width="2"/>
                  </svg>
                </div>
                <div>
                  <h3 class="text-xl font-bold text-gray-900">Level 1</h3>
                  <p class="text-sm text-gray-600">09-02-2025</p>
                </div>
              </div>
              <div class="text-right">
                <span class="bg-green-500 text-white px-4 py-2 rounded-lg font-bold text-lg">Paid</span>
              </div>
            </div>

            <!-- Level 2 -->
            <div class="bg-green-50 border border-green-200 rounded-xl p-6 flex items-center justify-between mt-4">
              <div class="flex items-center gap-4">
                <div class="bg-green-100 rounded-lg p-3">
                  <svg width="24" height="24" fill="none" viewBox="0 0 24 24">
                    <path d="M2 3h6a4 4 0 014 4v14a3 3 0 00-3-3H2V3z" stroke="#22c55e" stroke-width="2"/>
                    <path d="M22 3h-6a4 4 0 00-4 4v14a3 3 0 013-3h7V3z" stroke="#22c55e" stroke-width="2"/>
                  </svg>
                </div>
                <div>
                  <h3 class="text-xl font-bold text-gray-900">Level 2</h3>
                  <p class="text-sm text-gray-600">09-02-2025</p>
                </div>
              </div>
              <div class="text-right">
                <span class="bg-green-500 text-white px-4 py-2 rounded-lg font-bold text-lg">Paid</span>
              </div>
            </div>

            <!-- Monthly Competition -->
            <div class="bg-green-50 border border-green-200 rounded-xl p-6 flex items-center justify-between mt-4">
              <div class="flex items-center gap-4">
                <div class="bg-green-100 rounded-lg p-3">
                  <svg width="24" height="24" fill="#22c55e" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </div>
                <div>
                  <h3 class="text-xl font-bold text-gray-900">Monthly Competition</h3>
                  <p class="text-sm text-gray-600">09-02-2025</p>
                </div>
              </div>
              <div class="text-right">
                <span class="bg-green-500 text-white px-4 py-2 rounded-lg font-bold text-lg">Paid</span>
              </div>
            </div>

            <!-- Second Competition -->
            <div class="bg-green-50 border border-green-200 rounded-xl p-6 flex items-center justify-between mt-4">
              <div class="flex items-center gap-4">
                <div class="bg-green-100 rounded-lg p-3">
                  <svg width="24" height="24" fill="#22c55e" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </div>
                <div>
                  <h3 class="text-xl font-bold text-gray-900">Second Competition</h3>
                  <p class="text-sm text-gray-600">09-02-2025</p>
                </div>
              </div>
              <div class="text-right">
                <span class="bg-green-500 text-white px-4 py-2 rounded-lg font-bold text-lg">Paid</span>
              </div>
            </div>
          </div>

          <!-- Not Paid Items -->
          <div v-if="activePaymentStatus === 'not-paid'">
            <!-- Monthly Changes -->
            <div class="bg-red-50 border border-red-200 rounded-xl p-6 flex items-center justify-between">
              <div class="flex items-center gap-4">
                <div class="bg-yellow-100 rounded-lg p-3">
                  <svg width="24" height="24" fill="#FFC107" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </div>
                <div>
                  <h3 class="text-xl font-bold text-gray-900">Monthly changes</h3>
                  <p class="text-sm text-gray-600">09-02-2024</p>
                </div>
              </div>
              <div class="text-right">
                <button class="bg-red-500 text-white px-4 py-2 rounded-lg font-bold text-lg hover:bg-red-600 transition-colors">Pay</button>
              </div>
            </div>

            <!-- Level 2 -->
            <div class="bg-red-50 border border-red-200 rounded-xl p-6 flex items-center justify-between mt-4">
              <div class="flex items-center gap-4">
                <div class="bg-blue-100 rounded-lg p-3">
                  <svg width="24" height="24" fill="none" viewBox="0 0 24 24">
                    <path d="M2 3h6a4 4 0 014 4v14a3 3 0 00-3-3H2V3z" stroke="#3B82F6" stroke-width="2"/>
                    <path d="M22 3h-6a4 4 0 00-4 4v14a3 3 0 013-3h7V3z" stroke="#3B82F6" stroke-width="2"/>
                  </svg>
                </div>
                <div>
                  <h3 class="text-xl font-bold text-gray-900">Level 2</h3>
                  <p class="text-sm text-gray-600">09-02-2024</p>
                </div>
              </div>
              <div class="text-right">
                <button class="bg-red-500 text-white px-4 py-2 rounded-lg font-bold text-lg hover:bg-red-600 transition-colors">Pay</button>
              </div>
            </div>

            <!-- Second Competition -->
            <div class="bg-red-50 border border-red-200 rounded-xl p-6 flex items-center justify-between mt-4">
              <div class="flex items-center gap-4">
                <div class="bg-yellow-100 rounded-lg p-3">
                  <svg width="24" height="24" fill="#FFC107" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </div>
                <div>
                  <h3 class="text-xl font-bold text-gray-900">second competition</h3>
                  <p class="text-sm text-gray-600">09-02-2024</p>
                </div>
              </div>
              <div class="text-right">
                <button class="bg-red-500 text-white px-4 py-2 rounded-lg font-bold text-lg hover:bg-red-600 transition-colors">Pay</button>
              </div>
            </div>

            <!-- Level 1 -->
            <div class="bg-red-50 border border-red-200 rounded-xl p-6 flex items-center justify-between mt-4">
              <div class="flex items-center gap-4">
                <div class="bg-blue-100 rounded-lg p-3">
                  <svg width="24" height="24" fill="none" viewBox="0 0 24 24">
                    <path d="M2 3h6a4 4 0 014 4v14a3 3 0 00-3-3H2V3z" stroke="#3B82F6" stroke-width="2"/>
                    <path d="M22 3h-6a4 4 0 00-4 4v14a3 3 0 013-3h7V3z" stroke="#3B82F6" stroke-width="2"/>
                  </svg>
                </div>
                <div>
                  <h3 class="text-xl font-bold text-gray-900">Level 1</h3>
                  <p class="text-sm text-gray-600">09-02-2024</p>
                </div>
              </div>
              <div class="text-right">
                <button class="bg-red-500 text-white px-4 py-2 rounded-lg font-bold text-lg hover:bg-red-600 transition-colors">Pay</button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Certificates Content -->
      <div v-if="activeSection === 'certificates'" class="px-10 pt-8 pb-10">
        <h1 class="text-4xl font-extrabold text-[#043355] mb-8">Your Certificates</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <!-- Certificate 1 -->
          <div class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:scale-105 relative">
            <div class="relative h-64 bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
              <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300" alt="Certificate" class="w-full h-full object-cover rounded-t-2xl" />
              <div class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
                <div class="text-center text-white">
                  <svg class="w-16 h-16 mx-auto mb-4 opacity-80" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  <h3 class="text-2xl font-bold">CERTIFICATE</h3>
                  <p class="text-sm opacity-90">Level 1 Completion</p>
                </div>
              </div>
            </div>
            <div class="p-6">
              <div class="flex items-center justify-between">
                <h3 class="text-xl font-bold text-gray-900">certificate for level 1</h3>
                <button class="bg-yellow-400 text-white p-3 rounded-full hover:bg-yellow-500 transition-colors">
                  <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <!-- Certificate 2 -->
          <div class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:scale-105 relative">
            <div class="relative h-64 bg-gradient-to-br from-amber-50 to-yellow-100 flex items-center justify-center">
              <img src="https://images.unsplash.com/photo-1589829545856-d10d557cf95f?w=400&h=300" alt="Certificate" class="w-full h-full object-cover rounded-t-2xl" />
              <div class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
                <div class="text-center text-white">
                  <svg class="w-16 h-16 mx-auto mb-4 opacity-80" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.286 3.967a1 1 0 00.95.69h4.178c.969 0 1.371 1.24.588 1.81l-3.385 2.46a1 1 0 00-.364 1.118l1.287 3.966c.3.922-.755 1.688-1.54 1.118l-3.385-2.46a1 1 0 00-1.175 0l-3.385 2.46c-.784.57-1.838-.196-1.54-1.118l1.287-3.966a1 1 0 00-.364-1.118l-3.385-2.46c-.783-.57-.38-1.81.588-1.81h4.178a1 1 0 00.95-.69l1.286-3.967z"/>
                  </svg>
                  <h3 class="text-2xl font-bold">CERTIFICATE</h3>
                  <p class="text-sm opacity-90">Excellence Award</p>
                </div>
              </div>
            </div>
            <div class="p-6">
              <div class="flex items-center justify-between">
                <h3 class="text-xl font-bold text-gray-900">certificate for level 1</h3>
                <button class="bg-yellow-400 text-white p-3 rounded-full hover:bg-yellow-500 transition-colors">
                  <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <!-- Certificate 3 -->
          <div class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:scale-105 relative">
            <div class="relative h-64 bg-gradient-to-br from-red-50 to-orange-100 flex items-center justify-center">
              <img src="https://images.unsplash.com/photo-1606092195730-5d7b9af1efc5?w=400&h=300" alt="Certificate" class="w-full h-full object-cover rounded-t-2xl" />
              <div class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
                <div class="text-center text-white">
                  <svg class="w-16 h-16 mx-auto mb-4 opacity-80" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                  </svg>
                  <h3 class="text-2xl font-bold">CERTIFICATE</h3>
                  <p class="text-sm opacity-90">Achievement Badge</p>
                </div>
              </div>
            </div>
            <div class="p-6">
              <div class="flex items-center justify-between">
                <h3 class="text-xl font-bold text-gray-900">certificate for level 1</h3>
                <button class="bg-yellow-400 text-white p-3 rounded-full hover:bg-yellow-500 transition-colors">
                  <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <!-- Certificate 4 -->
          <div class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:scale-105 relative">
            <div class="relative h-64 bg-gradient-to-br from-purple-50 to-pink-100 flex items-center justify-center">
              <img src="https://images.unsplash.com/photo-1552664730-d307ca884978?w=400&h=300" alt="Certificate" class="w-full h-full object-cover rounded-t-2xl" />
              <div class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
                <div class="text-center text-white">
                  <svg class="w-16 h-16 mx-auto mb-4 opacity-80" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                  </svg>
                  <h3 class="text-2xl font-bold">CERTIFICATE</h3>
                  <p class="text-sm opacity-90">Honor Roll</p>
                </div>
              </div>
            </div>
            <div class="p-6">
              <div class="flex items-center justify-between">
                <h3 class="text-xl font-bold text-gray-900">certificate for level 1</h3>
                <button class="bg-yellow-400 text-white p-3 rounded-full hover:bg-yellow-500 transition-colors">
                  <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Settings Content -->
      <div v-if="activeSection === 'settings'" class="bg-[#F9FAFB] min-h-screen">
        <!-- Settings Profile Section -->
        <div class="bg-white mx-10 my-8 rounded-[24px] shadow-sm border border-[#E5E7EB]">
          <!-- Profile Header with centered image -->
          <div class="flex flex-col items-center pt-12 pb-8">
            <div class="relative mb-6">
              <img 
                src="https://randomuser.me/api/portraits/women/44.jpg" 
                alt="Holly Ephraim" 
                class="w-32 h-32 rounded-full object-cover border-4 border-white shadow-lg"
              />
            </div>
            <h2 class="text-3xl font-bold text-[#043355] mb-2">Holly Ephraim</h2>
            <p class="text-lg text-gray-600">Student</p>
          </div>

          <!-- Form Fields Section -->
          <div class="px-12 pb-12">
            <div class="space-y-6">
              <!-- First Name -->
              <div>
                <label class="block text-sm font-semibold text-gray-700 mb-3">First name</label>
                <input 
                  type="text" 
                  value="Holly"
                  class="w-full px-6 py-4 bg-[#F8FAFC] border border-[#E2E8F0] rounded-[16px] text-gray-900 text-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                />
              </div>

              <!-- Last Name -->
              <div>
                <label class="block text-sm font-semibold text-gray-700 mb-3">last name</label>
                <input 
                  type="text" 
                  value="Ephraim"
                  class="w-full px-6 py-4 bg-[#F8FAFC] border border-[#E2E8F0] rounded-[16px] text-gray-900 text-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                />
              </div>

              <!-- Phone Number -->
              <div>
                <label class="block text-sm font-semibold text-gray-700 mb-3">Phone Number</label>
                <input 
                  type="tel" 
                  value="+966437458493"
                  class="w-full px-6 py-4 bg-[#F8FAFC] border border-[#E2E8F0] rounded-[16px] text-gray-900 text-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                />
              </div>

              <!-- Father Number -->
              <div>
                <label class="block text-sm font-semibold text-gray-700 mb-3">Father Number</label>
                <input 
                  type="tel" 
                  value="+966437458493"
                  class="w-full px-6 py-4 bg-[#F8FAFC] border border-[#E2E8F0] rounded-[16px] text-gray-900 text-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                />
              </div>

              <!-- Email -->
              <div>
                <label class="block text-sm font-semibold text-gray-700 mb-3">Email</label>
                <input 
                  type="email" 
                  value="<EMAIL>"
                  class="w-full px-6 py-4 bg-[#F8FAFC] border border-[#E2E8F0] rounded-[16px] text-gray-900 text-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                />
              </div>

              <!-- Password -->
              <div>
                <label class="block text-sm font-semibold text-gray-700 mb-3">Password</label>
                <input 
                  type="password"
                  value="••••••••••••"
                  class="w-full px-6 py-4 bg-[#F8FAFC] border border-[#E2E8F0] rounded-[16px] text-gray-900 text-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                />
              </div>
            </div>

            <!-- Save Changes Button -->
            <div class="flex justify-end mt-10">
              <button class="bg-[#FFC107] hover:bg-[#FFB300] text-white px-10 py-4 rounded-[16px] font-bold text-lg transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-105">
                Save Changes
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Contact & Help Content -->
      <div v-if="activeSection === 'contact'" class="px-10 pt-8 pb-10">
        <div class="max-w-4xl mx-auto">
          <h1 class="text-4xl font-extrabold text-[#043355] mb-12 text-center">Contact Us</h1>
          
          <!-- Contact Information Text -->
          <div class="bg-white rounded-2xl shadow-lg p-12 mb-8">
            <div class="text-center space-y-6">
              <p class="text-gray-700 text-lg leading-relaxed max-w-3xl mx-auto">
                هل تحتاج إلى مساعدة؟ فريق دعم العملاء لدينا هنا لمساعدتك. نحن نقدر وقتك ونريد أن نجعل تجربتك معنا سهلة وممتعة قدر الإمكان. سواء كنت بحاجة إلى مساعدة في استخدام المنصة، أو لديك استفسارات حول الدورات، أو تواجه أي مشاكل تقنية، فلا تتردد في التواصل معنا.
              </p>
              
              <p class="text-gray-700 text-lg leading-relaxed max-w-3xl mx-auto">
                يمكنك الوصول إلينا بسهولة عبر الواتساب للحصول على دعم سريع ومباشر. فريقنا متاح للرد على استفساراتكم ومساعدتكم في أي وقت. نحن ملتزمون بتقديم أفضل خدمة ودعم لطلابنا لضمان حصولكم على أفضل تجربة تعليمية.
              </p>
              
              <p class="text-gray-700 text-lg leading-relaxed max-w-3xl mx-auto">
                لا تتردد في التواصل معنا إذا كان لديك أي أسئلة أو احتجت إلى مساعدة. نحن هنا من أجلكم!
              </p>
            </div>
          </div>
          
          <!-- Contact Button -->
          <div class="text-center">
            <a 
              href="https://wa.me/1234567890?text=Hello%2C%20I%20need%20help%20and%20support" 
              target="_blank"
              class="inline-flex items-center gap-3 bg-green-500 text-white px-12 py-4 rounded-full text-xl font-bold hover:bg-green-600 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105"
            >
              <svg width="28" height="28" fill="white" viewBox="0 0 24 24">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.465 3.516z"/>
              </svg>
              Contact with Support
            </a>
          </div>
          
          <!-- Additional Contact Methods (Optional) -->
          <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-white rounded-xl shadow-md p-6 text-center hover:shadow-lg transition-shadow">
              <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg width="32" height="32" fill="#3B82F6" viewBox="0 0 24 24">
                  <path d="M21 15a2 2 0 01-2 2H7l-4 4V5a2 2 0 012-2h14a2 2 0 012 2v10z" stroke="#3B82F6" stroke-width="2" fill="none"/>
                  <path d="M13 11h0" stroke="#3B82F6" stroke-width="2"/>
                  <path d="M9 11h0" stroke="#3B82F6" stroke-width="2"/>
                  <path d="M17 11h0" stroke="#3B82F6" stroke-width="2"/>
                </svg>
              </div>
              <h3 class="font-bold text-gray-900 mb-2">Live Chat</h3>
              <p class="text-gray-600 text-sm">Chat with our support team in real-time</p>
            </div>
            
            <div class="bg-white rounded-xl shadow-md p-6 text-center hover:shadow-lg transition-shadow">
              <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg width="32" height="32" fill="#22C55E" viewBox="0 0 24 24">
                  <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.465 3.516z"/>
                </svg>
              </div>
              <h3 class="font-bold text-gray-900 mb-2">WhatsApp</h3>
              <p class="text-gray-600 text-sm">Quick support via WhatsApp messaging</p>
            </div>
            
            <div class="bg-white rounded-xl shadow-md p-6 text-center hover:shadow-lg transition-shadow">
              <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg width="32" height="32" fill="#8B5CF6" viewBox="0 0 24 24">
                  <path d="M21 12.79A9 9 0 1 1 11.21 3a7 7 0 0 0 9.79 9.79Z" stroke="#8B5CF6" stroke-width="2" fill="none"/>
                </svg>
              </div>
              <h3 class="font-bold text-gray-900 mb-2">24/7 Support</h3>
              <p class="text-gray-600 text-sm">Round-the-clock assistance available</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Profile Content -->
      <div v-if="activeSection === 'profile'" class="px-10 pt-8 pb-10">
        <h1 class="text-4xl font-extrabold text-[#043355] mb-8">Profile</h1>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <!-- Profile Card -->
          <div class="lg:col-span-1">
            <div class="bg-white rounded-2xl shadow-lg p-6 text-center">
              <div class="relative inline-block mb-4">
                <img :src="userAvatar" :alt="userDisplayName" class="w-24 h-24 rounded-full object-cover border-4 border-blue-100 mx-auto" />
                <button class="absolute bottom-0 right-0 bg-blue-500 text-white rounded-full p-2 hover:bg-blue-600 transition-colors">
                  <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
                  </svg>
                </button>
              </div>
              <h2 class="text-xl font-bold text-gray-900 mb-2">{{ userDisplayName }}</h2>
              <p class="text-gray-600 mb-2">{{ currentUser?.email }}</p>
              <span class="inline-block px-3 py-1 text-sm font-medium rounded-full" :class="userRoleBadgeClass">
                {{ userRoleDisplay }}
              </span>
              <div class="mt-4 pt-4 border-t border-gray-200">
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Member since</span>
                  <span class="font-medium">{{ memberSince }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Profile Information -->
          <div class="lg:col-span-2">
            <div class="bg-white rounded-2xl shadow-lg p-6">
              <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-bold text-gray-900">Personal Information</h3>
                <button @click="isEditing = !isEditing" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                  {{ isEditing ? 'Cancel' : 'Edit Profile' }}
                </button>
              </div>

              <form @submit.prevent="saveProfile" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <!-- First Name -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                    <input
                      v-model="profileForm.first_name"
                      :disabled="!isEditing"
                      type="text"
                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500"
                    />
                  </div>

                  <!-- Last Name -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                    <input
                      v-model="profileForm.second_name"
                      :disabled="!isEditing"
                      type="text"
                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500"
                    />
                  </div>

                  <!-- Email -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                    <input
                      v-model="profileForm.email"
                      :disabled="!isEditing"
                      type="email"
                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500"
                    />
                  </div>

                  <!-- Age -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Age</label>
                    <input
                      v-model="profileForm.age"
                      :disabled="!isEditing"
                      type="number"
                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500"
                    />
                  </div>

                  <!-- Phone -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                    <input
                      v-model="profileForm.phone"
                      :disabled="!isEditing"
                      type="tel"
                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500"
                    />
                  </div>

                  <!-- Address -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                    <input
                      v-model="profileForm.address"
                      :disabled="!isEditing"
                      type="text"
                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500"
                    />
                  </div>
                </div>

                <!-- Bio -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Bio</label>
                  <textarea
                    v-model="profileForm.bio"
                    :disabled="!isEditing"
                    rows="4"
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500"
                    placeholder="Tell us about yourself..."
                  ></textarea>
                </div>

                <!-- Save Button -->
                <div v-if="isEditing" class="flex justify-end">
                  <button type="submit" class="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors">
                    Save Changes
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>

      <!-- Payment Content -->
      <div v-if="activeSection === 'payment'" class="px-10 pt-8 pb-10">
        <h1 class="text-4xl font-extrabold text-[#043355] mb-8">My Payments</h1>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
          <!-- Payment Stats -->
          <div class="bg-white rounded-2xl shadow-lg p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-600">Total Paid</p>
                <p class="text-3xl font-bold text-green-600">${{ totalPaid }}</p>
              </div>
              <div class="bg-green-100 p-3 rounded-full">
                <svg width="24" height="24" fill="#10B981" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-2xl shadow-lg p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-600">Pending</p>
                <p class="text-3xl font-bold text-orange-600">${{ totalPending }}</p>
              </div>
              <div class="bg-orange-100 p-3 rounded-full">
                <svg width="24" height="24" fill="#F59E0B" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                </svg>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-2xl shadow-lg p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-600">Total Payments</p>
                <p class="text-3xl font-bold text-blue-600">{{ myPayments.length }}</p>
              </div>
              <div class="bg-blue-100 p-3 rounded-full">
                <svg width="24" height="24" fill="#3B82F6" viewBox="0 0 20 20">
                  <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"/>
                </svg>
              </div>
            </div>
          </div>
        </div>

        <!-- Payment History -->
        <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Payment History</h3>
          </div>

          <div v-if="myPayments.length === 0" class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No payments yet</h3>
            <p class="mt-1 text-sm text-gray-500">Your payment history will appear here once you make payments.</p>
          </div>

          <div v-else class="overflow-x-auto">
            <table class="w-full">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Course</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Receipt</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="payment in myPayments" :key="payment.id" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">{{ payment.level?.title }}</div>
                    <div class="text-sm text-gray-500">{{ payment.level?.course?.title }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-bold text-gray-900">${{ payment.amount }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span :class="payment.is_confirmed ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800'" class="px-2 py-1 text-xs font-semibold rounded-full">
                      {{ payment.is_confirmed ? 'Confirmed' : 'Pending' }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ formatDate(payment.created_at) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      v-if="payment.transaction_pic"
                      @click="viewReceipt(payment.transaction_pic)"
                      class="text-blue-600 hover:text-blue-900 transition-colors"
                    >
                      View Receipt
                    </button>
                    <span v-else class="text-gray-400">No receipt</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Placeholder for other sections -->
      <div v-if="activeSection !== 'dashboard' && activeSection !== 'courses' && activeSection !== 'competition' && activeSection !== 'certificates' && activeSection !== 'payment' && activeSection !== 'contact' && activeSection !== 'settings' && activeSection !== 'profile'" class="px-10 pt-8 pb-10">
        <h1 class="text-4xl font-extrabold text-[#043355] mb-8 capitalize">{{ activeSection }}</h1>
        <div class="bg-white rounded-2xl shadow p-8 text-center">
          <p class="text-gray-500 text-lg">This section is coming soon...</p>
        </div>
      </div>
    </main>

    <!-- Logout Confirmation Modal -->
    <div v-if="showLogoutConfirm" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4">
        <!-- Header -->
        <div class="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 class="text-xl font-bold text-gray-900">Confirm Logout</h2>
          <button @click="showLogoutConfirm = false" class="text-gray-400 hover:text-gray-600 transition-colors">
            <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </div>

        <!-- Content -->
        <div class="p-6">
          <div class="flex items-center gap-4 mb-6">
            <div class="bg-red-100 p-3 rounded-full">
              <svg width="24" height="24" fill="#EF4444" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-gray-900">Are you sure you want to logout?</h3>
              <p class="text-sm text-gray-600 mt-1">You will be redirected to the home page and will need to login again to access your dashboard.</p>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex gap-3">
            <button
              @click="showLogoutConfirm = false"
              class="flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
            >
              Cancel
            </button>
            <button
              @click="handleLogout"
              :disabled="isLoggingOut"
              class="flex-1 px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:bg-red-400 disabled:cursor-not-allowed transition-colors font-medium"
            >
              {{ isLoggingOut ? 'Logging out...' : 'Yes, Logout' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useStudentDashboard } from '../../composables/useStudentDashboard'
import { useCourses } from '../../composables/useCourses'
import { useCompetitions } from '../../composables/useCompetitions'
import { usePayments } from '../../composables/usePayments'
import { useAuth } from '../../composables/useAuth'

const router = useRouter()

// Use composables
const {
  progress,
  certificates,
  myCourses,
  notifications,
  dashboardSummary,
  isLoading: dashboardLoading,
  fetchDashboardData,
  markNotificationAsRead,
  markAllNotificationsAsRead
} = useStudentDashboard()

const {
  courses,
  activeCourses,
  fetchCourses
} = useCourses()

const {
  competitions,
  competitionsByType,
  fetchCompetitions
} = useCompetitions()

const {
  payments: myPayments,
  fetchStudentPayments
} = usePayments()

const {
  logout,
  isLoading: isAuthLoading
} = useAuth()

// حالة البطاقة النشطة في الداشبورد
const activeCard = ref('certificates') // افتراضياً البطاقة الأولى نشطة

// حالة القسم النشط في الشريط الجانبي
const activeSection = ref('dashboard') // افتراضياً الداشبورد نشط
const showPassword = ref(false);

// حالة نوع المسابقة النشطة
const activeCompetitionType = ref('monthly') // افتراضياً المسابقات الشهرية نشطة

// حالة حالة الدفع النشطة
const activePaymentStatus = ref('paid') // افتراضياً عرض المدفوعات

// Profile editing state
const isEditing = ref(false)

// Logout modal state
const showLogoutConfirm = ref(false)
const isLoggingOut = ref(false)

// Get current user data
const currentUser = computed(() => {
  const userStr = localStorage.getItem('user')
  return userStr ? JSON.parse(userStr) : null
})

const userDisplayName = computed(() => {
  if (!currentUser.value) return 'Guest User'
  return `${currentUser.value.first_name} ${currentUser.value.second_name}`
})

const userRoleDisplay = computed(() => {
  if (!currentUser.value) return 'Guest'
  const roleMap = {
    'superAdmin': 'Super Admin',
    'admin': 'Admin',
    'teacher': 'Teacher',
    'student': 'Student',
    'guest': 'Guest'
  }
  return roleMap[currentUser.value.role] || currentUser.value.role
})

const userRoleBadgeClass = computed(() => {
  if (!currentUser.value) return 'bg-gray-100 text-gray-800'
  const roleClasses = {
    'superAdmin': 'bg-purple-100 text-purple-800',
    'admin': 'bg-red-100 text-red-800',
    'teacher': 'bg-blue-100 text-blue-800',
    'student': 'bg-green-100 text-green-800',
    'guest': 'bg-gray-100 text-gray-800'
  }
  return roleClasses[currentUser.value.role] || 'bg-gray-100 text-gray-800'
})

const userAvatar = computed(() => {
  if (currentUser.value?.profile?.profile_picture) {
    return currentUser.value.profile.profile_picture
  }
  // Generate avatar based on user initials
  const initials = userDisplayName.value.split(' ').map(n => n[0]).join('')
  return `https://ui-avatars.com/api/?name=${encodeURIComponent(userDisplayName.value)}&background=3B82F6&color=fff&size=200`
})

const memberSince = computed(() => {
  if (!currentUser.value?.created_at) return 'Recently'
  const date = new Date(currentUser.value.created_at)
  return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' })
})

// Profile form data
const profileForm = ref({
  first_name: '',
  second_name: '',
  email: '',
  age: '',
  phone: '',
  address: '',
  bio: ''
})

// Initialize profile form with current user data
const initializeProfileForm = () => {
  if (currentUser.value) {
    profileForm.value = {
      first_name: currentUser.value.first_name || '',
      second_name: currentUser.value.second_name || '',
      email: currentUser.value.email || '',
      age: currentUser.value.age || '',
      phone: currentUser.value.profile?.phone || '',
      address: currentUser.value.profile?.address || '',
      bio: currentUser.value.profile?.bio || ''
    }
  }
}

// Save profile changes
const saveProfile = async () => {
  try {
    // Here you would typically make an API call to update the user profile
    console.log('Saving profile:', profileForm.value)

    // Update localStorage with new data (temporary solution)
    const updatedUser = {
      ...currentUser.value,
      first_name: profileForm.value.first_name,
      second_name: profileForm.value.second_name,
      email: profileForm.value.email,
      age: profileForm.value.age,
      profile: {
        ...currentUser.value.profile,
        phone: profileForm.value.phone,
        address: profileForm.value.address,
        bio: profileForm.value.bio
      }
    }

    localStorage.setItem('user', JSON.stringify(updatedUser))
    isEditing.value = false

    // Show success message (you can implement a toast notification here)
    alert('Profile updated successfully!')

  } catch (error) {
    console.error('Error saving profile:', error)
    alert('Error updating profile. Please try again.')
  }
}

// Logout function
const handleLogout = async () => {
  isLoggingOut.value = true

  try {
    await logout()
    // The logout composable handles clearing localStorage and redirecting
    showLogoutConfirm.value = false
  } catch (error) {
    console.error('Logout failed:', error)
    // Even if logout fails on server, clear local storage and redirect
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user')
    router.push('/')
    showLogoutConfirm.value = false
  } finally {
    isLoggingOut.value = false
  }
}

// Helper methods
const getStatusBadgeClass = (status: string) => {
  const classes = {
    'active': 'bg-green-500 text-white',
    'inactive': 'bg-gray-500 text-white',
    'expired': 'bg-red-500 text-white'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-500 text-white'
}

const getStatusText = (status: string) => {
  const texts = {
    'active': 'Active',
    'inactive': 'Inactive',
    'expired': 'Expired'
  }
  return texts[status as keyof typeof texts] || status
}

const getCompetitionStatusClass = (status: string) => {
  const classes = {
    'upcoming': 'bg-blue-100 text-blue-800',
    'ongoing': 'bg-orange-100 text-orange-800',
    'completed': 'bg-green-100 text-green-800'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const getCompetitionStatusText = (status: string) => {
  const texts = {
    'upcoming': 'Upcoming',
    'ongoing': 'Ongoing',
    'completed': 'Completed'
  }
  return texts[status as keyof typeof texts] || status
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// Payment-related computed properties
const totalPaid = computed(() =>
  myPayments.value
    .filter(payment => payment.is_confirmed)
    .reduce((sum, payment) => sum + payment.amount, 0)
)

const totalPending = computed(() =>
  myPayments.value
    .filter(payment => !payment.is_confirmed)
    .reduce((sum, payment) => sum + payment.amount, 0)
)

const viewReceipt = (receiptUrl: string) => {
  window.open(receiptUrl, '_blank')
}

// Initialize on mount
onMounted(async () => {
  initializeProfileForm()

  // Load dashboard data
  await Promise.all([
    fetchDashboardData(),
    fetchCourses(),
    fetchCompetitions(),
    currentUser.value ? fetchStudentPayments(currentUser.value.id) : Promise.resolve()
  ])
})
</script>