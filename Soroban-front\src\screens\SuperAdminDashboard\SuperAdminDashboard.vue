<template>
  <div class="flex min-h-screen bg-[#E6EFFF]">
    <!-- Sidebar -->
    <aside class="w-64 bg-[#F6F9FF] flex flex-col justify-between border-r border-[#D1D5DC]">
      <div>
        <!-- Logo -->
        <div class="flex items-center gap-2 px-6 py-8">
          <img src="https://c.animaapp.com/mc5ppr8hKB91iA/img/logo-lerning-removebg-preview-1-1.png" alt="ELBARQ Logo" class="h-10 w-10 rounded" />
          <div>
            <div class="font-bold text-[#162456] text-lg leading-4">ELBARQ</div>
            <div class="text-xs text-[#d08700] font-semibold -mt-1">Soroban</div>
          </div>
        </div>

        <!-- Navigation -->
        <nav class="mt-2">
          <ul class="space-y-2 px-2">
            <li><a href="#" @click="activeSection = 'dashboard'" :class="activeSection === 'dashboard' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all"> 
              <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
              </svg>
              Dashboard
            </a></li>
            <li><a href="#" @click="activeSection = 'users'" :class="activeSection === 'users' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all"> 
              <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                <path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
              </svg>
              Users
            </a></li>
            <li><a href="#" @click="activeSection = 'competitions'" :class="activeSection === 'competitions' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all"> 
              <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
              Competitions
            </a></li>
            <li><a href="#" @click="activeSection = 'adult-training'" :class="activeSection === 'adult-training' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all"> 
              <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 14l9-5-9-5-9 5 9 5z M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"/>
              </svg>
              Adult Training
            </a></li>
          </ul>
        </nav>
      </div>

      <!-- Bottom Navigation -->
      <div class="mb-6 px-2 space-y-2">
        <a href="#" @click="activeSection = 'settings'" :class="activeSection === 'settings' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all">
          <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
            <path d="M11.828 2.25c-.916 0-1.699.663-1.85 1.567l-.091.549a.798.798 0 01-.517.608 7.45 7.45 0 00-.478.198.798.798 0 01-.796-.064l-.453-.324a1.875 1.875 0 00-2.416.2l-.243.243a1.875 1.875 0 00-.2 2.416l.324.453a.798.798 0 01.064.796 7.448 7.448 0 00-.198.478.798.798 0 01-.608.517l-.549.091A1.875 1.875 0 002.25 11.828v.344c0 .916.663 1.699 1.567 1.85l.549.091c.281.047.508.25.608.517.06.162.127.321.198.478a.798.798 0 01-.064.796l-.324.453a1.875 1.875 0 00.2 2.416l.243.243c.648.648 1.67.733 2.416.2l.453-.324a.798.798 0 01.796-.064c.157.071.316.138.478.198.267.1.47.327.517.608l.091.549c.151.904.934 1.567 1.85 1.567h.344c.916 0 1.699-.663 1.85-1.567l.091-.549a.798.798 0 01.517-.608 7.52 7.52 0 00.478-.198.798.798 0 01.796.064l.453.324a1.875 1.875 0 002.416-.2l.243-.243c.648-.648.733-1.67.2-2.416l-.324-.453a.798.798 0 01-.064-.796c.071-.157.138-.316.198-.478.1-.267.327-.47.608-.517l.549-.091c.904-.151 1.567-.934 1.567-1.85v-.344c0-.916-.663-1.699-1.567-1.85l-.549-.091a.798.798 0 01-.608-.517 7.507 7.507 0 00-.198-.478.798.798 0 01.064-.796l.324-.453a1.875 1.875 0 00-.2-2.416l-.243-.243a1.875 1.875 0 00-2.416-.2l-.453.324a.798.798 0 01-.796.064 7.462 7.462 0 00-.478-.198.798.798 0 01-.517-.608l-.091-.549A1.875 1.875 0 0012.172 2.25h-.344zM12 15.75a3.75 3.75 0 100-7.5 3.75 3.75 0 000 7.5z"/>
          </svg>
          Setting
        </a>
        <a href="/" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#e7000b] transition-all hover:bg-red-50">
          <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
            <path d="M9 21H5a2 2 0 01-2-2V5a2 2 0 012-2h4m7 14l5-5-5-5m5 5H9"/>
          </svg>
          Log out
        </a>
      </div>
    </aside>

    <!-- Main Content -->
    <main class="flex-1 min-h-screen">
      <!-- Header -->
      <div class="flex justify-end items-center px-10 py-4 bg-white shadow-sm border-b border-[#F3F3F3]" style="min-height:107px;">
        <div class="flex items-center gap-6">
          <span class="text-xl text-black"><svg width="20" height="20" fill="none" viewBox="0 0 24 24"><path d="M21 12.79A9 9 0 1 1 11.21 3a7 7 0 0 0 9.79 9.79Z" stroke="#000" stroke-width="2"/></svg></span>
          <span class="relative">
            <svg class="w-7 h-7 text-[#043355]" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V4a2 2 0 10-4 0v1.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/></svg>
            <span class="absolute -top-2 -right-2 bg-[#BC0000] text-white rounded-full w-5 h-5 flex items-center justify-center text-xs border-2 border-white">1</span>
          </span>
          <div class="flex items-center gap-2 bg-white border border-[#D1D5DC] rounded-full px-4 py-2">
            <img src="https://randomuser.me/api/portraits/men/32.jpg" class="w-8 h-8 rounded-full object-cover border border-[#D1D5DC]" />
            <span class="font-bold text-[#043355]">Gamal Yousef</span>
          </div>
        </div>
      </div>

      <!-- Dashboard Content -->
      <div v-if="activeSection === 'dashboard'" class="px-10 py-8">
        <!-- Analysis Section -->
        <div class="mb-8">
          <div class="flex items-center gap-2 mb-6">
            <svg width="24" height="24" fill="#043355" viewBox="0 0 24 24">
              <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
            </svg>
            <h2 class="text-xl font-bold text-[#043355]">Analysis</h2>
          </div>

          <!-- Stats Cards Row 1 -->
          <div class="grid grid-cols-4 gap-6 mb-8">
            <!-- Count Admins -->
            <button @click="viewAdmins()" class="bg-[#1e3a8a] rounded-2xl p-6 text-white hover:bg-[#1e40af] transition-all duration-300 transform hover:scale-105 cursor-pointer">
              <div class="flex flex-col">
                <h3 class="text-sm font-medium mb-2 opacity-90">Count Admins</h3>
                <div class="flex items-end gap-2">
                  <span class="text-4xl font-bold">300</span>
                  <span class="text-sm opacity-75">Admins</span>
                </div>
              </div>
            </button>

            <!-- Count Teachers -->
            <button @click="viewTeachers()" class="bg-[#0f3460] rounded-2xl p-6 text-white hover:bg-[#1e40af] transition-all duration-300 transform hover:scale-105 cursor-pointer">
              <div class="flex flex-col">
                <h3 class="text-sm font-medium mb-2 opacity-90">Count Teachers</h3>
                <div class="flex items-end gap-2">
                  <span class="text-4xl font-bold">3</span>
                  <span class="text-sm opacity-75">Teachers</span>
                </div>
              </div>
            </button>

            <!-- Count Teams -->
            <button @click="viewTeams()" class="bg-white rounded-2xl p-6 border border-[#E5E7EB] hover:bg-gray-50 hover:shadow-lg transition-all duration-300 transform hover:scale-105 cursor-pointer">
              <div class="flex flex-col">
                <h3 class="text-sm font-medium mb-2 text-gray-600">Count Teams</h3>
                <div class="flex items-end gap-2">
                  <span class="text-4xl font-bold text-[#043355]">10</span>
                  <span class="text-sm text-gray-500">Teams</span>
                </div>
              </div>
            </button>

            <!-- Count Groups -->
            <button @click="viewGroups()" class="bg-white rounded-2xl p-6 border border-[#E5E7EB] hover:bg-gray-50 hover:shadow-lg transition-all duration-300 transform hover:scale-105 cursor-pointer">
              <div class="flex flex-col">
                <h3 class="text-sm font-medium mb-2 text-gray-600">Count Groups</h3>
                <div class="flex items-end gap-2">
                  <span class="text-4xl font-bold text-blue-600">20</span>
                  <span class="text-sm text-gray-500">Groups</span>
                </div>
              </div>
            </button>
          </div>

          <!-- Charts Row -->
          <div class="grid grid-cols-3 gap-6 mb-8">
            <!-- Count Students Chart -->
            <button @click="viewStudents()" class="bg-[#1e3a8a] rounded-2xl p-6 text-white hover:bg-[#1e40af] transition-all duration-300 transform hover:scale-105 cursor-pointer">
              <h3 class="text-sm font-medium mb-4 opacity-90">Count Students</h3>
              <div class="flex items-center justify-center h-32">
                <div class="relative w-24 h-24">
                  <!-- Donut Chart -->
                  <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                    <!-- Background circle -->
                    <circle cx="50" cy="50" r="35" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="8"/>
                    <!-- Level 1 (Green) -->
                    <circle cx="50" cy="50" r="35" fill="none" stroke="#10B981" stroke-width="8" 
                            stroke-dasharray="70 150" stroke-dashoffset="0"/>
                    <!-- Level 2 (Red) -->
                    <circle cx="50" cy="50" r="35" fill="none" stroke="#EF4444" stroke-width="8" 
                            stroke-dasharray="40 180" stroke-dashoffset="-70"/>
                    <!-- Level 3 (Yellow) -->
                    <circle cx="50" cy="50" r="35" fill="none" stroke="#F59E0B" stroke-width="8" 
                            stroke-dasharray="60 160" stroke-dashoffset="-110"/>
                  </svg>
                  <!-- Center text -->
                  <div class="absolute inset-0 flex items-center justify-center">
                    <div class="text-center">
                      <div class="text-xl font-bold">700</div>
                      <div class="text-xs opacity-75">Students</div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- Legend -->
              <div class="mt-4 space-y-1">
                <div class="flex items-center gap-2 text-xs">
                  <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span>Level 1</span>
                </div>
                <div class="flex items-center gap-2 text-xs">
                  <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span>Level 2</span>
                </div>
                <div class="flex items-center gap-2 text-xs">
                  <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <span>Level 3</span>
                </div>
              </div>
            </button>

            <!-- Monthly Competitions -->
            <button @click="viewMonthlyCompetitions()" class="bg-white rounded-2xl p-6 border border-[#E5E7EB] hover:bg-gray-50 hover:shadow-lg transition-all duration-300 transform hover:scale-105 cursor-pointer">
              <h3 class="text-sm font-medium mb-4 text-gray-600">Monthly Competitions</h3>
              <div class="flex items-center justify-center h-32">
                <div class="relative w-24 h-24">
                  <!-- Donut Chart -->
                  <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                    <!-- Background circle -->
                    <circle cx="50" cy="50" r="35" fill="none" stroke="#F3F4F6" stroke-width="8"/>
                    <!-- Finished (Green) -->
                    <circle cx="50" cy="50" r="35" fill="none" stroke="#10B981" stroke-width="8" 
                            stroke-dasharray="80 140" stroke-dashoffset="0"/>
                    <!-- Pending (Yellow) -->
                    <circle cx="50" cy="50" r="35" fill="none" stroke="#F59E0B" stroke-width="8" 
                            stroke-dasharray="60 160" stroke-dashoffset="-80"/>
                  </svg>
                  <!-- Center text -->
                  <div class="absolute inset-0 flex items-center justify-center">
                    <div class="text-center">
                      <div class="text-xl font-bold text-[#043355]">20</div>
                      <div class="text-xs text-gray-500">Competitions</div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- Legend -->
              <div class="mt-4 space-y-1">
                <div class="flex items-center gap-2 text-xs">
                  <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span>Finished</span>
                </div>
                <div class="flex items-center gap-2 text-xs">
                  <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <span>Pending</span>
                </div>
              </div>
              <div class="text-center mt-2 text-xs text-gray-500">10</div>
            </button>

            <!-- Seasonal Competitions -->
            <button @click="viewSeasonalCompetitions()" class="bg-white rounded-2xl p-6 border border-[#E5E7EB] hover:bg-gray-50 hover:shadow-lg transition-all duration-300 transform hover:scale-105 cursor-pointer">
              <h3 class="text-sm font-medium mb-4 text-gray-600">Seasonal Competitions</h3>
              <div class="flex items-center justify-center h-32">
                <div class="relative w-24 h-24">
                  <!-- Donut Chart -->
                  <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                    <!-- Background circle -->
                    <circle cx="50" cy="50" r="35" fill="none" stroke="#F3F4F6" stroke-width="8"/>
                    <!-- Finished (Green) -->
                    <circle cx="50" cy="50" r="35" fill="none" stroke="#10B981" stroke-width="8" 
                            stroke-dasharray="90 130" stroke-dashoffset="0"/>
                    <!-- Pending (Yellow) -->
                    <circle cx="50" cy="50" r="35" fill="none" stroke="#F59E0B" stroke-width="8" 
                            stroke-dasharray="50 170" stroke-dashoffset="-90"/>
                  </svg>
                  <!-- Center text -->
                  <div class="absolute inset-0 flex items-center justify-center">
                    <div class="text-center">
                      <div class="text-xl font-bold text-[#043355]">15</div>
                      <div class="text-xs text-gray-500">Competitions</div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- Legend -->
              <div class="mt-4 space-y-1">
                <div class="flex items-center gap-2 text-xs">
                  <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span>Finished</span>
                </div>
                <div class="flex items-center gap-2 text-xs">
                  <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <span>Pending</span>
                </div>
              </div>
            </button>
          </div>

          <!-- Soroban Progress Chart -->
          <div class="bg-white rounded-2xl p-8 border border-[#E5E7EB]">
            <div class="flex items-center gap-2 mb-6">
              <svg width="20" height="20" fill="#043355" viewBox="0 0 24 24">
                <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22" stroke="#043355" stroke-width="2" fill="none"/>
              </svg>
              <h3 class="text-xl font-bold text-[#043355]">Soroban Progress</h3>
            </div>
            
            <!-- Chart Area -->
            <div class="h-80 relative">
              <svg class="w-full h-full" viewBox="0 0 800 300">
                <!-- Grid Lines -->
                <defs>
                  <pattern id="grid" width="100" height="50" patternUnits="userSpaceOnUse">
                    <path d="M 100 0 L 0 0 0 50" fill="none" stroke="#f3f4f6" stroke-width="1"/>
                  </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#grid)" />
                
                <!-- Y-axis labels -->
                <text x="30" y="50" font-size="12" fill="#6b7280">100</text>
                <text x="30" y="100" font-size="12" fill="#6b7280">80</text>
                <text x="30" y="150" font-size="12" fill="#6b7280">60</text>
                <text x="30" y="200" font-size="12" fill="#6b7280">40</text>
                <text x="30" y="250" font-size="12" fill="#6b7280">20</text>
                <text x="30" y="290" font-size="12" fill="#6b7280">0</text>
                
                <!-- X-axis labels -->
                <text x="100" y="320" font-size="12" fill="#6b7280">2024</text>
                <text x="200" y="320" font-size="12" fill="#6b7280">2025</text>
                <text x="400" y="320" font-size="12" fill="#6b7280">2026</text>
                <text x="600" y="320" font-size="12" fill="#6b7280">2027</text>
                <text x="700" y="320" font-size="12" fill="#6b7280">2028</text>
                
                <!-- Progress Line -->
                <polyline 
                  points="100,200 150,180 200,160 250,170 300,120 350,180 400,160 450,90 500,110 550,140 600,120 650,160 700,130 750,80"
                  fill="none" 
                  stroke="#8B5CF6" 
                  stroke-width="3"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                
                <!-- Data Points -->
                <circle cx="100" cy="200" r="4" fill="#8B5CF6"/>
                <circle cx="150" cy="180" r="4" fill="#8B5CF6"/>
                <circle cx="200" cy="160" r="4" fill="#8B5CF6"/>
                <circle cx="250" cy="170" r="4" fill="#8B5CF6"/>
                <circle cx="300" cy="120" r="4" fill="#8B5CF6"/>
                <circle cx="350" cy="180" r="4" fill="#8B5CF6"/>
                <circle cx="400" cy="160" r="4" fill="#8B5CF6"/>
                <circle cx="450" cy="90" r="4" fill="#8B5CF6"/>
                <circle cx="500" cy="110" r="4" fill="#8B5CF6"/>
                <circle cx="550" cy="140" r="4" fill="#8B5CF6"/>
                <circle cx="600" cy="120" r="4" fill="#8B5CF6"/>
                <circle cx="650" cy="160" r="4" fill="#8B5CF6"/>
                <circle cx="700" cy="130" r="4" fill="#8B5CF6"/>
                <circle cx="750" cy="80" r="4" fill="#8B5CF6"/>
              </svg>
            </div>
          </div>
        </div>
      </div>

      <!-- Users Section -->
      <div v-if="activeSection === 'users'" class="px-10 pt-8 pb-10">
        <div class="bg-white rounded-2xl border border-[#E5E7EB] overflow-hidden">
          <!-- Search Section -->
          <div class="px-8 pt-6 pb-4">
            <div class="flex items-center justify-between mb-6">
              <!-- Tabs -->
              <div class="flex gap-8">
                <button 
                  @click="activeUsersTab = 'admins'"
                  :class="activeUsersTab === 'admins' ? 'text-[#043355] border-b-2 border-[#043355] font-semibold' : 'text-gray-500'"
                  class="pb-2 transition-all"
                >
                  Admins
                </button>
                <button 
                  @click="activeUsersTab = 'teachers'"
                  :class="activeUsersTab === 'teachers' ? 'text-[#043355] border-b-2 border-[#043355] font-semibold' : 'text-gray-500'"
                  class="pb-2 transition-all"
                >
                  Teachers
                </button>
              </div>
            </div>

            <!-- Search Bar -->
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg width="20" height="20" fill="#9CA3AF" viewBox="0 0 24 24">
                  <path d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" stroke="currentColor" stroke-width="2" fill="none"/>
                </svg>
              </div>
              <input 
                v-model="searchQuery"
                type="text" 
                placeholder="Search for Admin"
                class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <svg width="20" height="20" fill="#9CA3AF" viewBox="0 0 24 24">
                  <circle cx="12" cy="12" r="3" fill="currentColor"/>
                </svg>
              </button>
            </div>
          </div>

          <!-- Admins List -->
          <div v-if="activeUsersTab === 'admins'" class="px-8 pb-8">
            <div class="space-y-4">
              <div v-for="admin in filteredAdmins" :key="admin.id" 
                   class="flex items-center justify-between py-4 border-b border-gray-100 last:border-b-0">
                <!-- Admin Info -->
                <div class="flex items-center gap-4">
                  <img :src="admin.avatar" :alt="admin.name" 
                       class="w-12 h-12 rounded-full object-cover border border-gray-200">
                  <div>
                    <h4 class="font-semibold text-[#043355]">{{ admin.name }}</h4>
                    <p class="text-sm text-gray-500">{{ admin.role }}</p>
                  </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex items-center gap-3">
                  <button @click="editAdmin(admin)" 
                          class="px-6 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-all font-medium">
                    Edit
                  </button>
                  <button @click="deleteAdmin(admin)" 
                          class="px-6 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-all font-medium">
                    Delete
                  </button>
                </div>
              </div>
            </div>

            <!-- Add Admin Button -->
            <div class="flex justify-end mt-8">
              <button @click="showAddAdminModal = true"
                      class="flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all font-medium">
                <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 4.5v15m7.5-7.5h-15"/>
                </svg>
                Add Admin
              </button>
            </div>
          </div>

          <!-- Teachers List -->
          <div v-if="activeUsersTab === 'teachers'" class="px-8 pb-8">
            <div class="space-y-4">
              <div v-for="teacher in filteredTeachers" :key="teacher.id" 
                   class="flex items-center justify-between py-4 border-b border-gray-100 last:border-b-0">
                <!-- Teacher Info -->
                <div class="flex items-center gap-4">
                  <img :src="teacher.avatar" :alt="teacher.name" 
                       class="w-12 h-12 rounded-full object-cover border border-gray-200">
                  <div>
                    <h4 class="font-semibold text-[#043355]">{{ teacher.name }}</h4>
                    <p class="text-sm text-gray-500">{{ teacher.subject }}</p>
                  </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex items-center gap-3">
                  <button @click="editTeacher(teacher)" 
                          class="px-6 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-all font-medium">
                    Edit
                  </button>
                  <button @click="deleteTeacher(teacher)" 
                          class="px-6 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-all font-medium">
                    Delete
                  </button>
                </div>
              </div>
            </div>

            <!-- Add Teacher Button -->
            <div class="flex justify-end mt-8">
              <button @click="showAddTeacherModal = true"
                      class="flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all font-medium">
                <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 4.5v15m7.5-7.5h-15"/>
                </svg>
                Add Teacher
              </button>
            </div>
          </div>
        </div>

        <!-- Add Admin Modal -->
        <div v-if="showAddAdminModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div class="bg-white rounded-xl p-8 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <h3 class="text-xl font-bold text-[#043355] mb-6">Add New Admin</h3>
            
            <div class="grid grid-cols-2 gap-6">
              <!-- Full Name -->
              <div>
                <label class="block text-sm font-medium text-[#043355] mb-2">Full Name</label>
                <input 
                  v-model="newAdmin.name" 
                  type="text" 
                  placeholder="Nada Mahammed"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
                >
              </div>
              
              <!-- Email -->
              <div>
                <label class="block text-sm font-medium text-[#043355] mb-2">Email</label>
                <input 
                  v-model="newAdmin.email" 
                  type="email" 
                  placeholder="<EMAIL>"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
                >
              </div>
              
              <!-- Phone Number -->
              <div>
                <label class="block text-sm font-medium text-[#043355] mb-2">Phone Number</label>
                <div class="flex gap-2">
                  <select v-model="newAdmin.countryCode" class="px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50">
                    <option value="+213">+213</option>
                    <option value="+966">+966</option>
                    <option value="+20">+20</option>
                    <option value="+971">+971</option>
                  </select>
                  <input 
                    v-model="newAdmin.phoneNumber" 
                    type="tel" 
                    placeholder="Phone number"
                    class="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
                  >
                </div>
              </div>
              
              <!-- Photo -->
              <div>
                <label class="block text-sm font-medium text-[#043355] mb-2">Photo</label>
                <div class="relative">
                  <input 
                    ref="photoInput"
                    type="file" 
                    accept="image/*"
                    @change="handlePhotoUpload"
                    class="hidden"
                  >
                  <button 
                    @click="$refs.photoInput.click()"
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50 text-left text-gray-500 flex items-center gap-2"
                  >
                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                    </svg>
                    {{ newAdmin.photo ? 'Photo selected' : 'Photo' }}
                  </button>
                </div>
              </div>
            </div>

            <!-- Description -->
            <div class="mt-6">
              <label class="block text-sm font-medium text-[#043355] mb-2">Description</label>
              <textarea 
                v-model="newAdmin.description" 
                rows="6"
                placeholder="isl. quis orci leo. ipsum vitae at quam lacus, tincidunt diam laoreet sapien eu quam ipsum quis quis non vitae lacus, lorem. malesuada cursus dignissim, est.

dolor odio scelerisque e"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50 resize-none"
              ></textarea>
            </div>

            <!-- Department Name -->
            <div class="mt-6">
              <label class="block text-sm font-medium text-[#043355] mb-2">Department Name</label>
              <input 
                v-model="newAdmin.department" 
                type="text" 
                placeholder="finance department"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
              >
            </div>
            
            <!-- Add Button -->
            <div class="flex justify-end mt-8">
              <button @click="addAdmin" class="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium">
                Add
              </button>
            </div>
          </div>
        </div>

        <!-- Edit Admin Modal -->
        <div v-if="showEditAdminModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div class="bg-white rounded-xl p-8 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <h3 class="text-xl font-bold text-[#043355] mb-6">Edit Admin</h3>
            
            <div class="grid grid-cols-2 gap-6">
              <!-- Full Name -->
              <div>
                <label class="block text-sm font-medium text-[#043355] mb-2">Full Name</label>
                <input 
                  v-model="editingAdmin.name" 
                  type="text" 
                  placeholder="Nada Mahammed"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
                >
              </div>
              
              <!-- Email -->
              <div>
                <label class="block text-sm font-medium text-[#043355] mb-2">Email</label>
                <input 
                  v-model="editingAdmin.email" 
                  type="email" 
                  placeholder="<EMAIL>"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
                >
              </div>
              
              <!-- Phone Number -->
              <div>
                <label class="block text-sm font-medium text-[#043355] mb-2">Phone Number</label>
                <div class="flex gap-2">
                  <select v-model="editingAdmin.countryCode" class="px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50">
                    <option value="+213">+213</option>
                    <option value="+966">+966</option>
                    <option value="+20">+20</option>
                    <option value="+971">+971</option>
                  </select>
                  <input 
                    v-model="editingAdmin.phoneNumber" 
                    type="tel" 
                    placeholder="Phone number"
                    class="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
                  >
                </div>
              </div>
              
              <!-- Photo -->
              <div>
                <label class="block text-sm font-medium text-[#043355] mb-2">Photo</label>
                <div class="relative">
                  <input 
                    ref="editPhotoInput"
                    type="file" 
                    accept="image/*"
                    @change="handleEditPhotoUpload"
                    class="hidden"
                  >
                  <button 
                    @click="$refs.editPhotoInput.click()"
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50 text-left text-gray-500 flex items-center gap-2"
                  >
                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                    </svg>
                    {{ editingAdmin.photo ? 'Photo selected' : 'Photo' }}
                  </button>
                </div>
              </div>
            </div>

            <!-- Description -->
            <div class="mt-6">
              <label class="block text-sm font-medium text-[#043355] mb-2">Description</label>
              <textarea 
                v-model="editingAdmin.description" 
                rows="6"
                placeholder="isl. quis orci leo. ipsum vitae at quam lacus, tincidunt diam laoreet sapien eu quam ipsum quis quis non vitae lacus, lorem. malesuada cursus dignissim, est.

dolor odio scelerisque e"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50 resize-none"
              ></textarea>
            </div>

            <!-- Department Name -->
            <div class="mt-6">
              <label class="block text-sm font-medium text-[#043355] mb-2">Department Name</label>
              <input 
                v-model="editingAdmin.department" 
                type="text" 
                placeholder="finance department"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
              >
            </div>
            
            <!-- Update Button -->
            <div class="flex justify-end mt-8">
              <button @click="updateAdmin" class="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium">
                Update
              </button>
            </div>
          </div>
        </div>

        <!-- Add Teacher Modal -->
        <div v-if="showAddTeacherModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div class="bg-white rounded-xl p-8 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <h3 class="text-xl font-bold text-[#043355] mb-6">Add New Teacher</h3>
            
            <div class="grid grid-cols-2 gap-6">
              <!-- Full Name -->
              <div>
                <label class="block text-sm font-medium text-[#043355] mb-2">Full Name</label>
                <input 
                  v-model="newTeacher.name" 
                  type="text" 
                  placeholder="Nada Mahammed"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
                >
              </div>
              
              <!-- Email -->
              <div>
                <label class="block text-sm font-medium text-[#043355] mb-2">Email</label>
                <input 
                  v-model="newTeacher.email" 
                  type="email" 
                  placeholder="<EMAIL>"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
                >
              </div>
              
              <!-- Phone Number -->
              <div>
                <label class="block text-sm font-medium text-[#043355] mb-2">Phone Number</label>
                <div class="flex gap-2">
                  <select v-model="newTeacher.countryCode" class="px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50">
                    <option value="+213">+213</option>
                    <option value="+966">+966</option>
                    <option value="+20">+20</option>
                    <option value="+971">+971</option>
                  </select>
                  <input 
                    v-model="newTeacher.phoneNumber" 
                    type="tel" 
                    placeholder="Phone number"
                    class="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
                  >
                </div>
              </div>
              
              <!-- Photo -->
              <div>
                <label class="block text-sm font-medium text-[#043355] mb-2">Photo</label>
                <div class="relative">
                  <input 
                    ref="teacherPhotoInput"
                    type="file" 
                    accept="image/*"
                    @change="handleTeacherPhotoUpload"
                    class="hidden"
                  >
                  <button 
                    @click="$refs.teacherPhotoInput.click()"
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50 text-left text-gray-500 flex items-center gap-2"
                  >
                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                    </svg>
                    {{ newTeacher.photo ? 'Photo selected' : 'Photo' }}
                  </button>
                </div>
              </div>

              <!-- Job Title -->
              <div>
                <label class="block text-sm font-medium text-[#043355] mb-2">Job Title</label>
                <input 
                  v-model="newTeacher.jobTitle" 
                  type="text" 
                  placeholder="math teacher"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
                >
              </div>

              <!-- Course Name -->
              <div>
                <label class="block text-sm font-medium text-[#043355] mb-2">Course Name</label>
                <input 
                  v-model="newTeacher.courseName" 
                  type="text" 
                  placeholder="Basic Material"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
                >
              </div>
            </div>

            <!-- Description -->
            <div class="mt-6">
              <label class="block text-sm font-medium text-[#043355] mb-2">Description</label>
              <textarea 
                v-model="newTeacher.description" 
                rows="6"
                placeholder="isl. quis orci leo. ipsum vitae at quam lacus, tincidunt diam laoreet sapien eu quam ipsum quis quis non vitae lacus, lorem. malesuada cursus dignissim, est.

dolor odio scelerisque e"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50 resize-none"
              ></textarea>
            </div>
            
            <!-- Add Button -->
            <div class="flex justify-end mt-8">
              <button @click="addTeacher" class="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium">
                Add
              </button>
            </div>
          </div>
        </div>

        <!-- Edit Teacher Modal -->
        <div v-if="showEditTeacherModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div class="bg-white rounded-xl p-8 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <h3 class="text-xl font-bold text-[#043355] mb-6">Edit Teacher</h3>
            
            <div class="grid grid-cols-2 gap-6">
              <!-- Full Name -->
              <div>
                <label class="block text-sm font-medium text-[#043355] mb-2">Full Name</label>
                <input 
                  v-model="editingTeacher.name" 
                  type="text" 
                  placeholder="Nada Mahammed"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
                >
              </div>
              
              <!-- Email -->
              <div>
                <label class="block text-sm font-medium text-[#043355] mb-2">Email</label>
                <input 
                  v-model="editingTeacher.email" 
                  type="email" 
                  placeholder="<EMAIL>"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
                >
              </div>
              
              <!-- Phone Number -->
              <div>
                <label class="block text-sm font-medium text-[#043355] mb-2">Phone Number</label>
                <div class="flex gap-2">
                  <select v-model="editingTeacher.countryCode" class="px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50">
                    <option value="+213">+213</option>
                    <option value="+966">+966</option>
                    <option value="+20">+20</option>
                    <option value="+971">+971</option>
                  </select>
                  <input 
                    v-model="editingTeacher.phoneNumber" 
                    type="tel" 
                    placeholder="Phone number"
                    class="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
                  >
                </div>
              </div>
              
              <!-- Photo -->
              <div>
                <label class="block text-sm font-medium text-[#043355] mb-2">Photo</label>
                <div class="relative">
                  <input 
                    ref="editTeacherPhotoInput"
                    type="file" 
                    accept="image/*"
                    @change="handleEditTeacherPhotoUpload"
                    class="hidden"
                  >
                  <button 
                    @click="$refs.editTeacherPhotoInput.click()"
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50 text-left text-gray-500 flex items-center gap-2"
                  >
                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                    </svg>
                    {{ editingTeacher.photo ? 'Photo selected' : 'Photo' }}
                  </button>
                </div>
              </div>

              <!-- Job Title -->
              <div>
                <label class="block text-sm font-medium text-[#043355] mb-2">Job Title</label>
                <input 
                  v-model="editingTeacher.jobTitle" 
                  type="text" 
                  placeholder="math teacher"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
                >
              </div>

              <!-- Course Name -->
              <div>
                <label class="block text-sm font-medium text-[#043355] mb-2">Course Name</label>
                <input 
                  v-model="editingTeacher.courseName" 
                  type="text" 
                  placeholder="Basic Material"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
                >
              </div>
            </div>

            <!-- Description -->
            <div class="mt-6">
              <label class="block text-sm font-medium text-[#043355] mb-2">Description</label>
              <textarea 
                v-model="editingTeacher.description" 
                rows="6"
                placeholder="isl. quis orci leo. ipsum vitae at quam lacus, tincidunt diam laoreet sapien eu quam ipsum quis quis non vitae lacus, lorem. malesuada cursus dignissim, est.

dolor odio scelerisque e"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50 resize-none"
              ></textarea>
            </div>
            
            <!-- Update Button -->
            <div class="flex justify-end mt-8">
              <button @click="updateTeacher" class="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium">
                Update
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Competitions Section -->
      <div v-if="activeSection === 'competitions'" class="px-10 pt-8 pb-10">
        <!-- Search Bar -->
        <div class="flex items-center justify-between mb-6">
          <div class="relative flex-1 max-w-md">
            <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
            <input 
              type="text" 
              placeholder="Search for Admin"
              class="w-full pl-10 pr-4 py-3 bg-white border border-[#D1D5DC] rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
            <svg class="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </div>
          
          <!-- Competition Type Tabs -->
          <div class="flex bg-gray-100 rounded-full p-1 ml-6">
            <button 
              @click="competitionType = 'monthly'"
              :class="competitionType === 'monthly' ? 'bg-yellow-400 text-black' : 'text-gray-600'"
              class="px-6 py-2 rounded-full font-medium transition-all"
            >
              Monthly
            </button>
            <button 
              @click="competitionType = 'seasonal'"
              :class="competitionType === 'seasonal' ? 'bg-blue-600 text-white' : 'text-gray-600'"
              class="px-6 py-2 rounded-full font-medium transition-all"
            >
              Seasonal
            </button>
          </div>
        </div>

        <!-- Competition Cards -->
        <div class="space-y-4">
          <!-- Competition Card 1 -->
          <div class="bg-white rounded-2xl p-6 border border-[#E5E7EB] flex items-center justify-between">
            <div class="flex items-center gap-4">
              <img 
                src="https://images.unsplash.com/photo-1509062522246-3755977927d7?w=80&h=80&fit=crop&crop=faces" 
                alt="May Math Madness" 
                class="w-20 h-20 rounded-xl object-cover"
              >
              <div>
                <h3 class="text-lg font-bold text-[#043355] mb-1">May Math Madness</h3>
                <p class="text-gray-600 text-sm mb-2">Solve 10 logic puzzles in 5 days...</p>
                <div class="flex items-center gap-4 text-sm text-gray-500">
                  <span class="flex items-center gap-1">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
                    </svg>
                    12 May 2025
                  </span>
                  <span class="flex items-center gap-1 text-yellow-600 font-medium">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                    Badge: Logic Sprinter
                  </span>
                </div>
              </div>
            </div>
            
            <div class="flex items-center gap-3">
              <button @click="showEditCompetitionModal = true" class="px-6 py-2 border border-yellow-400 text-yellow-600 rounded-full font-medium hover:bg-yellow-50 transition-all">
                Edit
              </button>
              <button class="px-6 py-2 border border-red-400 text-red-600 rounded-full font-medium hover:bg-red-50 transition-all">
                Delete
              </button>
            </div>
          </div>

          <!-- Competition Card 2 -->
          <div class="bg-white rounded-2xl p-6 border border-[#E5E7EB] flex items-center justify-between">
            <div class="flex items-center gap-4">
              <img 
                src="https://images.unsplash.com/photo-1596495578065-6e0763fa1178?w=80&h=80&fit=crop&crop=faces" 
                alt="May Math Madness" 
                class="w-20 h-20 rounded-xl object-cover"
              >
              <div>
                <h3 class="text-lg font-bold text-[#043355] mb-1">May Math Madness</h3>
                <p class="text-gray-600 text-sm mb-2">Solve 10 logic puzzles in 5 days...</p>
                <div class="flex items-center gap-4 text-sm text-gray-500">
                  <span class="flex items-center gap-1">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
                    </svg>
                    12 May 2025
                  </span>
                  <span class="flex items-center gap-1 text-yellow-600 font-medium">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                    Badge: Logic Sprinter
                  </span>
                </div>
              </div>
            </div>
            
            <div class="flex items-center gap-3">
              <button @click="showEditCompetitionModal = true" class="px-6 py-2 border border-yellow-400 text-yellow-600 rounded-full font-medium hover:bg-yellow-50 transition-all">
                Edit
              </button>
              <button class="px-6 py-2 border border-red-400 text-red-600 rounded-full font-medium hover:bg-red-50 transition-all">
                Delete
              </button>
            </div>
          </div>

          <!-- Competition Card 3 -->
          <div class="bg-white rounded-2xl p-6 border border-[#E5E7EB] flex items-center justify-between">
            <div class="flex items-center gap-4">
              <img 
                src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=80&h=80&fit=crop&crop=faces" 
                alt="May Math Madness" 
                class="w-20 h-20 rounded-xl object-cover"
              >
              <div>
                <h3 class="text-lg font-bold text-[#043355] mb-1">May Math Madness</h3>
                <p class="text-gray-600 text-sm mb-2">Solve 10 logic puzzles in 5 days...</p>
                <div class="flex items-center gap-4 text-sm text-gray-500">
                  <span class="flex items-center gap-1">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
                    </svg>
                    12 May 2025
                  </span>
                  <span class="flex items-center gap-1 text-yellow-600 font-medium">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                    Badge: Logic Sprinter
                  </span>
                </div>
              </div>
            </div>
            
            <div class="flex items-center gap-3">
              <button @click="showEditCompetitionModal = true" class="px-6 py-2 border border-yellow-400 text-yellow-600 rounded-full font-medium hover:bg-yellow-50 transition-all">
                Edit
              </button>
              <button class="px-6 py-2 border border-red-400 text-red-600 rounded-full font-medium hover:bg-red-50 transition-all">
                Delete
              </button>
            </div>
          </div>
        </div>

        <!-- Publish Competition Button -->
        <div class="flex justify-end mt-8">
          <button @click="showAddCompetitionModal = true" class="px-8 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-all flex items-center gap-2">
            <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
            Publish Competition
          </button>
        </div>
      </div>

      <!-- Adult Training Section -->
      <div v-if="activeSection === 'adult-training'" class="px-10 pt-8 pb-10">
        <div class="bg-white rounded-2xl p-8 border border-[#E5E7EB]">
          <h3 class="text-2xl font-bold text-[#043355] mb-6">Adult Training Management</h3>
          <p class="text-gray-600">Adult Training section content will be added here...</p>
        </div>
      </div>

      <!-- Adult Training Section -->
      <div v-if="activeSection === 'adult-training'" class="px-10 pt-8 pb-10">
        <!-- Search Bar -->
        <div class="flex items-center justify-between mb-6">
          <div class="relative flex-1 max-w-md">
            <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
            <input 
              type="text" 
              placeholder="Search for Admin"
              class="w-full pl-10 pr-4 py-3 bg-white border border-[#D1D5DC] rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
            <svg class="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </div>
          
          <!-- Add Training Round Button -->
          <button class="px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-all flex items-center gap-2">
            <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
            Adult Training Round
          </button>
        </div>

        <!-- Training Rounds -->
        <div class="space-y-4">
          <!-- First Round -->
          <div class="bg-white rounded-2xl p-6 border border-[#E5E7EB] flex items-center justify-between">
            <div>
              <h3 class="text-lg font-bold text-[#043355] mb-1">First Round</h3>
              <p class="text-gray-600 text-sm">Start Date : 5-5-2025</p>
            </div>
            
            <div class="flex items-center gap-3">
              <button class="px-6 py-2 border border-red-400 text-red-600 rounded-full font-medium hover:bg-red-50 transition-all">
                Delete
              </button>
            </div>
          </div>

          <!-- Second Round -->
          <div class="bg-white rounded-2xl p-6 border border-[#E5E7EB] flex items-center justify-between">
            <div>
              <h3 class="text-lg font-bold text-[#043355] mb-1">Second Round</h3>
              <p class="text-gray-600 text-sm">Start Date : 10-5-2025</p>
            </div>
            
            <div class="flex items-center gap-3">
              <button class="px-6 py-2 border border-red-400 text-red-600 rounded-full font-medium hover:bg-red-50 transition-all">
                Delete
              </button>
            </div>
          </div>

          <!-- Third Round -->
          <div class="bg-white rounded-2xl p-6 border border-[#E5E7EB] flex items-center justify-between">
            <div>
              <h3 class="text-lg font-bold text-[#043355] mb-1">Third Round</h3>
              <p class="text-gray-600 text-sm">Start Date : 10-5-2026</p>
            </div>
            
            <div class="flex items-center gap-3">
              <button class="px-6 py-2 border border-red-400 text-red-600 rounded-full font-medium hover:bg-red-50 transition-all">
                Delete
              </button>
            </div>
          </div>

          <!-- Fourth Round -->
          <div class="bg-white rounded-2xl p-6 border border-[#E5E7EB] flex items-center justify-between">
            <div>
              <h3 class="text-lg font-bold text-[#043355] mb-1">Fourth Round</h3>
              <p class="text-gray-600 text-sm">Start Date : 10-5-2026</p>
            </div>
            
            <div class="flex items-center gap-3">
              <button class="px-6 py-2 border border-red-400 text-red-600 rounded-full font-medium hover:bg-red-50 transition-all">
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Settings Section -->
      <div v-if="activeSection === 'settings'" class="flex-1 bg-[#E6EFFF] p-8">
        <div class="max-w-2xl mx-auto">
          <!-- Profile Picture and Name -->
          <div class="flex flex-col items-center mb-8">
            <div class="w-32 h-32 rounded-full overflow-hidden mb-4 border-4 border-white shadow-lg">
              <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=200&h=200&fit=crop&crop=face" 
                   alt="Gamal Yousef" 
                   class="w-full h-full object-cover">
            </div>
            <h2 class="text-2xl font-bold text-[#043355] mb-1">Gamal Yousef</h2>
            <p class="text-gray-500 text-lg">Super Admin</p>
          </div>

          <!-- Form Fields -->
          <div class="space-y-6">
            <!-- First Name -->
            <div>
              <label class="block text-base font-medium text-gray-700 mb-2">first name</label>
              <input 
                v-model="superAdminProfile.firstName" 
                type="text" 
                class="w-full px-4 py-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white shadow-sm"
                placeholder="Gamal"
              />
            </div>

            <!-- Last Name -->
            <div>
              <label class="block text-base font-medium text-gray-700 mb-2">last name</label>
              <input 
                v-model="superAdminProfile.lastName" 
                type="text" 
                class="w-full px-4 py-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white shadow-sm"
                placeholder="Yousef"
              />
            </div>

            <!-- Phone Number -->
            <div>
              <label class="block text-base font-medium text-gray-700 mb-2">Phone Number</label>
              <input 
                v-model="superAdminProfile.phoneNumber" 
                type="tel" 
                class="w-full px-4 py-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white shadow-sm"
                placeholder="+966457347584993"
              />
            </div>

            <!-- Father Number -->
            <div>
              <label class="block text-base font-medium text-gray-700 mb-2">Father Number</label>
              <input 
                v-model="superAdminProfile.fatherNumber" 
                type="tel" 
                class="w-full px-4 py-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white shadow-sm"
                placeholder="+966457347584993"
              />
            </div>

            <!-- Email -->
            <div>
              <label class="block text-base font-medium text-gray-700 mb-2">Email</label>
              <input 
                v-model="superAdminProfile.email" 
                type="email" 
                class="w-full px-4 py-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white shadow-sm"
                placeholder="<EMAIL>"
              />
            </div>

            <!-- Password -->
            <div>
              <label class="block text-base font-medium text-gray-700 mb-2">Password</label>
              <input 
                v-model="superAdminProfile.password" 
                type="password" 
                class="w-full px-4 py-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white shadow-sm"
                placeholder="••••••••••••"
              />
            </div>

            <!-- Save Button -->
            <div class="pt-6 flex justify-end">
              <button @click="saveProfileChanges()" 
                      class="bg-[#FFC107] text-black px-8 py-3 rounded-lg font-semibold hover:bg-[#FFD54F] transition-all shadow-md">
                Save Changes
              </button>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>

  <!-- Add Competition Modal -->
  <div v-if="showAddCompetitionModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click="showAddCompetitionModal = false">
    <div class="bg-white rounded-2xl p-8 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto" @click.stop>
      <!-- Modal Header -->
      <div class="flex items-center justify-between mb-8">
        <h2 class="text-2xl font-bold text-[#043355]">Add Competition</h2>
        <button @click="showAddCompetitionModal = false" class="text-gray-400 hover:text-gray-600 transition-colors">
          <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Modal Content -->
      <div class="grid grid-cols-2 gap-8">
        <!-- Left Column -->
        <div class="space-y-6">
          <!-- Competition Name -->
          <div>
            <label class="block text-sm font-medium text-[#043355] mb-2">Competition Name</label>
            <input 
              type="text" 
              placeholder="Nada Mahammed"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
            >
          </div>

          <!-- Photo -->
          <div>
            <label class="block text-sm font-medium text-[#043355] mb-2">Photo</label>
            <div class="relative">
              <input 
                type="file" 
                accept="image/*"
                class="hidden"
                id="competitionPhoto"
              >
              <button 
                @click="document.getElementById('competitionPhoto').click()"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50 text-left text-[#6366f1] flex items-center gap-2"
              >
                <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                </svg>
                Photo
              </button>
            </div>
          </div>

          <!-- Reward -->
          <div>
            <label class="block text-sm font-medium text-[#043355] mb-2">Reward</label>
            <input 
              type="text" 
              placeholder="prize"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
            >
          </div>

          <!-- Description -->
          <div>
            <label class="block text-sm font-medium text-[#043355] mb-2">Description</label>
            <textarea 
              rows="8"
              placeholder="isl. quis orci leo. ipsum vitae at quam lacus, tincidunt diam laoreet sapien eu quam ipsum quis quis non vitae lacus, lorem. malesuada cursus dignissim, est.

dolor odio scelerisque e"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50 resize-none"
            ></textarea>
          </div>
        </div>

        <!-- Right Column -->
        <div class="space-y-6">
          <!-- Date -->
          <div>
            <label class="block text-sm font-medium text-[#043355] mb-2">Date</label>
            <div class="relative">
              <input 
                type="text" 
                placeholder="15-02-2025"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50 pl-12"
              >
              <svg class="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[#6366f1]" fill="currentColor" viewBox="0 0 24 24">
                <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
              </svg>
            </div>
          </div>

          <!-- Price -->
          <div>
            <label class="block text-sm font-medium text-[#043355] mb-2">Price</label>
            <input 
              type="text" 
              placeholder="$15.00"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
            >
          </div>

          <!-- Individual Or Team -->
          <div>
            <label class="block text-sm font-medium text-[#043355] mb-2">Individual Or Team</label>
            <input 
              type="text" 
              placeholder="Team"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
            >
          </div>

          <!-- Conditions & Rules -->
          <div>
            <label class="block text-sm font-medium text-[#043355] mb-2">Conditions & Rules</label>
            <textarea 
              rows="8"
              placeholder="isl. quis orci leo. ipsum vitae at quam lacus, tincidunt diam laoreet sapien eu quam ipsum quis quis non vitae lacus, lorem. malesuada cursus dignissim, est.

dolor odio scelerisque e"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50 resize-none"
            ></textarea>
          </div>
        </div>
      </div>

      <!-- Add Button -->
      <div class="flex justify-end mt-8">
        <button class="px-8 py-3 bg-yellow-500 text-white rounded-lg font-medium hover:bg-yellow-600 transition-all">
          Add
        </button>
      </div>
    </div>
  </div>

  <!-- Edit Competition Modal -->
  <div v-if="showEditCompetitionModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click="showEditCompetitionModal = false">
    <div class="bg-white rounded-2xl p-8 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto" @click.stop>
      <!-- Modal Header -->
      <div class="flex items-center justify-between mb-8">
        <h2 class="text-2xl font-bold text-[#043355]">Edit Competition</h2>
        <button @click="showEditCompetitionModal = false" class="text-gray-400 hover:text-gray-600 transition-colors">
          <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Modal Content -->
      <div class="grid grid-cols-2 gap-8">
        <!-- Left Column -->
        <div class="space-y-6">
          <!-- Competition Name -->
          <div>
            <label class="block text-sm font-medium text-[#043355] mb-2">Competition Name</label>
            <input 
              type="text" 
              value="Nada Mahammed"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
            >
          </div>

          <!-- Photo -->
          <div>
            <label class="block text-sm font-medium text-[#043355] mb-2">Photo</label>
            <div class="relative">
              <input 
                type="file" 
                accept="image/*"
                class="hidden"
                id="editCompetitionPhoto"
              >
              <button 
                @click="document.getElementById('editCompetitionPhoto').click()"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50 text-left text-[#6366f1] flex items-center gap-2"
              >
                <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                </svg>
                Photo
              </button>
            </div>
          </div>

          <!-- Reward -->
          <div>
            <label class="block text-sm font-medium text-[#043355] mb-2">Reward</label>
            <input 
              type="text" 
              value="prize"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
            >
          </div>

          <!-- Description -->
          <div>
            <label class="block text-sm font-medium text-[#043355] mb-2">Description</label>
            <textarea 
              rows="8"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50 resize-none"
            >isl. quis orci leo. ipsum vitae at quam lacus, tincidunt diam laoreet sapien eu quam ipsum quis quis non vitae lacus, lorem. malesuada cursus dignissim, est.

dolor odio scelerisque e</textarea>
          </div>
        </div>

        <!-- Right Column -->
        <div class="space-y-6">
          <!-- Date -->
          <div>
            <label class="block text-sm font-medium text-[#043355] mb-2">Date</label>
            <div class="relative">
              <input 
                type="text" 
                value="15-02-2025"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50 pl-12"
              >
              <svg class="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[#6366f1]" fill="currentColor" viewBox="0 0 24 24">
                <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
              </svg>
            </div>
          </div>

          <!-- Price -->
          <div>
            <label class="block text-sm font-medium text-[#043355] mb-2">Price</label>
            <input 
              type="text" 
              value="$15.00"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
            >
          </div>

          <!-- Individual Or Team -->
          <div>
            <label class="block text-sm font-medium text-[#043355] mb-2">Individual Or Team</label>
            <input 
              type="text" 
              value="Team"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
            >
          </div>

          <!-- Conditions & Rules -->
          <div>
            <label class="block text-sm font-medium text-[#043355] mb-2">Conditions & Rules</label>
            <textarea 
              rows="8"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50 resize-none"
            >isl. quis orci leo. ipsum vitae at quam lacus, tincidunt diam laoreet sapien eu quam ipsum quis quis non vitae lacus, lorem. malesuada cursus dignissim, est.

dolor odio scelerisque e</textarea>
          </div>
        </div>
      </div>

      <!-- Update Button -->
      <div class="flex justify-end mt-8">
        <button class="px-8 py-3 bg-yellow-500 text-white rounded-lg font-medium hover:bg-yellow-600 transition-all">
          Update
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

const activeSection = ref('dashboard')
const activeUsersTab = ref('admins')
const competitionType = ref('seasonal')
const searchQuery = ref('')
const showAddAdminModal = ref(false)
const showEditAdminModal = ref(false)
const showAddTeacherModal = ref(false)
const showEditTeacherModal = ref(false)
const showAddCompetitionModal = ref(false)
const showEditCompetitionModal = ref(false)

// Super Admin profile data
const superAdminProfile = ref({
  firstName: 'Gamal',
  lastName: 'Yousef',
  phoneNumber: '+966457347584993',
  fatherNumber: '+966457347584993',
  email: '<EMAIL>',
  password: '••••••••••••'
})

// Admins data
const admins = ref([
  {
    id: 1,
    name: 'Lila Youssef',
    role: 'Finance manager',
    email: '<EMAIL>',
    countryCode: '+213',
    phoneNumber: '555123456',
    description: 'Experienced finance manager with 5+ years in financial planning and analysis.',
    department: 'Finance department',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=50&h=50&fit=crop&crop=face'
  },
  {
    id: 2,
    name: 'Youssef Mollar',
    role: 'Logistic',
    email: '<EMAIL>',
    countryCode: '+966',
    phoneNumber: '555654321',
    description: 'Logistics coordinator specializing in supply chain management.',
    department: 'Logistics department',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face'
  },
  {
    id: 3,
    name: 'Nany Jan',
    role: 'Marketing',
    email: '<EMAIL>',
    countryCode: '+20',
    phoneNumber: '555789012',
    description: 'Digital marketing specialist with expertise in social media campaigns.',
    department: 'Marketing department',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=50&h=50&fit=crop&crop=face'
  }
])

// Teachers data
const teachers = ref([
  {
    id: 1,
    name: 'Ahmed Hassan',
    subject: 'Mathematics',
    jobTitle: 'Senior Math Teacher',
    courseName: 'Advanced Mathematics',
    email: '<EMAIL>',
    countryCode: '+966',
    phoneNumber: '555111222',
    description: 'Experienced mathematics teacher with 8+ years of teaching advanced calculus and algebra.',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop&crop=face'
  },
  {
    id: 2,
    name: 'Sara Mohammed',
    subject: 'Physics',
    jobTitle: 'Physics Instructor',
    courseName: 'Basic Physics',
    email: '<EMAIL>',
    countryCode: '+20',
    phoneNumber: '555333444',
    description: 'Physics instructor specializing in experimental physics and laboratory techniques.',
    avatar: 'https://images.unsplash.com/photo-1544725176-7c40e5a71c5e?w=50&h=50&fit=crop&crop=face'
  }
])

// New admin/teacher forms
const newAdmin = ref({
  name: '',
  email: '',
  countryCode: '+213',
  phoneNumber: '',
  photo: null as File | null,
  description: '',
  department: ''
})

const editingAdmin = ref({
  id: null as number | null,
  name: '',
  email: '',
  countryCode: '+213',
  phoneNumber: '',
  photo: null as File | null,
  description: '',
  department: ''
})

const newTeacher = ref({
  name: '',
  email: '',
  countryCode: '+213',
  phoneNumber: '',
  photo: null as File | null,
  jobTitle: '',
  courseName: '',
  description: ''
})

const editingTeacher = ref({
  id: null as number | null,
  name: '',
  email: '',
  countryCode: '+213',
  phoneNumber: '',
  photo: null as File | null,
  jobTitle: '',
  courseName: '',
  description: ''
})

// Computed properties for filtering
const filteredAdmins = computed(() => {
  if (!searchQuery.value) return admins.value
  return admins.value.filter(admin => 
    admin.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    admin.role.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

const filteredTeachers = computed(() => {
  if (!searchQuery.value) return teachers.value
  return teachers.value.filter(teacher => 
    teacher.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    teacher.subject.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    teacher.jobTitle?.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    teacher.courseName?.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

const getSectionTitle = () => {
  const titles: Record<string, string> = {
    dashboard: 'Dashboard',
    users: 'Users Management',
    competitions: 'Competitions Management',
    'adult-training': 'Adult Training Management',
    settings: 'Settings'
  }
  return titles[activeSection.value] || activeSection.value
}

// Admin management functions
const handlePhotoUpload = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files[0]) {
    newAdmin.value.photo = target.files[0]
  }
}

const handleEditPhotoUpload = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files[0]) {
    editingAdmin.value.photo = target.files[0]
  }
}

const editAdmin = (admin: any) => {
  console.log('Editing admin:', admin)
  // Fill the editing form with current admin data
  editingAdmin.value = {
    id: admin.id,
    name: admin.name,
    email: admin.email,
    countryCode: admin.countryCode || '+213',
    phoneNumber: admin.phoneNumber ? admin.phoneNumber.replace(/^\+\d+\s/, '') : '',
    photo: null,
    description: admin.description || '',
    department: admin.department || admin.role
  }
  showEditAdminModal.value = true
}

const updateAdmin = () => {
  if (editingAdmin.value.name && editingAdmin.value.email && editingAdmin.value.phoneNumber && editingAdmin.value.department) {
    const index = admins.value.findIndex(a => a.id === editingAdmin.value.id)
    if (index > -1) {
      admins.value[index] = {
        ...admins.value[index],
        name: editingAdmin.value.name,
        role: editingAdmin.value.department,
        email: editingAdmin.value.email,
        countryCode: editingAdmin.value.countryCode,
        phoneNumber: editingAdmin.value.phoneNumber,
        description: editingAdmin.value.description,
        department: editingAdmin.value.department
      }
      
      showEditAdminModal.value = false
      editingAdmin.value = {
        id: null,
        name: '',
        email: '',
        countryCode: '+213',
        phoneNumber: '',
        photo: null,
        description: '',
        department: ''
      }
      alert('Admin updated successfully!')
    }
  } else {
    alert('Please fill all required fields (Name, Email, Phone Number, Department)')
  }
}

const deleteAdmin = (admin: any) => {
  console.log('Deleting admin:', admin)
  if (confirm(`Are you sure you want to delete ${admin.name}?`)) {
    const index = admins.value.findIndex(a => a.id === admin.id)
    if (index > -1) {
      admins.value.splice(index, 1)
      alert(`Admin ${admin.name} has been deleted`)
    }
  }
}

const addAdmin = () => {
  if (newAdmin.value.name && newAdmin.value.email && newAdmin.value.phoneNumber && newAdmin.value.department) {
    const admin = {
      id: Date.now(),
      name: newAdmin.value.name,
      role: newAdmin.value.department,
      email: newAdmin.value.email,
      countryCode: newAdmin.value.countryCode,
      phoneNumber: newAdmin.value.phoneNumber,
      description: newAdmin.value.description,
      department: newAdmin.value.department,
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face'
    }
    admins.value.push(admin)
    newAdmin.value = { 
      name: '', 
      email: '', 
      countryCode: '+213',
      phoneNumber: '',
      photo: null,
      description: '',
      department: ''
    }
    showAddAdminModal.value = false
    alert('Admin added successfully!')
  } else {
    alert('Please fill all required fields (Name, Email, Phone Number, Department)')
  }
}

// Teacher management functions
const handleTeacherPhotoUpload = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files[0]) {
    newTeacher.value.photo = target.files[0]
  }
}

const handleEditTeacherPhotoUpload = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files[0]) {
    editingTeacher.value.photo = target.files[0]
  }
}

const editTeacher = (teacher: any) => {
  console.log('Editing teacher:', teacher)
  // Fill the editing form with current teacher data
  editingTeacher.value = {
    id: teacher.id,
    name: teacher.name,
    email: teacher.email,
    countryCode: teacher.countryCode || '+213',
    phoneNumber: teacher.phoneNumber ? teacher.phoneNumber.replace(/^\+\d+\s/, '') : '',
    photo: null,
    jobTitle: teacher.jobTitle || teacher.subject || '',
    courseName: teacher.courseName || '',
    description: teacher.description || ''
  }
  showEditTeacherModal.value = true
}

const updateTeacher = () => {
  if (editingTeacher.value.name && editingTeacher.value.email && editingTeacher.value.phoneNumber && editingTeacher.value.jobTitle && editingTeacher.value.courseName) {
    const index = teachers.value.findIndex(t => t.id === editingTeacher.value.id)
    if (index > -1) {
      teachers.value[index] = {
        ...teachers.value[index],
        name: editingTeacher.value.name,
        subject: editingTeacher.value.jobTitle,
        jobTitle: editingTeacher.value.jobTitle,
        courseName: editingTeacher.value.courseName,
        email: editingTeacher.value.email,
        countryCode: editingTeacher.value.countryCode,
        phoneNumber: editingTeacher.value.phoneNumber,
        description: editingTeacher.value.description
      }
      
      showEditTeacherModal.value = false
      editingTeacher.value = {
        id: null,
        name: '',
        email: '',
        countryCode: '+213',
        phoneNumber: '',
        photo: null,
        jobTitle: '',
        courseName: '',
        description: ''
      }
      alert('Teacher updated successfully!')
    }
  } else {
    alert('Please fill all required fields (Name, Email, Phone Number, Job Title, Course Name)')
  }
}

const deleteTeacher = (teacher: any) => {
  console.log('Deleting teacher:', teacher)
  if (confirm(`Are you sure you want to delete ${teacher.name}?`)) {
    const index = teachers.value.findIndex(t => t.id === teacher.id)
    if (index > -1) {
      teachers.value.splice(index, 1)
      alert(`Teacher ${teacher.name} has been deleted`)
    }
  }
}

const addTeacher = () => {
  if (newTeacher.value.name && newTeacher.value.email && newTeacher.value.phoneNumber && newTeacher.value.jobTitle && newTeacher.value.courseName) {
    const teacher = {
      id: Date.now(),
      name: newTeacher.value.name,
      subject: newTeacher.value.jobTitle,
      jobTitle: newTeacher.value.jobTitle,
      courseName: newTeacher.value.courseName,
      email: newTeacher.value.email,
      countryCode: newTeacher.value.countryCode,
      phoneNumber: newTeacher.value.phoneNumber,
      description: newTeacher.value.description,
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face'
    }
    teachers.value.push(teacher)
    newTeacher.value = { 
      name: '', 
      email: '', 
      countryCode: '+213',
      phoneNumber: '',
      photo: null,
      jobTitle: '',
      courseName: '',
      description: ''
    }
    showAddTeacherModal.value = false
    alert('Teacher added successfully!')
  } else {
    alert('Please fill all required fields (Name, Email, Phone Number, Job Title, Course Name)')
  }
}

// Settings functions
const saveProfileChanges = () => {
  console.log('Saving super admin profile changes:', superAdminProfile.value)
  alert('Profile changes saved successfully!')
}

// Stats Cards Functions
const viewAdmins = () => {
  console.log('Viewing Admins list')
  activeSection.value = 'users'
  activeUsersTab.value = 'admins'
}

const viewTeachers = () => {
  console.log('Viewing Teachers list')
  activeSection.value = 'users'
  activeUsersTab.value = 'teachers'
}

const viewTeams = () => {
  console.log('Viewing Teams list')
  alert('Redirecting to Teams management page...')
}

const viewGroups = () => {
  console.log('Viewing Groups list')
  alert('Redirecting to Groups management page...')
}

// Chart Functions
const viewStudents = () => {
  console.log('Viewing Students analytics')
  alert('Showing detailed Students analytics...')
}

const viewMonthlyCompetitions = () => {
  console.log('Viewing Monthly Competitions')
  alert('Showing Monthly Competitions details...')
}

const viewSeasonalCompetitions = () => {
  console.log('Viewing Seasonal Competitions')
  alert('Showing Seasonal Competitions details...')
}
</script>
