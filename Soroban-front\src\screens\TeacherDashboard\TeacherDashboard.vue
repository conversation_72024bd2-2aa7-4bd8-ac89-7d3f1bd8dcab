<template>
  <div class="flex min-h-screen bg-[#F9FAFB]">
    <!-- Sidebar -->
    <aside class="w-64 bg-[#F6F9FF] flex flex-col justify-between border-r border-[#D1D5DC]">
      <div>
        <div class="flex items-center gap-2 px-6 py-8">
          <img src="https://c.animaapp.com/mc5ppr8hKB91iA/img/logo-lerning-removebg-preview-1-1.png" alt="ELBARQ Logo" class="h-10 w-10 rounded" />
          <div>
            <div class="font-bold text-[#162456] text-lg leading-4">ELBARQ</div>
            <div class="text-xs text-[#d08700] font-semibold -mt-1">Soroban</div>
          </div>
        </div>
        <nav class="mt-2">
          <ul class="space-y-2 px-2">
            <li><a href="#" @click="activeSection = 'dashboard'" :class="activeSection === 'dashboard' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all"> 
              <span>
                <svg width="18" height="18" fill="#043355" viewBox="0 0 24 24">
                  <path d="M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" stroke="#043355" stroke-width="2" fill="none"/>
                  <path d="M9 22V12h6v10" stroke="#043355" stroke-width="2" fill="none"/>
                </svg>
              </span> 
              Dashboard
            </a></li>
            
            <li><a href="#" @click="activeSection = 'groups'" :class="activeSection === 'groups' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all"> 
              <span>
                <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
                  <path d="M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2" stroke="#043355" stroke-width="2"/>
                  <circle cx="9" cy="7" r="4" stroke="#043355" stroke-width="2"/>
                  <path d="M23 21v-2a4 4 0 00-3-3.87" stroke="#043355" stroke-width="2"/>
                  <path d="M16 3.13a4 4 0 010 7.75" stroke="#043355" stroke-width="2"/>
                </svg>
              </span> 
              Groups
            </a></li>
            
            <li><a href="#" @click="activeSection = 'teams'" :class="activeSection === 'teams' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all"> 
              <span>
                <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
                  <path d="M16 21v-2a4 4 0 00-4-4H6a4 4 0 00-4 4v2" stroke="#043355" stroke-width="2"/>
                  <circle cx="9" cy="7" r="4" stroke="#043355" stroke-width="2"/>
                  <path d="M22 21v-2a4 4 0 00-3-3.87" stroke="#043355" stroke-width="2"/>
                  <path d="M16 3.13a4 4 0 010 7.75" stroke="#043355" stroke-width="2"/>
                </svg>
              </span> 
              Teams
            </a></li>
            
            <li><a href="#" @click="activeSection = 'courses'" :class="activeSection === 'courses' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all"> 
              <span>
                <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
                  <path d="M2 3h6a4 4 0 014 4v14a3 3 0 00-3-3H2V3z" stroke="#043355" stroke-width="2"/>
                  <path d="M22 3h-6a4 4 0 00-4 4v14a3 3 0 013-3h7V3z" stroke="#043355" stroke-width="2"/>
                </svg>
              </span> 
              Courses
            </a></li>
            
            <li><a href="#" @click="activeSection = 'contact'" :class="activeSection === 'contact' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all">
              <span>
                <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
                  <path d="M21 15a2 2 0 01-2 2H7l-4 4V5a2 2 0 012-2h14a2 2 0 012 2v10z" stroke="#043355" stroke-width="2"/>
                  <path d="M13 11h0" stroke="#043355" stroke-width="2"/>
                  <path d="M9 11h0" stroke="#043355" stroke-width="2"/>
                  <path d="M17 11h0" stroke="#043355" stroke-width="2"/>
                </svg>
              </span>
              Contact & Help
            </a></li>

            <!-- Course & Video Management -->
            <li><router-link :to="{ name: 'teacher-courses' }" :class="$route.name === 'teacher-courses' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all">
              <span>
                <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
                  <path d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" stroke="#043355" stroke-width="2"/>
                </svg>
              </span>
              Course Management
            </router-link></li>

            <li><router-link :to="{ name: 'teacher-videos' }" :class="$route.name === 'teacher-videos' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all">
              <span>
                <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
                  <path d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" stroke="#043355" stroke-width="2"/>
                </svg>
              </span>
              Video Management
            </router-link></li>
          </ul>
        </nav>
      </div>
      <div class="mb-6 px-2 space-y-2">
        <a href="#" @click="activeSection = 'settings'" :class="activeSection === 'settings' ? 'bg-white shadow font-semibold' : 'hover:bg-white hover:shadow-md'" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#043355] transition-all">
          <span>
            <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
              <circle cx="12" cy="12" r="3" stroke="#043355" stroke-width="2"/>
              <path d="M19.4 15a1.65 1.65 0 00.33 1.82l.06.06a2 2 0 010 2.83 2 2 0 01-2.83 0l-.06-.06a1.65 1.65 0 00-1.82-.33 1.65 1.65 0 00-1 1.51V21a2 2 0 01-2 2 2 2 0 01-2-2v-.09A1.65 1.65 0 009 19.4a1.65 1.65 0 00-1.82.33l-.06.06a2 2 0 01-2.83 0 2 2 0 010-2.83l.06-.06a1.65 1.65 0 00.33-1.82 1.65 1.65 0 00-1.51-1H3a2 2 0 01-2-2 2 2 0 012-2h.09A1.65 1.65 0 004.6 9a1.65 1.65 0 00-.33-1.82l-.06-.06a2 2 0 010-2.83 2 2 0 012.83 0l.06.06a1.65 1.65 0 001.82.33H9a1.65 1.65 0 001-1.51V3a2 2 0 012-2 2 2 0 012 2v.09a1.65 1.65 0 001 1.51 1.65 1.65 0 001.82-.33l.06-.06a2 2 0 012.83 0 2 2 0 010 2.83l-.06.06a1.65 1.65 0 00-.33 1.82V9a1.65 1.65 0 001.51 1H21a2 2 0 012 2 2 2 0 01-2 2h-.09a1.65 1.65 0 00-1.51 1z" stroke="#043355" stroke-width="2"/>
            </svg>
          </span>
          Setting
        </a>
        <a href="#" class="flex items-center gap-3 px-4 py-3 rounded-[24px] text-[#e7000b] hover:bg-red-50 hover:shadow-md transition-all">
          <span>
            <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
              <path d="M9 21H5a2 2 0 01-2-2V5a2 2 0 012-2h4" stroke="#e7000b" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M16 17l5-5-5-5" stroke="#e7000b" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M21 12H9" stroke="#e7000b" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </span>
          Log out
        </a>
      </div>
    </aside>

    <!-- Main Content -->
    <main class="flex-1 min-h-screen">
      <!-- Header -->
      <div class="flex justify-end items-center px-10 py-4 bg-white shadow-sm border-b border-[#F3F3F3]" style="min-height:107px;">
        <div class="flex items-center gap-6">
          <span class="text-xl text-black"><svg width="20" height="20" fill="none" viewBox="0 0 24 24"><path d="M21 12.79A9 9 0 1 1 11.21 3a7 7 0 0 0 9.79 9.79Z" stroke="#000" stroke-width="2"/></svg></span>
          <span class="relative">
            <svg class="w-7 h-7 text-[#043355]" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V4a2 2 0 10-4 0v1.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/></svg>
            <span class="absolute -top-2 -right-2 bg-[#BC0000] text-white rounded-full w-5 h-5 flex items-center justify-center text-xs border-2 border-white">1</span>
          </span>
          <div class="flex items-center gap-2 bg-white border border-[#D1D5DC] rounded-full px-4 py-2">
            <img src="https://randomuser.me/api/portraits/men/32.jpg" class="w-8 h-8 rounded-full object-cover border border-[#D1D5DC]" />
            <span class="font-bold text-[#043355]">Gamal Yousef</span>
          </div>
        </div>
      </div>

      <!-- Dashboard Content -->
      <div v-if="activeSection === 'dashboard'" class="flex gap-8">
        <!-- Left Side - Main Dashboard -->
        <div class="flex-1 px-10 pt-8 pb-10">
          <!-- Dashboard Icon and Title -->
          <div class="flex items-center gap-3 mb-8">
            <div class="bg-[#043355] p-3 rounded-lg">
              <svg width="24" height="24" fill="white" viewBox="0 0 24 24">
                <path d="M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" stroke="white" stroke-width="2" fill="none"/>
                <path d="M9 22V12h6v10" stroke="white" stroke-width="2" fill="none"/>
              </svg>
            </div>
            <h1 class="text-4xl font-extrabold text-[#043355]">Dashboard</h1>
          </div>
          
          <!-- Statistics Cards -->
          <div class="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-10">
            <!-- Count Students Card -->
            <div class="bg-[#043355] rounded-[24px] flex flex-col items-center justify-center py-8 min-w-[180px] text-white shadow-lg">
              <span class="text-sm font-semibold mb-2">Count Students</span>
              <div class="flex items-baseline gap-1">
                <span class="text-4xl font-extrabold">300</span>
                <span class="text-sm">student</span>
              </div>
            </div>
            
            <!-- Count Courses Card -->
            <div class="bg-[#F6F9FF] hover:bg-[#E8F2FF] rounded-[24px] flex flex-col items-center justify-center py-8 min-w-[180px] cursor-pointer transition-all duration-300 hover:shadow-lg transform hover:scale-105">
              <span class="text-[#043355] text-sm font-semibold mb-2">Count Courses</span>
              <div class="flex items-baseline gap-1">
                <span class="text-[#1447E6] text-4xl font-extrabold">3</span>
                <span class="text-[#043355] text-sm">Course</span>
              </div>
            </div>
            
            <!-- Count Lessons Card -->
            <div class="bg-[#F6F9FF] hover:bg-[#E8F2FF] rounded-[24px] flex flex-col items-center justify-center py-8 min-w-[180px] cursor-pointer transition-all duration-300 hover:shadow-lg transform hover:scale-105">
              <span class="text-[#043355] text-sm font-semibold mb-2">Count Lessons</span>
              <div class="flex items-baseline gap-1">
                <span class="text-[#1447E6] text-4xl font-extrabold">120</span>
                <span class="text-[#043355] text-sm">lesson</span>
              </div>
            </div>
            
            <!-- Count Groups Card -->
            <div class="bg-[#F6F9FF] hover:bg-[#E8F2FF] rounded-[24px] flex flex-col items-center justify-center py-8 min-w-[180px] cursor-pointer transition-all duration-300 hover:shadow-lg transform hover:scale-105">
              <span class="text-[#043355] text-sm font-semibold mb-2">Count Groups</span>
              <div class="flex items-baseline gap-1">
                <span class="text-[#1447E6] text-4xl font-extrabold">20</span>
                <span class="text-[#043355] text-sm">Group</span>
              </div>
            </div>
          </div>

          <!-- Quick Access Cards -->
          <div class="grid grid-cols-2 gap-6 mb-10">
            <!-- Course Management -->
            <router-link :to="{ name: 'teacher-courses' }" class="bg-gradient-to-br from-blue-500 to-blue-600 text-white rounded-2xl p-6 hover:from-blue-600 hover:to-blue-700 transition-all transform hover:scale-105 shadow-lg">
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-lg font-semibold mb-2">Course Management</h3>
                  <p class="text-blue-100 text-sm">Create and manage your courses and lessons</p>
                </div>
                <div class="bg-white bg-opacity-20 rounded-full p-3">
                  <svg width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                  </svg>
                </div>
              </div>
            </router-link>

            <!-- Video Management -->
            <router-link :to="{ name: 'teacher-videos' }" class="bg-gradient-to-br from-purple-500 to-purple-600 text-white rounded-2xl p-6 hover:from-purple-600 hover:to-purple-700 transition-all transform hover:scale-105 shadow-lg">
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-lg font-semibold mb-2">Video Management</h3>
                  <p class="text-purple-100 text-sm">Manage YouTube playlists and video content</p>
                </div>
                <div class="bg-white bg-opacity-20 rounded-full p-3">
                  <svg width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                  </svg>
                </div>
              </div>
            </router-link>
          </div>

          <!-- Groups Progress Chart -->
          <div class="bg-white rounded-2xl shadow p-6">
            <h2 class="text-xl font-bold text-[#162456] mb-6 flex items-center gap-2">
              <span>
                <svg width="20" height="20" fill="none" viewBox="0 0 24 24">
                  <path d="M9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z" stroke="#162456" stroke-width="2" fill="none"/>
                </svg>
              </span>
              Groups Progress
            </h2>
            
            <!-- Bar Chart -->
            <div class="relative h-80 flex items-end justify-center gap-8 px-8">
              <!-- Group A1 -->
              <div class="flex flex-col items-center">
                <div class="bg-[#2C3E50] rounded-t-lg w-12 mb-4" style="height: 180px;"></div>
                <span class="text-xs text-gray-600 font-medium">Group A1</span>
              </div>
              
              <!-- Group C1 -->
              <div class="flex flex-col items-center">
                <div class="bg-[#2C3E50] rounded-t-lg w-12 mb-4" style="height: 220px;"></div>
                <span class="text-xs text-gray-600 font-medium">Group C1</span>
              </div>
              
              <!-- Group B2 -->
              <div class="flex flex-col items-center">
                <div class="bg-[#2C3E50] rounded-t-lg w-12 mb-4" style="height: 120px;"></div>
                <span class="text-xs text-gray-600 font-medium">Group B2</span>
              </div>
              
              <!-- Group a3 -->
              <div class="flex flex-col items-center">
                <div class="bg-[#2C3E50] rounded-t-lg w-12 mb-4" style="height: 160px;"></div>
                <span class="text-xs text-gray-600 font-medium">Group a3</span>
              </div>
              
              <!-- Group A3 -->
              <div class="flex flex-col items-center">
                <div class="bg-[#2C3E50] rounded-t-lg w-12 mb-4" style="height: 260px;"></div>
                <span class="text-xs text-gray-600 font-medium">Group A3</span>
              </div>
              
              <!-- Group B2 -->
              <div class="flex flex-col items-center">
                <div class="bg-[#2C3E50] rounded-t-lg w-12 mb-4" style="height: 140px;"></div>
                <span class="text-xs text-gray-600 font-medium">Group B2</span>
              </div>
              
              <!-- Group C1 -->
              <div class="flex flex-col items-center">
                <div class="bg-[#2C3E50] rounded-t-lg w-12 mb-4" style="height: 200px;"></div>
                <span class="text-xs text-gray-600 font-medium">Group C1</span>
              </div>
              
              <!-- Group C1 -->
              <div class="flex flex-col items-center">
                <div class="bg-[#2C3E50] rounded-t-lg w-12 mb-4" style="height: 280px;"></div>
                <span class="text-xs text-gray-600 font-medium">Group C1</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Right Side - Students Feedback -->
        <div class="w-80 px-6 pt-8 pb-10 bg-white border-l border-[#E5E7EB]">
          <div class="flex items-center gap-3 mb-8">
            <div class="bg-[#043355] p-2 rounded-lg">
              <svg width="20" height="20" fill="white" viewBox="0 0 24 24">
                <path d="M21 15a2 2 0 01-2 2H7l-4 4V5a2 2 0 012-2h14a2 2 0 012 2v10z"/>
              </svg>
            </div>
            <h2 class="text-xl font-bold text-[#162456]">Students Feedback</h2>
          </div>
          
          <div class="space-y-6">
            <!-- Feedback 1 -->
            <div class="bg-white border border-[#E5E7EB] rounded-xl p-4">
              <div class="flex text-yellow-500 mb-3">
                <span>★★★★★</span>
              </div>
              <p class="text-sm text-gray-600 mb-4">"I never thought learning could be this fun! The challenges really keep me motivated, and I love how the platform adapts to my level."</p>
              <div class="flex items-center gap-3">
                <img src="https://randomuser.me/api/portraits/women/32.jpg" alt="Nour Jan" class="w-10 h-10 rounded-full object-cover">
                <div>
                  <div class="font-semibold text-sm">Nour Jan</div>
                  <div class="text-xs text-gray-500">High School Student</div>
                </div>
              </div>
            </div>

            <!-- Feedback 2 -->
            <div class="bg-white border border-[#E5E7EB] rounded-xl p-4">
              <div class="flex text-yellow-500 mb-3">
                <span>★★★★★</span>
              </div>
              <p class="text-sm text-gray-600 mb-4">"I never thought learning could be this fun! The challenges really keep me motivated, and I love how the platform adapts to my level."</p>
              <div class="flex items-center gap-3">
                <img src="https://randomuser.me/api/portraits/women/45.jpg" alt="Holly Jack" class="w-10 h-10 rounded-full object-cover">
                <div>
                  <div class="font-semibold text-sm">Holly Jack</div>
                  <div class="text-xs text-gray-500">High School Student</div>
                </div>
              </div>
            </div>

            <!-- Feedback 3 -->
            <div class="bg-white border border-[#E5E7EB] rounded-xl p-4">
              <div class="flex text-yellow-500 mb-3">
                <span>★★★★★</span>
              </div>
              <p class="text-sm text-gray-600 mb-4">"I never thought learning could be this fun! The challenges really keep me motivated, and I love how the platform adapts to my level."</p>
              <div class="flex items-center gap-3">
                <img src="https://randomuser.me/api/portraits/women/28.jpg" alt="Nour Jan" class="w-10 h-10 rounded-full object-cover">
                <div>
                  <div class="font-semibold text-sm">Nour Jan</div>
                  <div class="text-xs text-gray-500">High School Student</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Groups Content -->
      <div v-if="activeSection === 'groups' && !showAddGroupPage" class="px-10 pt-8 pb-10">
        <h1 class="text-4xl font-extrabold text-[#043355] mb-8">Groups</h1>
        
        <!-- Group Tabs -->
        <div class="flex bg-gray-100 rounded-lg p-1 mb-8 max-w-2xl">
          <button 
            @click="activeGroupTab = 'waiting'" 
            :class="activeGroupTab === 'waiting' ? 'bg-white text-[#043355] shadow-sm font-semibold' : 'text-gray-600 hover:bg-white hover:shadow-sm'"
            class="px-6 py-3 rounded-lg transition-all flex-1"
          >
            Waiting List
          </button>
          <button 
            @click="activeGroupTab = 'groupA1'" 
            :class="activeGroupTab === 'groupA1' ? 'bg-white text-[#043355] shadow-sm font-semibold' : 'text-gray-600 hover:bg-white hover:shadow-sm'"
            class="px-6 py-3 rounded-lg transition-all flex-1"
          >
            Group A1
          </button>
          <button 
            @click="activeGroupTab = 'groupB2'" 
            :class="activeGroupTab === 'groupB2' ? 'bg-white text-[#043355] shadow-sm font-semibold' : 'text-gray-600 hover:bg-white hover:shadow-sm'"
            class="px-6 py-3 rounded-lg transition-all flex-1"
          >
            Group B2
          </button>
          <button 
            @click="activeGroupTab = 'groupC3'" 
            :class="activeGroupTab === 'groupC3' ? 'bg-white text-[#043355] shadow-sm font-semibold' : 'text-gray-600 hover:bg-white hover:shadow-sm'"
            class="px-6 py-3 rounded-lg transition-all flex-1"
          >
            Group C3
          </button>
        </div>

        <!-- Waiting List Content -->
        <div v-if="activeGroupTab === 'waiting'">
          <div class="space-y-4">
            <!-- Student 1 -->
            <div class="bg-white rounded-2xl border border-[#E5E7EB] p-6 flex items-center justify-between hover:shadow-md transition-shadow">
              <div class="flex items-center gap-4">
                <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Lila Youssef" class="w-12 h-12 rounded-full object-cover">
                <div>
                  <h3 class="font-semibold text-[#043355] text-base">Lila Youssef</h3>
                  <p class="text-[#6B7280] text-sm">65%</p>
                </div>
              </div>
              <button class="bg-white border-2 border-[#3B82F6] text-[#3B82F6] hover:bg-[#3B82F6] hover:text-white px-8 py-2 rounded-full font-semibold transition-all duration-200">
                Add
              </button>
            </div>

            <!-- Student 2 -->
            <div class="bg-white rounded-2xl border border-[#E5E7EB] p-6 flex items-center justify-between hover:shadow-md transition-shadow">
              <div class="flex items-center gap-4">
                <img src="https://randomuser.me/api/portraits/women/32.jpg" alt="Nany Jan" class="w-12 h-12 rounded-full object-cover">
                <div>
                  <h3 class="font-semibold text-[#043355] text-base">Nany Jan</h3>
                  <p class="text-[#6B7280] text-sm">85%</p>
                </div>
              </div>
              <button class="bg-white border-2 border-[#3B82F6] text-[#3B82F6] hover:bg-[#3B82F6] hover:text-white px-8 py-2 rounded-full font-semibold transition-all duration-200">
                Add
              </button>
            </div>

            <!-- Student 3 -->
            <div class="bg-white rounded-2xl border border-[#E5E7EB] p-6 flex items-center justify-between hover:shadow-md transition-shadow">
              <div class="flex items-center gap-4">
                <img src="https://randomuser.me/api/portraits/men/25.jpg" alt="Youssef Mollar" class="w-12 h-12 rounded-full object-cover">
                <div>
                  <h3 class="font-semibold text-[#043355] text-base">Youssef Mollar</h3>
                  <p class="text-[#6B7280] text-sm">70%</p>
                </div>
              </div>
              <button class="bg-white border-2 border-[#3B82F6] text-[#3B82F6] hover:bg-[#3B82F6] hover:text-white px-8 py-2 rounded-full font-semibold transition-all duration-200">
                Add
              </button>
            </div>

            <!-- Student 4 -->
            <div class="bg-white rounded-2xl border border-[#E5E7EB] p-6 flex items-center justify-between hover:shadow-md transition-shadow">
              <div class="flex items-center gap-4">
                <img src="https://randomuser.me/api/portraits/women/28.jpg" alt="Lila Youssef" class="w-12 h-12 rounded-full object-cover">
                <div>
                  <h3 class="font-semibold text-[#043355] text-base">Lila Youssef</h3>
                  <p class="text-[#6B7280] text-sm">65%</p>
                </div>
              </div>
              <button class="bg-white border-2 border-[#3B82F6] text-[#3B82F6] hover:bg-[#3B82F6] hover:text-white px-8 py-2 rounded-full font-semibold transition-all duration-200">
                Add
              </button>
            </div>

            <!-- Student 5 -->
            <div class="bg-white rounded-2xl border border-[#E5E7EB] p-6 flex items-center justify-between hover:shadow-md transition-shadow">
              <div class="flex items-center gap-4">
                <img src="https://randomuser.me/api/portraits/women/35.jpg" alt="Nany Jan" class="w-12 h-12 rounded-full object-cover">
                <div>
                  <h3 class="font-semibold text-[#043355] text-base">Nany Jan</h3>
                  <p class="text-[#6B7280] text-sm">85%</p>
                </div>
              </div>
              <button class="bg-white border-2 border-[#3B82F6] text-[#3B82F6] hover:bg-[#3B82F6] hover:text-white px-8 py-2 rounded-full font-semibold transition-all duration-200">
                Add
              </button>
            </div>

            <!-- Student 6 -->
            <div class="bg-white rounded-2xl border border-[#E5E7EB] p-6 flex items-center justify-between hover:shadow-md transition-shadow">
              <div class="flex items-center gap-4">
                <img src="https://randomuser.me/api/portraits/men/40.jpg" alt="Youssef Mollar" class="w-12 h-12 rounded-full object-cover">
                <div>
                  <h3 class="font-semibold text-[#043355] text-base">Youssef Mollar</h3>
                  <p class="text-[#6B7280] text-sm">70%</p>
                </div>
              </div>
              <button class="bg-white border-2 border-[#3B82F6] text-[#3B82F6] hover:bg-[#3B82F6] hover:text-white px-8 py-2 rounded-full font-semibold transition-all duration-200">
                Add
              </button>
            </div>

            <!-- Student 7 -->
            <div class="bg-white rounded-2xl border border-[#E5E7EB] p-6 flex items-center justify-between hover:shadow-md transition-shadow">
              <div class="flex items-center gap-4">
                <img src="https://randomuser.me/api/portraits/women/42.jpg" alt="Nany Jan" class="w-12 h-12 rounded-full object-cover">
                <div>
                  <h3 class="font-semibold text-[#043355] text-base">Nany Jan</h3>
                  <p class="text-[#6B7280] text-sm">85%</p>
                </div>
              </div>
              <button class="bg-white border-2 border-[#3B82F6] text-[#3B82F6] hover:bg-[#3B82F6] hover:text-white px-8 py-2 rounded-full font-semibold transition-all duration-200">
                Add
              </button>
            </div>
          </div>
        </div>

        <!-- Group A1 Content -->
        <div v-if="activeGroupTab === 'groupA1'">
          <div class="space-y-4 mb-8">
            <!-- Student 1 -->
            <div class="bg-white rounded-2xl border border-[#E5E7EB] p-6 flex items-center justify-between hover:shadow-md transition-shadow">
              <div class="flex items-center gap-4">
                <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Lila Youssef" class="w-12 h-12 rounded-full object-cover">
                <div>
                  <h3 class="font-semibold text-[#043355] text-base">Lila Youssef</h3>
                </div>
              </div>
              <div class="flex gap-2">
                <button class="bg-[#FFC107] hover:bg-[#FFB300] text-white px-6 py-2 rounded-full font-semibold transition-colors">
                  Edit
                </button>
                <button class="bg-[#DC2626] hover:bg-[#B91C1C] text-white px-6 py-2 rounded-full font-semibold transition-colors">
                  Delete
                </button>
              </div>
            </div>

            <!-- Student 2 -->
            <div class="bg-white rounded-2xl border border-[#E5E7EB] p-6 flex items-center justify-between hover:shadow-md transition-shadow">
              <div class="flex items-center gap-4">
                <img src="https://randomuser.me/api/portraits/women/32.jpg" alt="Nora Saad" class="w-12 h-12 rounded-full object-cover">
                <div>
                  <h3 class="font-semibold text-[#043355] text-base">Nora Saad</h3>
                </div>
              </div>
              <div class="flex gap-2">
                <button class="bg-[#FFC107] hover:bg-[#FFB300] text-white px-6 py-2 rounded-full font-semibold transition-colors">
                  Edit
                </button>
                <button class="bg-[#DC2626] hover:bg-[#B91C1C] text-white px-6 py-2 rounded-full font-semibold transition-colors">
                  Delete
                </button>
              </div>
            </div>

            <!-- Student 3 -->
            <div class="bg-white rounded-2xl border border-[#E5E7EB] p-6 flex items-center justify-between hover:shadow-md transition-shadow">
              <div class="flex items-center gap-4">
                <img src="https://randomuser.me/api/portraits/men/25.jpg" alt="Mollar Youssef" class="w-12 h-12 rounded-full object-cover">
                <div>
                  <h3 class="font-semibold text-[#043355] text-base">Mollar Youssef</h3>
                </div>
              </div>
              <div class="flex gap-2">
                <button class="bg-[#FFC107] hover:bg-[#FFB300] text-white px-6 py-2 rounded-full font-semibold transition-colors">
                  Edit
                </button>
                <button class="bg-[#DC2626] hover:bg-[#B91C1C] text-white px-6 py-2 rounded-full font-semibold transition-colors">
                  Delete
                </button>
              </div>
            </div>

            <!-- Student 4 -->
            <div class="bg-white rounded-2xl border border-[#E5E7EB] p-6 flex items-center justify-between hover:shadow-md transition-shadow">
              <div class="flex items-center gap-4">
                <img src="https://randomuser.me/api/portraits/women/28.jpg" alt="Lila Youssef" class="w-12 h-12 rounded-full object-cover">
                <div>
                  <h3 class="font-semibold text-[#043355] text-base">Lila Youssef</h3>
                </div>
              </div>
              <div class="flex gap-2">
                <button class="bg-[#FFC107] hover:bg-[#FFB300] text-white px-6 py-2 rounded-full font-semibold transition-colors">
                  Edit
                </button>
                <button class="bg-[#DC2626] hover:bg-[#B91C1C] text-white px-6 py-2 rounded-full font-semibold transition-colors">
                  Delete
                </button>
              </div>
            </div>

            <!-- Student 5 -->
            <div class="bg-white rounded-2xl border border-[#E5E7EB] p-6 flex items-center justify-between hover:shadow-md transition-shadow">
              <div class="flex items-center gap-4">
                <img src="https://randomuser.me/api/portraits/women/35.jpg" alt="Nora Saad" class="w-12 h-12 rounded-full object-cover">
                <div>
                  <h3 class="font-semibold text-[#043355] text-base">Nora Saad</h3>
                </div>
              </div>
              <div class="flex gap-2">
                <button class="bg-[#FFC107] hover:bg-[#FFB300] text-white px-6 py-2 rounded-full font-semibold transition-colors">
                  Edit
                </button>
                <button class="bg-[#DC2626] hover:bg-[#B91C1C] text-white px-6 py-2 rounded-full font-semibold transition-colors">
                  Delete
                </button>
              </div>
            </div>

            <!-- Student 6 -->
            <div class="bg-white rounded-2xl border border-[#E5E7EB] p-6 flex items-center justify-between hover:shadow-md transition-shadow">
              <div class="flex items-center gap-4">
                <img src="https://randomuser.me/api/portraits/men/40.jpg" alt="Mollar Youssef" class="w-12 h-12 rounded-full object-cover">
                <div>
                  <h3 class="font-semibold text-[#043355] text-base">Mollar Youssef</h3>
                </div>
              </div>
              <div class="flex gap-2">
                <button class="bg-[#FFC107] hover:bg-[#FFB300] text-white px-6 py-2 rounded-full font-semibold transition-colors">
                  Edit
                </button>
                <button class="bg-[#DC2626] hover:bg-[#B91C1C] text-white px-6 py-2 rounded-full font-semibold transition-colors">
                  Delete
                </button>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex justify-center gap-4 mt-8">
            <a 
              href="https://wa.me/1234567890?text=Hello%2C%20I%20need%20help%20with%20groups" 
              target="_blank"
              class="bg-[#25D366] hover:bg-[#20BA5A] text-white px-8 py-3 rounded-lg font-semibold transition-all duration-300 flex items-center gap-3 shadow-lg hover:shadow-xl transform hover:scale-105"
            >
              <svg width="20" height="20" fill="white" viewBox="0 0 24 24">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.465 3.516z"/>
              </svg>
              What's App
            </a>
            <button 
              @click="showAddGroup"
              class="bg-[#3B82F6] hover:bg-[#2563EB] text-white px-8 py-3 rounded-lg font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
            >
              + Add Group
            </button>
            <button class="bg-[#DC2626] hover:bg-[#B91C1C] text-white px-8 py-3 rounded-lg font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
              Delete Group
            </button>
          </div>
        </div>

        <!-- Group B2 Content -->
        <div v-if="activeGroupTab === 'groupB2'" class="bg-white rounded-2xl shadow p-8 text-center">
          <h2 class="text-2xl font-bold text-[#043355] mb-4">Group B2</h2>
          <p class="text-gray-500 text-lg">Students will be displayed here...</p>
        </div>

        <!-- Group C3 Content -->
        <div v-if="activeGroupTab === 'groupC3'" class="bg-white rounded-2xl shadow p-8 text-center">
          <h2 class="text-2xl font-bold text-[#043355] mb-4">Group C3</h2>
          <p class="text-gray-500 text-lg">Students will be displayed here...</p>
        </div>
      </div>

      <!-- Add Team Page -->
      <div v-if="activeSection === 'teams' && showAddTeamPage" class="px-10 pt-8 pb-10 min-h-screen">
        <div class="max-w-2xl mx-auto">
          <!-- Back Button -->
          <button 
            @click="hideAddTeam" 
            class="mb-8 flex items-center gap-2 text-[#043355] hover:text-[#2563EB] transition-colors"
          >
            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
            Back to Teams
          </button>

          <div class="bg-white rounded-3xl shadow-lg p-12">
            <!-- Team Name Field -->
            <div class="mb-8">
              <label class="block text-[#043355] text-xl font-bold mb-6">Team Name :</label>
              <input 
                type="text" 
                placeholder="Group D1" 
                class="w-full p-4 text-lg border border-[#E5E7EB] rounded-2xl bg-[#F8FAFC] focus:outline-none focus:ring-2 focus:ring-[#3B82F6] focus:border-transparent transition-all"
              />
            </div>

            <!-- Select Team Field -->
            <div class="mb-12">
              <label class="block text-[#043355] text-xl font-bold mb-6">Select Team :</label>
              <div class="relative">
                <select class="w-full p-4 text-lg border border-[#E5E7EB] rounded-2xl bg-[#F8FAFC] focus:outline-none focus:ring-2 focus:ring-[#3B82F6] focus:border-transparent transition-all appearance-none cursor-pointer">
                  <option>Select...</option>
                  <option>Team 1</option>
                  <option>Team 2</option>
                  <option>Team 3</option>
                  <option>Team 4</option>
                  <option>Team 5</option>
                </select>
                <svg class="absolute right-4 top-1/2 transform -translate-y-1/2 w-6 h-6 text-gray-400 pointer-events-none" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                </svg>
              </div>
            </div>

            <!-- Add Button -->
            <div class="flex justify-end">
              <button class="bg-[#3B82F6] hover:bg-[#2563EB] text-white px-12 py-4 rounded-full text-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                Add
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Add Group Page -->
      <div v-if="activeSection === 'groups' && showAddGroupPage" class="px-10 pt-8 pb-10 min-h-screen">
        <div class="max-w-2xl mx-auto">
          <!-- Back Button -->
          <button 
            @click="hideAddGroup" 
            class="mb-8 flex items-center gap-2 text-[#043355] hover:text-[#2563EB] transition-colors"
          >
            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
            Back to Groups
          </button>

          <div class="bg-white rounded-3xl shadow-lg p-12">
            <!-- Group Name Field -->
            <div class="mb-8">
              <label class="block text-[#043355] text-xl font-bold mb-6">Group Name :</label>
              <input 
                type="text" 
                placeholder="Group D1" 
                class="w-full p-4 text-lg border border-[#E5E7EB] rounded-2xl bg-[#F8FAFC] focus:outline-none focus:ring-2 focus:ring-[#3B82F6] focus:border-transparent transition-all"
              />
            </div>

            <!-- Select Students Field -->
            <div class="mb-12">
              <label class="block text-[#043355] text-xl font-bold mb-6">select students :</label>
              <div class="relative">
                <select class="w-full p-4 text-lg border border-[#E5E7EB] rounded-2xl bg-[#F8FAFC] focus:outline-none focus:ring-2 focus:ring-[#3B82F6] focus:border-transparent transition-all appearance-none cursor-pointer">
                  <option>Select...</option>
                  <option>Lila Youssef</option>
                  <option>Nany Jan</option>
                  <option>Youssef Mollar</option>
                  <option>Nora Saad</option>
                  <option>Mollar Youssef</option>
                </select>
                <svg class="absolute right-4 top-1/2 transform -translate-y-1/2 w-6 h-6 text-gray-400 pointer-events-none" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                </svg>
              </div>
            </div>

            <!-- Add Button -->
            <div class="flex justify-end">
              <button class="bg-[#3B82F6] hover:bg-[#2563EB] text-white px-12 py-4 rounded-full text-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                Add
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Teams Content -->
      <div v-if="activeSection === 'teams' && !showAddTeamPage" class="px-10 pt-8 pb-10">
        <h1 class="text-4xl font-extrabold text-[#043355] mb-8">Teams</h1>
        
        <!-- Team Tabs -->
        <div class="flex bg-gray-100 rounded-lg p-1 mb-8 max-w-2xl">
          <button 
            @click="activeTeamTab = 'waiting'" 
            :class="activeTeamTab === 'waiting' ? 'bg-white text-[#043355] shadow-sm font-semibold' : 'text-gray-600 hover:bg-white hover:shadow-sm'"
            class="px-6 py-3 rounded-lg transition-all flex-1"
          >
            Waiting List
          </button>
          <button 
            @click="activeTeamTab = 'team1'" 
            :class="activeTeamTab === 'team1' ? 'bg-white text-[#043355] shadow-sm font-semibold' : 'text-gray-600 hover:bg-white hover:shadow-sm'"
            class="px-6 py-3 rounded-lg transition-all flex-1"
          >
            Team 1
          </button>
          <button 
            @click="activeTeamTab = 'team2'" 
            :class="activeTeamTab === 'team2' ? 'bg-white text-[#043355] shadow-sm font-semibold' : 'text-gray-600 hover:bg-white hover:shadow-sm'"
            class="px-6 py-3 rounded-lg transition-all flex-1"
          >
            Team 2
          </button>
          <button 
            @click="activeTeamTab = 'team3'" 
            :class="activeTeamTab === 'team3' ? 'bg-white text-[#043355] shadow-sm font-semibold' : 'text-gray-600 hover:bg-white hover:shadow-sm'"
            class="px-6 py-3 rounded-lg transition-all flex-1"
          >
            Team 3
          </button>
        </div>

        <!-- Team Content -->
        <div v-if="activeTeamTab === 'waiting'" class="space-y-4 mb-8">
          <!-- Student 1 -->
          <div class="bg-white rounded-2xl border border-[#E5E7EB] p-6 flex items-center justify-between hover:shadow-md transition-shadow">
            <div class="flex items-center gap-4">
              <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Lila Youssef" class="w-12 h-12 rounded-full object-cover">
              <h3 class="font-semibold text-[#043355] text-base">Lila Youssef</h3>
            </div>
            <button class="bg-white border-2 border-[#3B82F6] text-[#3B82F6] hover:bg-[#3B82F6] hover:text-white px-8 py-2 rounded-full font-semibold transition-all duration-200">
              Add
            </button>
          </div>

          <!-- Student 2 -->
          <div class="bg-white rounded-2xl border border-[#E5E7EB] p-6 flex items-center justify-between hover:shadow-md transition-shadow">
            <div class="flex items-center gap-4">
              <img src="https://randomuser.me/api/portraits/women/32.jpg" alt="Nany Jan" class="w-12 h-12 rounded-full object-cover">
              <h3 class="font-semibold text-[#043355] text-base">Nany Jan</h3>
            </div>
            <button class="bg-white border-2 border-[#3B82F6] text-[#3B82F6] hover:bg-[#3B82F6] hover:text-white px-8 py-2 rounded-full font-semibold transition-all duration-200">
              Add
            </button>
          </div>

          <!-- Student 3 -->
          <div class="bg-white rounded-2xl border border-[#E5E7EB] p-6 flex items-center justify-between hover:shadow-md transition-shadow">
            <div class="flex items-center gap-4">
              <img src="https://randomuser.me/api/portraits/men/25.jpg" alt="Youssef Mollar" class="w-12 h-12 rounded-full object-cover">
              <h3 class="font-semibold text-[#043355] text-base">Youssef Mollar</h3>
            </div>
            <button class="bg-white border-2 border-[#3B82F6] text-[#3B82F6] hover:bg-[#3B82F6] hover:text-white px-8 py-2 rounded-full font-semibold transition-all duration-200">
              Add
            </button>
          </div>

          <!-- Student 4 -->
          <div class="bg-white rounded-2xl border border-[#E5E7EB] p-6 flex items-center justify-between hover:shadow-md transition-shadow">
            <div class="flex items-center gap-4">
              <img src="https://randomuser.me/api/portraits/women/28.jpg" alt="Lila Youssef" class="w-12 h-12 rounded-full object-cover">
              <h3 class="font-semibold text-[#043355] text-base">Lila Youssef</h3>
            </div>
            <button class="bg-white border-2 border-[#3B82F6] text-[#3B82F6] hover:bg-[#3B82F6] hover:text-white px-8 py-2 rounded-full font-semibold transition-all duration-200">
              Add
            </button>
          </div>

          <!-- Student 5 -->
          <div class="bg-white rounded-2xl border border-[#E5E7EB] p-6 flex items-center justify-between hover:shadow-md transition-shadow">
            <div class="flex items-center gap-4">
              <img src="https://randomuser.me/api/portraits/women/35.jpg" alt="Nany Jan" class="w-12 h-12 rounded-full object-cover">
              <h3 class="font-semibold text-[#043355] text-base">Nany Jan</h3>
            </div>
            <button class="bg-white border-2 border-[#3B82F6] text-[#3B82F6] hover:bg-[#3B82F6] hover:text-white px-8 py-2 rounded-full font-semibold transition-all duration-200">
              Add
            </button>
          </div>

          <!-- Student 6 -->
          <div class="bg-white rounded-2xl border border-[#E5E7EB] p-6 flex items-center justify-between hover:shadow-md transition-shadow">
            <div class="flex items-center gap-4">
              <img src="https://randomuser.me/api/portraits/men/40.jpg" alt="Youssef Mollar" class="w-12 h-12 rounded-full object-cover">
              <h3 class="font-semibold text-[#043355] text-base">Youssef Mollar</h3>
            </div>
            <button class="bg-white border-2 border-[#3B82F6] text-[#3B82F6] hover:bg-[#3B82F6] hover:text-white px-8 py-2 rounded-full font-semibold transition-all duration-200">
              Add
            </button>
          </div>

          <!-- Student 7 -->
          <div class="bg-white rounded-2xl border border-[#E5E7EB] p-6 flex items-center justify-between hover:shadow-md transition-shadow">
            <div class="flex items-center gap-4">
              <img src="https://randomuser.me/api/portraits/women/42.jpg" alt="Nany Jan" class="w-12 h-12 rounded-full object-cover">
              <h3 class="font-semibold text-[#043355] text-base">Nany Jan</h3>
            </div>
            <button class="bg-white border-2 border-[#3B82F6] text-[#3B82F6] hover:bg-[#3B82F6] hover:text-white px-8 py-2 rounded-full font-semibold transition-all duration-200">
              Add
            </button>
          </div>
        </div>

        <!-- Team 1/2/3 Content -->
        <div v-else class="space-y-4 mb-8">
          <!-- Student 1 -->
          <div class="bg-white rounded-2xl border border-[#E5E7EB] p-6 flex items-center justify-between hover:shadow-md transition-shadow">
            <div class="flex items-center gap-4">
              <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Lila Youssef" class="w-12 h-12 rounded-full object-cover">
              <h3 class="font-semibold text-[#043355] text-base">Lila Youssef</h3>
            </div>
            <div class="flex gap-2">
              <button class="bg-[#FFC107] hover:bg-[#FFB300] text-white px-6 py-2 rounded-full font-semibold transition-colors">
                Edit
              </button>
              <button class="bg-[#DC2626] hover:bg-[#B91C1C] text-white px-6 py-2 rounded-full font-semibold transition-colors">
                Delete
              </button>
            </div>
          </div>

          <!-- Student 2 -->
          <div class="bg-white rounded-2xl border border-[#E5E7EB] p-6 flex items-center justify-between hover:shadow-md transition-shadow">
            <div class="flex items-center gap-4">
              <img src="https://randomuser.me/api/portraits/women/32.jpg" alt="Nora Saad" class="w-12 h-12 rounded-full object-cover">
              <h3 class="font-semibold text-[#043355] text-base">Nora Saad</h3>
            </div>
            <div class="flex gap-2">
              <button class="bg-[#FFC107] hover:bg-[#FFB300] text-white px-6 py-2 rounded-full font-semibold transition-colors">
                Edit
              </button>
              <button class="bg-[#DC2626] hover:bg-[#B91C1C] text-white px-6 py-2 rounded-full font-semibold transition-colors">
                Delete
              </button>
            </div>
          </div>

          <!-- Student 3 -->
          <div class="bg-white rounded-2xl border border-[#E5E7EB] p-6 flex items-center justify-between hover:shadow-md transition-shadow">
            <div class="flex items-center gap-4">
              <img src="https://randomuser.me/api/portraits/men/25.jpg" alt="Mollar Youssef" class="w-12 h-12 rounded-full object-cover">
              <h3 class="font-semibold text-[#043355] text-base">Mollar Youssef</h3>
            </div>
            <div class="flex gap-2">
              <button class="bg-[#FFC107] hover:bg-[#FFB300] text-white px-6 py-2 rounded-full font-semibold transition-colors">
                Edit
              </button>
              <button class="bg-[#DC2626] hover:bg-[#B91C1C] text-white px-6 py-2 rounded-full font-semibold transition-colors">
                Delete
              </button>
            </div>
          </div>

          <!-- Student 4 -->
          <div class="bg-white rounded-2xl border border-[#E5E7EB] p-6 flex items-center justify-between hover:shadow-md transition-shadow">
            <div class="flex items-center gap-4">
              <img src="https://randomuser.me/api/portraits/women/28.jpg" alt="Lila Youssef" class="w-12 h-12 rounded-full object-cover">
              <h3 class="font-semibold text-[#043355] text-base">Lila Youssef</h3>
            </div>
            <div class="flex gap-2">
              <button class="bg-[#FFC107] hover:bg-[#FFB300] text-white px-6 py-2 rounded-full font-semibold transition-colors">
                Edit
              </button>
              <button class="bg-[#DC2626] hover:bg-[#B91C1C] text-white px-6 py-2 rounded-full font-semibold transition-colors">
                Delete
              </button>
            </div>
          </div>

          <!-- Student 5 -->
          <div class="bg-white rounded-2xl border border-[#E5E7EB] p-6 flex items-center justify-between hover:shadow-md transition-shadow">
            <div class="flex items-center gap-4">
              <img src="https://randomuser.me/api/portraits/women/35.jpg" alt="Nora Saad" class="w-12 h-12 rounded-full object-cover">
              <h3 class="font-semibold text-[#043355] text-base">Nora Saad</h3>
            </div>
            <div class="flex gap-2">
              <button class="bg-[#FFC107] hover:bg-[#FFB300] text-white px-6 py-2 rounded-full font-semibold transition-colors">
                Edit
              </button>
              <button class="bg-[#DC2626] hover:bg-[#B91C1C] text-white px-6 py-2 rounded-full font-semibold transition-colors">
                Delete
              </button>
            </div>
          </div>

          <!-- Student 6 -->
          <div class="bg-white rounded-2xl border border-[#E5E7EB] p-6 flex items-center justify-between hover:shadow-md transition-shadow">
            <div class="flex items-center gap-4">
              <img src="https://randomuser.me/api/portraits/men/40.jpg" alt="Mollar Youssef" class="w-12 h-12 rounded-full object-cover">
              <h3 class="font-semibold text-[#043355] text-base">Mollar Youssef</h3>
            </div>
            <div class="flex gap-2">
              <button class="bg-[#FFC107] hover:bg-[#FFB300] text-white px-6 py-2 rounded-full font-semibold transition-colors">
                Edit
              </button>
              <button class="bg-[#DC2626] hover:bg-[#B91C1C] text-white px-6 py-2 rounded-full font-semibold transition-colors">
                Delete
              </button>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-center gap-4 mt-8">
          <button 
            @click="showAddTeam"
            class="bg-[#3B82F6] hover:bg-[#2563EB] text-white px-8 py-3 rounded-lg font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center gap-2"
          >
            <svg width="16" height="16" fill="white" viewBox="0 0 24 24">
              <path d="M12 5v14m7-7H5"/>
            </svg>
            Add New Team
          </button>
          <button class="bg-[#DC2626] hover:bg-[#B91C1C] text-white px-8 py-3 rounded-lg font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center gap-2">
            <svg width="16" height="16" fill="white" viewBox="0 0 24 24">
              <path d="M18 6L6 18M6 6l12 12"/>
            </svg>
            Delete Team
          </button>
        </div>
      </div>

      <!-- Courses Content -->
      <div v-if="activeSection === 'courses' && !showAddCoursePage && !showAddLessonPage && !showEditLessonPage" class="px-10 pt-8 pb-10">
        <h1 class="text-4xl font-extrabold text-[#043355] mb-8">Courses List</h1>
        
        <!-- Basic Course Section -->
        <div class="mb-8">
          <div class="bg-white rounded-2xl border border-[#E5E7EB] overflow-hidden">
            <!-- Basic Course Header -->
            <div class="bg-[#F8FAFC] px-6 py-4 border-b border-[#E5E7EB] flex items-center justify-between">
              <h2 class="text-xl font-bold text-[#043355]">Basic Course</h2>
              <svg width="20" height="20" fill="#043355" viewBox="0 0 24 24">
                <path d="M6 9l6 6 6-6"/>
              </svg>
            </div>
            
            <!-- Lesson 1 -->
            <div class="p-6 border-b border-[#E5E7EB] flex items-start gap-4">
              <div class="relative">
                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=120&h=80&fit=crop&crop=face" 
                     alt="Lesson 1" class="w-24 h-20 rounded-lg object-cover">
                <div class="absolute inset-0 flex items-center justify-center">
                  <div class="bg-black bg-opacity-50 rounded-full p-2">
                    <svg width="16" height="16" fill="white" viewBox="0 0 24 24">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </div>
                </div>
              </div>
              <div class="flex-1">
                <h3 class="text-lg font-bold text-[#043355] mb-2">Lesson 1</h3>
                <p class="text-sm text-gray-600 mb-3">risus felis, turpis ultrices quam sit turpis non, lacus orci at odio tincidunt sit felis...</p>
                <div class="flex items-center gap-4">
                  <div class="flex items-center gap-1 text-red-600">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                    </svg>
                    <span class="text-sm font-medium">PDF</span>
                  </div>
                  <div class="flex items-center gap-1 text-orange-500">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"/>
                    </svg>
                    <span class="text-sm font-medium">Quiz</span>
                  </div>
                </div>
              </div>
              <div class="flex gap-2">
                <button 
                  @click="showEditLesson"
                  class="bg-[#FFC107] hover:bg-[#FFB300] text-white px-4 py-2 rounded-full font-semibold transition-colors"
                >
                  Edit
                </button>
                <button class="bg-[#DC2626] hover:bg-[#B91C1C] text-white px-4 py-2 rounded-full font-semibold transition-colors">
                  Delete
                </button>
              </div>
            </div>

            <!-- Lesson 2 -->
            <div class="p-6 border-b border-[#E5E7EB] flex items-start gap-4">
              <div class="relative">
                <img src="https://images.unsplash.com/photo-1522075469751-3a6694fb2f61?w=120&h=80&fit=crop&crop=face" 
                     alt="Lesson 2" class="w-24 h-20 rounded-lg object-cover">
                <div class="absolute inset-0 flex items-center justify-center">
                  <div class="bg-black bg-opacity-50 rounded-full p-2">
                    <svg width="16" height="16" fill="white" viewBox="0 0 24 24">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </div>
                </div>
              </div>
              <div class="flex-1">
                <h3 class="text-lg font-bold text-[#043355] mb-2">Lesson 2</h3>
                <p class="text-sm text-gray-600 mb-3">risus felis, turpis ultrices quam sit turpis non, lacus orci at odio tincidunt sit felis...</p>
                <div class="flex items-center gap-4">
                  <div class="flex items-center gap-1 text-red-600">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                    </svg>
                    <span class="text-sm font-medium">PDF</span>
                  </div>
                  <div class="flex items-center gap-1 text-orange-500">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"/>
                    </svg>
                    <span class="text-sm font-medium">Quiz</span>
                  </div>
                </div>
              </div>
              <div class="flex gap-2">
                <button 
                  @click="showEditLesson"
                  class="bg-[#FFC107] hover:bg-[#FFB300] text-white px-4 py-2 rounded-full font-semibold transition-colors"
                >
                  Edit
                </button>
                <button class="bg-[#DC2626] hover:bg-[#B91C1C] text-white px-4 py-2 rounded-full font-semibold transition-colors">
                  Delete
                </button>
              </div>
            </div>

            <!-- Lesson 3 -->
            <div class="p-6 flex items-start gap-4">
              <div class="relative">
                <img src="https://images.unsplash.com/photo-1509062522246-3755977927d7?w=120&h=80&fit=crop&crop=faces" 
                     alt="Lesson 3" class="w-24 h-20 rounded-lg object-cover">
                <div class="absolute inset-0 flex items-center justify-center">
                  <div class="bg-black bg-opacity-50 rounded-full p-2">
                    <svg width="16" height="16" fill="white" viewBox="0 0 24 24">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </div>
                </div>
              </div>
              <div class="flex-1">
                <h3 class="text-lg font-bold text-[#043355] mb-2">Lesson 3</h3>
                <p class="text-sm text-gray-600 mb-3">risus felis, turpis ultrices quam sit turpis non, lacus orci at odio tincidunt sit felis...</p>
                <div class="flex items-center gap-4">
                  <div class="flex items-center gap-1 text-red-600">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                    </svg>
                    <span class="text-sm font-medium">PDF</span>
                  </div>
                  <div class="flex items-center gap-1 text-orange-500">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"/>
                    </svg>
                    <span class="text-sm font-medium">Quiz</span>
                  </div>
                </div>
              </div>
              <div class="flex gap-2">
                <button 
                  @click="showEditLesson"
                  class="bg-[#FFC107] hover:bg-[#FFB300] text-white px-4 py-2 rounded-full font-semibold transition-colors"
                >
                  Edit
                </button>
                <button class="bg-[#DC2626] hover:bg-[#B91C1C] text-white px-4 py-2 rounded-full font-semibold transition-colors">
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Advanced Section -->
        <div class="mb-8">
          <div class="bg-white rounded-2xl border border-[#E5E7EB] overflow-hidden">
            <!-- Advanced Header -->
            <div class="bg-[#F8FAFC] px-6 py-4 border-b border-[#E5E7EB] flex items-center justify-between">
              <h2 class="text-xl font-bold text-[#043355]">Advanced</h2>
              <svg width="20" height="20" fill="#043355" viewBox="0 0 24 24">
                <path d="M6 9l6 6 6-6"/>
              </svg>
            </div>
            
            <!-- Advanced Lesson 1 -->
            <div class="p-6 border-b border-[#E5E7EB] flex items-start gap-4">
              <div class="relative">
                <img src="https://images.unsplash.com/photo-1503676260728-1c00da094a0b?w=120&h=80&fit=crop&crop=faces" 
                     alt="Advanced Lesson 1" class="w-24 h-20 rounded-lg object-cover">
                <div class="absolute inset-0 flex items-center justify-center">
                  <div class="bg-black bg-opacity-50 rounded-full p-2">
                    <svg width="16" height="16" fill="white" viewBox="0 0 24 24">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </div>
                </div>
              </div>
              <div class="flex-1">
                <h3 class="text-lg font-bold text-[#043355] mb-2">Lesson 1</h3>
                <p class="text-sm text-gray-600 mb-3">risus felis, turpis ultrices quam sit turpis non, lacus orci at odio tincidunt sit felis...</p>
                <div class="flex items-center gap-4">
                  <div class="flex items-center gap-1 text-red-600">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                    </svg>
                    <span class="text-sm font-medium">PDF</span>
                  </div>
                  <div class="flex items-center gap-1 text-orange-500">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"/>
                    </svg>
                    <span class="text-sm font-medium">Quiz</span>
                  </div>
                </div>
              </div>
              <div class="flex gap-2">
                <button @click="showEditLesson" class="bg-[#FFC107] hover:bg-[#FFB300] text-white px-4 py-2 rounded-full font-semibold transition-colors">
                  Edit
                </button>
                <button class="bg-[#DC2626] hover:bg-[#B91C1C] text-white px-4 py-2 rounded-full font-semibold transition-colors">
                  Delete
                </button>
              </div>
            </div>

            <!-- Advanced Lesson 2 -->
            <div class="p-6 border-b border-[#E5E7EB] flex items-start gap-4">
              <div class="relative">
                <img src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=120&h=80&fit=crop&crop=faces" 
                     alt="Advanced Lesson 2" class="w-24 h-20 rounded-lg object-cover">
                <div class="absolute inset-0 flex items-center justify-center">
                  <div class="bg-black bg-opacity-50 rounded-full p-2">
                    <svg width="16" height="16" fill="white" viewBox="0 0 24 24">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </div>
                </div>
              </div>
              <div class="flex-1">
                <h3 class="text-lg font-bold text-[#043355] mb-2">Lesson 2</h3>
                <p class="text-sm text-gray-600 mb-3">risus felis, turpis ultrices quam sit turpis non, lacus orci at odio tincidunt sit felis...</p>
                <div class="flex items-center gap-4">
                  <div class="flex items-center gap-1 text-red-600">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                    </svg>
                    <span class="text-sm font-medium">PDF</span>
                  </div>
                  <div class="flex items-center gap-1 text-orange-500">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"/>
                    </svg>
                    <span class="text-sm font-medium">Quiz</span>
                  </div>
                </div>
              </div>
              <div class="flex gap-2">
                <button @click="showEditLesson" class="bg-[#FFC107] hover:bg-[#FFB300] text-white px-4 py-2 rounded-full font-semibold transition-colors">
                  Edit
                </button>
                <button class="bg-[#DC2626] hover:bg-[#B91C1C] text-white px-4 py-2 rounded-full font-semibold transition-colors">
                  Delete
                </button>
              </div>
            </div>

            <!-- Advanced Lesson 3 -->
            <div class="p-6 flex items-start gap-4">
              <div class="relative">
                <img src="https://images.unsplash.com/photo-1544717297-fa95b6ee9643?w=120&h=80&fit=crop&crop=faces" 
                     alt="Advanced Lesson 3" class="w-24 h-20 rounded-lg object-cover">
                <div class="absolute inset-0 flex items-center justify-center">
                  <div class="bg-black bg-opacity-50 rounded-full p-2">
                    <svg width="16" height="16" fill="white" viewBox="0 0 24 24">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </div>
                </div>
              </div>
              <div class="flex-1">
                <h3 class="text-lg font-bold text-[#043355] mb-2">Lesson 3</h3>
                <p class="text-sm text-gray-600 mb-3">risus felis, turpis ultrices quam sit turpis non, lacus orci at odio tincidunt sit felis...</p>
                <div class="flex items-center gap-4">
                  <div class="flex items-center gap-1 text-red-600">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                    </svg>
                    <span class="text-sm font-medium">PDF</span>
                  </div>
                  <div class="flex items-center gap-1 text-orange-500">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"/>
                    </svg>
                    <span class="text-sm font-medium">Quiz</span>
                  </div>
                </div>
              </div>
              <div class="flex gap-2">
                <button @click="showEditLesson" class="bg-[#FFC107] hover:bg-[#FFB300] text-white px-4 py-2 rounded-full font-semibold transition-colors">
                  Edit
                </button>
                <button class="bg-[#DC2626] hover:bg-[#B91C1C] text-white px-4 py-2 rounded-full font-semibold transition-colors">
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Expert Section -->
        <div class="mb-8">
          <div class="bg-white rounded-2xl border border-[#E5E7EB] overflow-hidden">
            <!-- Expert Header -->
            <div class="bg-[#F8FAFC] px-6 py-4 border-b border-[#E5E7EB] flex items-center justify-between">
              <h2 class="text-xl font-bold text-[#043355]">Expert</h2>
              <svg width="20" height="20" fill="#043355" viewBox="0 0 24 24">
                <path d="M6 9l6 6 6-6"/>
              </svg>
            </div>
            
            <!-- Expert Lesson 1 -->
            <div class="p-6 border-b border-[#E5E7EB] flex items-start gap-4">
              <div class="relative">
                <img src="https://images.unsplash.com/photo-1573164713714-d95e436ab8d6?w=120&h=80&fit=crop&crop=faces" 
                     alt="Expert Lesson 1" class="w-24 h-20 rounded-lg object-cover">
                <div class="absolute inset-0 flex items-center justify-center">
                  <div class="bg-black bg-opacity-50 rounded-full p-2">
                    <svg width="16" height="16" fill="white" viewBox="0 0 24 24">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </div>
                </div>
              </div>
              <div class="flex-1">
                <h3 class="text-lg font-bold text-[#043355] mb-2">Lesson 1</h3>
                <p class="text-sm text-gray-600 mb-3">risus felis, turpis ultrices quam sit turpis non, lacus orci at odio tincidunt sit felis...</p>
                <div class="flex items-center gap-4">
                  <div class="flex items-center gap-1 text-red-600">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                    </svg>
                    <span class="text-sm font-medium">PDF</span>
                  </div>
                  <div class="flex items-center gap-1 text-orange-500">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"/>
                    </svg>
                    <span class="text-sm font-medium">Quiz</span>
                  </div>
                </div>
              </div>
              <div class="flex gap-2">
                <button @click="showEditLesson" class="bg-[#FFC107] hover:bg-[#FFB300] text-white px-4 py-2 rounded-full font-semibold transition-colors">
                  Edit
                </button>
                <button class="bg-[#DC2626] hover:bg-[#B91C1C] text-white px-4 py-2 rounded-full font-semibold transition-colors">
                  Delete
                </button>
              </div>
            </div>

            <!-- Expert Lesson 2 -->
            <div class="p-6 border-b border-[#E5E7EB] flex items-start gap-4">
              <div class="relative">
                <img src="https://images.unsplash.com/photo-1556157382-97eda2d62296?w=120&h=80&fit=crop&crop=faces" 
                     alt="Expert Lesson 2" class="w-24 h-20 rounded-lg object-cover">
                <div class="absolute inset-0 flex items-center justify-center">
                  <div class="bg-black bg-opacity-50 rounded-full p-2">
                    <svg width="16" height="16" fill="white" viewBox="0 0 24 24">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </div>
                </div>
              </div>
              <div class="flex-1">
                <h3 class="text-lg font-bold text-[#043355] mb-2">Lesson 2</h3>
                <p class="text-sm text-gray-600 mb-3">risus felis, turpis ultrices quam sit turpis non, lacus orci at odio tincidunt sit felis...</p>
                <div class="flex items-center gap-4">
                  <div class="flex items-center gap-1 text-red-600">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                    </svg>
                    <span class="text-sm font-medium">PDF</span>
                  </div>
                  <div class="flex items-center gap-1 text-orange-500">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"/>
                    </svg>
                    <span class="text-sm font-medium">Quiz</span>
                  </div>
                </div>
              </div>
              <div class="flex gap-2">
                <button @click="showEditLesson" class="bg-[#FFC107] hover:bg-[#FFB300] text-white px-4 py-2 rounded-full font-semibold transition-colors">
                  Edit
                </button>
                <button class="bg-[#DC2626] hover:bg-[#B91C1C] text-white px-4 py-2 rounded-full font-semibold transition-colors">
                  Delete
                </button>
              </div>
            </div>

            <!-- Expert Lesson 3 -->
            <div class="p-6 flex items-start gap-4">
              <div class="relative">
                <img src="https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=120&h=80&fit=crop&crop=faces" 
                     alt="Expert Lesson 3" class="w-24 h-20 rounded-lg object-cover">
                <div class="absolute inset-0 flex items-center justify-center">
                  <div class="bg-black bg-opacity-50 rounded-full p-2">
                    <svg width="16" height="16" fill="white" viewBox="0 0 24 24">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </div>
                </div>
              </div>
              <div class="flex-1">
                <h3 class="text-lg font-bold text-[#043355] mb-2">Lesson 3</h3>
                <p class="text-sm text-gray-600 mb-3">risus felis, turpis ultrices quam sit turpis non, lacus orci at odio tincidunt sit felis...</p>
                <div class="flex items-center gap-4">
                  <div class="flex items-center gap-1 text-red-600">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                    </svg>
                    <span class="text-sm font-medium">PDF</span>
                  </div>
                  <div class="flex items-center gap-1 text-orange-500">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"/>
                    </svg>
                    <span class="text-sm font-medium">Quiz</span>
                  </div>
                </div>
              </div>
              <div class="flex gap-2">
                <button @click="showEditLesson" class="bg-[#FFC107] hover:bg-[#FFB300] text-white px-4 py-2 rounded-full font-semibold transition-colors">
                  Edit
                </button>
                <button class="bg-[#DC2626] hover:bg-[#B91C1C] text-white px-4 py-2 rounded-full font-semibold transition-colors">
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Floating Add Lesson Button -->
        <div v-if="activeSection === 'courses'" class="fixed bottom-8 right-8">
          <button 
            @click="showAddLesson"
            class="bg-[#059669] hover:bg-[#047857] text-white px-6 py-3 rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center justify-center gap-2 font-semibold"
          >
            <svg width="20" height="20" fill="white" viewBox="0 0 24 24">
              <path d="M12 5v14m7-7H5" stroke="white" stroke-width="2" fill="none"/>
            </svg>
            <span>إضافة درس</span>
          </button>
        </div>
      </div>

      <!-- Add Course Page -->
      <div v-if="activeSection === 'courses' && showAddCoursePage" class="px-10 pt-8 pb-10 min-h-screen">
        <div class="max-w-2xl mx-auto">
          <!-- Back Button -->
          <button 
            @click="hideAddCourse" 
            class="mb-8 flex items-center gap-2 text-[#043355] hover:text-[#2563EB] transition-colors"
          >
            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
            Back to Courses
          </button>

          <div class="bg-white rounded-3xl shadow-lg p-12">
            <!-- Course Name Field -->
            <div class="mb-8">
              <label class="block text-[#043355] text-xl font-bold mb-6">Course Name :</label>
              <input 
                type="text" 
                placeholder="Soroban Level 1" 
                class="w-full p-4 text-lg border border-[#E5E7EB] rounded-2xl bg-[#F8FAFC] focus:outline-none focus:ring-2 focus:ring-[#3B82F6] focus:border-transparent transition-all"
              />
            </div>

            <!-- Select Level Field -->
            <div class="mb-12">
              <label class="block text-[#043355] text-xl font-bold mb-6">Select Level :</label>
              <div class="relative">
                <select class="w-full p-4 text-lg border border-[#E5E7EB] rounded-2xl bg-[#F8FAFC] focus:outline-none focus:ring-2 focus:ring-[#3B82F6] focus:border-transparent transition-all appearance-none cursor-pointer">
                  <option>Select...</option>
                  <option>Beginner Level</option>
                  <option>Intermediate Level</option>
                  <option>Advanced Level</option>
                  <option>Expert Level</option>
                </select>
                <svg class="absolute right-4 top-1/2 transform -translate-y-1/2 w-6 h-6 text-gray-400 pointer-events-none" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                </svg>
              </div>
            </div>

            <!-- Add Button -->
            <div class="flex justify-end">
              <button class="bg-[#3B82F6] hover:bg-[#2563EB] text-white px-12 py-4 rounded-full text-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                Add
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Add Lesson Page -->
      <div v-if="activeSection === 'courses' && showAddLessonPage" class="px-10 pt-8 pb-10 min-h-screen bg-[#F9FAFB]">
        <div class="max-w-4xl mx-auto">
          <!-- Back Button -->
          <button 
            @click="hideAddLesson" 
            class="mb-8 flex items-center gap-2 text-[#043355] hover:text-[#2563EB] transition-colors"
          >
            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
            العودة إلى الكورسات
          </button>

          <div class="bg-white rounded-3xl shadow-lg p-8">
            <!-- Grid Layout -->
            <div class="grid grid-cols-2 gap-8">
              <!-- Video Section -->
              <div>
                <label class="block text-[#374151] text-lg font-semibold mb-4">Video</label>
                <div class="relative">
                  <input 
                    type="text" 
                    placeholder="lesson1 Video" 
                    class="w-full p-4 text-base border border-[#D1D5DB] rounded-xl bg-[#F9FAFB] focus:outline-none focus:ring-2 focus:ring-[#3B82F6] focus:border-transparent transition-all pl-12"
                  />
                  <svg class="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"/>
                  </svg>
                </div>
              </div>

              <!-- Lesson Name Section -->
              <div>
                <label class="block text-[#374151] text-lg font-semibold mb-4">Lesson Name</label>
                <input 
                  type="text" 
                  placeholder="Basic part 1" 
                  class="w-full p-4 text-base border border-[#D1D5DB] rounded-xl bg-[#F9FAFB] focus:outline-none focus:ring-2 focus:ring-[#3B82F6] focus:border-transparent transition-all"
                />
              </div>

              <!-- PDF Section -->
              <div>
                <label class="block text-[#374151] text-lg font-semibold mb-4">PDF</label>
                <div class="relative">
                  <input 
                    type="text" 
                    placeholder="lesson1 pdf" 
                    class="w-full p-4 text-base border border-[#D1D5DB] rounded-xl bg-[#F9FAFB] focus:outline-none focus:ring-2 focus:ring-[#3B82F6] focus:border-transparent transition-all pl-12"
                  />
                  <svg class="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"/>
                  </svg>
                </div>
              </div>

              <!-- Quiz Section -->
              <div>
                <label class="block text-[#374151] text-lg font-semibold mb-4">Quiz</label>
                <div class="relative">
                  <input 
                    type="text" 
                    placeholder="lesson1Quiz" 
                    class="w-full p-4 text-base border border-[#D1D5DB] rounded-xl bg-[#F9FAFB] focus:outline-none focus:ring-2 focus:ring-[#3B82F6] focus:border-transparent transition-all pl-12"
                  />
                  <svg class="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"/>
                  </svg>
                </div>
              </div>
            </div>

            <!-- Description Section -->
            <div class="mt-8">
              <label class="block text-[#374151] text-lg font-semibold mb-4">Description</label>
              <textarea 
                placeholder="isl. quis orci leo. ipsum vitae at quam lacus, tincidunt diam laoreet sapien eu quam ipsum quis quis non vitae lacus, lorem. malesuada cursus dignissim, est.

dolor odio scelerisque e" 
                rows="6"
                class="w-full p-4 text-base border border-[#D1D5DB] rounded-xl bg-[#F9FAFB] focus:outline-none focus:ring-2 focus:ring-[#3B82F6] focus:border-transparent transition-all resize-none"
              ></textarea>
            </div>

            <!-- Add Button -->
            <div class="flex justify-end mt-8">
              <button class="bg-[#3B82F6] hover:bg-[#2563EB] text-white px-8 py-3 rounded-full text-lg font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                Add
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Edit Lesson Page -->
      <div v-if="activeSection === 'courses' && showEditLessonPage" class="px-10 pt-8 pb-10 min-h-screen bg-[#F9FAFB]">
        <div class="max-w-4xl mx-auto">
          <!-- Back Button -->
          <button 
            @click="hideEditLesson" 
            class="mb-8 flex items-center gap-2 text-[#043355] hover:text-[#2563EB] transition-colors"
          >
            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
            العودة إلى الكورسات
          </button>

          <div class="bg-white rounded-3xl shadow-lg p-8">
            <!-- Grid Layout -->
            <div class="grid grid-cols-2 gap-8">
              <!-- Video Section -->
              <div>
                <label class="block text-[#374151] text-lg font-semibold mb-4">Video</label>
                <div class="relative">
                  <input 
                    type="text" 
                    value="lesson1 Video" 
                    class="w-full p-4 text-base border border-[#D1D5DB] rounded-xl bg-[#F9FAFB] focus:outline-none focus:ring-2 focus:ring-[#3B82F6] focus:border-transparent transition-all pl-12"
                  />
                  <svg class="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"/>
                  </svg>
                </div>
              </div>

              <!-- Lesson Name Section -->
              <div>
                <label class="block text-[#374151] text-lg font-semibold mb-4">Lesson Name</label>
                <input 
                  type="text" 
                  value="Basic part 1" 
                  class="w-full p-4 text-base border border-[#D1D5DB] rounded-xl bg-[#F9FAFB] focus:outline-none focus:ring-2 focus:ring-[#3B82F6] focus:border-transparent transition-all"
                />
              </div>

              <!-- PDF Section -->
              <div>
                <label class="block text-[#374151] text-lg font-semibold mb-4">PDF</label>
                <div class="relative">
                  <input 
                    type="text" 
                    value="lesson1 pdf" 
                    class="w-full p-4 text-base border border-[#D1D5DB] rounded-xl bg-[#F9FAFB] focus:outline-none focus:ring-2 focus:ring-[#3B82F6] focus:border-transparent transition-all pl-12"
                  />
                  <svg class="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"/>
                  </svg>
                </div>
              </div>

              <!-- Quiz Section -->
              <div>
                <label class="block text-[#374151] text-lg font-semibold mb-4">Quiz</label>
                <div class="relative">
                  <input 
                    type="text" 
                    value="lesson1Quiz" 
                    class="w-full p-4 text-base border border-[#D1D5DB] rounded-xl bg-[#F9FAFB] focus:outline-none focus:ring-2 focus:ring-[#3B82F6] focus:border-transparent transition-all pl-12"
                  />
                  <svg class="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"/>
                  </svg>
                </div>
              </div>
            </div>

            <!-- Description Section -->
            <div class="mt-8">
              <label class="block text-[#374151] text-lg font-semibold mb-4">Description</label>
              <textarea 
                rows="6"
                class="w-full p-4 text-base border border-[#D1D5DB] rounded-xl bg-[#F9FAFB] focus:outline-none focus:ring-2 focus:ring-[#3B82F6] focus:border-transparent transition-all resize-none"
              >isl. quis orci leo. ipsum vitae at quam lacus, tincidunt diam laoreet sapien eu quam ipsum quis quis non vitae lacus, lorem. malesuada cursus dignissim, est.

dolor odio scelerisque e</textarea>
            </div>

            <!-- Update Button -->
            <div class="flex justify-end mt-8">
              <button class="bg-[#3B82F6] hover:bg-[#2563EB] text-white px-8 py-3 rounded-full text-lg font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                Update
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Contact & Help Section -->
      <div v-if="activeSection === 'contact'" class="px-10 pt-8 pb-10 min-h-screen">
        <div class="max-w-4xl mx-auto text-center">
          <div class="bg-white rounded-3xl shadow-lg p-16">
            <!-- Header -->
            <h1 class="text-5xl font-bold text-[#043355] mb-8">Contact Us</h1>
            
            <!-- Description Text -->
            <div class="text-[#6B7280] text-lg leading-relaxed mb-12 max-w-3xl mx-auto">
              <p class="mb-4">
                ex. vitae amet, massa non, gravida orci ex elit libero, nulla. Cras
                ex dignissim, molestias nec ullamcorper ultrices nunc massa
                scelerisque ex. Pharetra Lorem et.
              </p>
              <p class="mb-4">
                ex. placerat pharetra, egestas quam ac feilis ipsum nibh, vitae, Lorem
                amet, et nisl bibendum cursus ut lacus tristique turpis finibus
                quis finibus eiduspal tellus.
              </p>
            </div>

            <!-- Contact Buttons -->
            <div class="flex gap-6 justify-center">
              <!-- Contact with Admin Button -->
              <button 
                class="bg-[#25D366] hover:bg-[#20C05C] text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center gap-3 min-w-[250px]"
                @click="contactAdmin"
              >
                <svg width="24" height="24" fill="white" viewBox="0 0 24 24">
                  <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.488"/>
                </svg>
                Contact with Admin
              </button>

              <!-- Contact with Super Admin Button -->
              <button 
                class="bg-[#25D366] hover:bg-[#20C05C] text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center gap-3 min-w-[250px]"
                @click="contactSuperAdmin"
              >
                <svg width="24" height="24" fill="white" viewBox="0 0 24 24">
                  <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.488"/>
                </svg>
                Contact with Super Admin
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Settings Section -->
      <div v-if="activeSection === 'settings'" class="px-10 pt-8 pb-10 min-h-screen bg-[#E6EFFF]">
        <div class="max-w-4xl mx-auto">
          <div class="bg-white rounded-3xl shadow-lg p-12">
            <!-- Profile Section -->
            <div class="flex items-center gap-8 mb-12">
              <!-- Profile Picture -->
              <div class="relative">
                <img 
                  src="https://randomuser.me/api/portraits/men/32.jpg" 
                  alt="Gamal Yousef" 
                  class="w-32 h-32 rounded-full object-cover border-4 border-[#E5E7EB]"
                />
              </div>
              
              <!-- Profile Info -->
              <div>
                <h2 class="text-4xl font-bold text-[#043355] mb-2">Gamal Yousef</h2>
                <p class="text-[#6B7280] text-lg">Teacher</p>
              </div>
            </div>

            <!-- Form Fields -->
            <div class="space-y-6">
              <!-- First Name -->
              <div>
                <label class="block text-[#374151] text-lg font-semibold mb-3">first name</label>
                <input 
                  type="text" 
                  v-model="userInfo.firstName"
                  class="w-full p-4 text-base border border-[#D1D5DB] rounded-xl bg-[#F9FAFB] focus:outline-none focus:ring-2 focus:ring-[#3B82F6] focus:border-transparent transition-all"
                />
              </div>

              <!-- Last Name -->
              <div>
                <label class="block text-[#374151] text-lg font-semibold mb-3">last name</label>
                <input 
                  type="text" 
                  v-model="userInfo.lastName"
                  class="w-full p-4 text-base border border-[#D1D5DB] rounded-xl bg-[#F9FAFB] focus:outline-none focus:ring-2 focus:ring-[#3B82F6] focus:border-transparent transition-all"
                />
              </div>

              <!-- Phone Number -->
              <div>
                <label class="block text-[#374151] text-lg font-semibold mb-3">Phone Number</label>
                <input 
                  type="tel" 
                  v-model="userInfo.phoneNumber"
                  class="w-full p-4 text-base border border-[#D1D5DB] rounded-xl bg-[#F9FAFB] focus:outline-none focus:ring-2 focus:ring-[#3B82F6] focus:border-transparent transition-all"
                />
              </div>

              <!-- Father Number -->
              <div>
                <label class="block text-[#374151] text-lg font-semibold mb-3">Father Number</label>
                <input 
                  type="tel" 
                  v-model="userInfo.fatherNumber"
                  class="w-full p-4 text-base border border-[#D1D5DB] rounded-xl bg-[#F9FAFB] focus:outline-none focus:ring-2 focus:ring-[#3B82F6] focus:border-transparent transition-all"
                />
              </div>

              <!-- Email -->
              <div>
                <label class="block text-[#374151] text-lg font-semibold mb-3">Email</label>
                <input 
                  type="email" 
                  v-model="userInfo.email"
                  class="w-full p-4 text-base border border-[#D1D5DB] rounded-xl bg-[#F9FAFB] focus:outline-none focus:ring-2 focus:ring-[#3B82F6] focus:border-transparent transition-all"
                />
              </div>

              <!-- Password -->
              <div>
                <label class="block text-[#374151] text-lg font-semibold mb-3">Password</label>
                <input 
                  type="password" 
                  v-model="userInfo.password"
                  placeholder="•••••••••••••"
                  class="w-full p-4 text-base border border-[#D1D5DB] rounded-xl bg-[#F9FAFB] focus:outline-none focus:ring-2 focus:ring-[#3B82F6] focus:border-transparent transition-all"
                />
              </div>
            </div>

            <!-- Save Button -->
            <div class="flex justify-end mt-12">
              <button 
                @click="saveChanges"
                class="bg-[#FFC107] hover:bg-[#FFB300] text-white px-12 py-4 rounded-full text-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
              >
                Save Changes
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Placeholder for other sections -->
      <div v-if="activeSection !== 'dashboard' && activeSection !== 'groups' && activeSection !== 'teams' && activeSection !== 'courses' && activeSection !== 'contact' && activeSection !== 'settings'" class="px-10 pt-8 pb-10">
        <h1 class="text-4xl font-extrabold text-[#043355] mb-8 capitalize">{{ activeSection }}</h1>
        <div class="bg-white rounded-2xl shadow p-8 text-center">
          <p class="text-gray-500 text-lg">This section is coming soon...</p>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const activeSection = ref('dashboard') // افتراضياً الداشبورد نشط
const activeGroupTab = ref('waiting') // افتراضياً تبويب قائمة الانتظار نشط
const activeTeamTab = ref('waiting') // افتراضياً تبويب قائمة الانتظار نشط للفرق
const showAddGroupPage = ref(false) // متغير لإظهار صفحة إضافة جروب
const showAddTeamPage = ref(false) // متغير لإظهار صفحة إضافة تيم
const showAddCoursePage = ref(false) // متغير لإظهار صفحة إضافة كورس
const showAddLessonPage = ref(false) // متغير لإظهار صفحة إضافة درس
const showEditLessonPage = ref(false) // متغير لإظهار صفحة تعديل درس

// معلومات المستخدم للإعدادات
const userInfo = ref({
  firstName: 'Gamal',
  lastName: 'Youseef',
  phoneNumber: '+96645734758493',
  fatherNumber: '+96645734758493',
  email: '<EMAIL>',
  password: '•••••••••••••'
})

const showAddGroup = () => {
  showAddGroupPage.value = true
}

const hideAddGroup = () => {
  showAddGroupPage.value = false
}

const showAddTeam = () => {
  showAddTeamPage.value = true
}

const hideAddTeam = () => {
  showAddTeamPage.value = false
}

const showAddCourse = () => {
  showAddCoursePage.value = true
}

const hideAddCourse = () => {
  showAddCoursePage.value = false
}

const showAddLesson = () => {
  showAddLessonPage.value = true
}

const hideAddLesson = () => {
  showAddLessonPage.value = false
}

const showEditLesson = () => {
  showEditLessonPage.value = true
}

const hideEditLesson = () => {
  showEditLessonPage.value = false
}

// وظائف التواصل عبر واتساب
const contactAdmin = () => {
  console.log('Contacting Admin via WhatsApp...')
  const phoneNumber = '966501234567' // رقم هاتف الإدارة - أضف الرقم الحقيقي هنا
  const message = 'مرحباً، أحتاج للمساعدة من فضلك'
  const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`
  console.log('WhatsApp URL:', whatsappUrl)
  window.open(whatsappUrl, '_blank')
}

const contactSuperAdmin = () => {
  console.log('Contacting Super Admin via WhatsApp...')
  const phoneNumber = '966507654321' // رقم هاتف الإدارة العليا - أضف الرقم الحقيقي هنا
  const message = 'مرحباً، أحتاج للتواصل مع الإدارة العليا'
  const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`
  console.log('WhatsApp URL:', whatsappUrl)
  window.open(whatsappUrl, '_blank')
}

// وظيفة حفظ التغييرات
const saveChanges = () => {
  console.log('Saving user changes...', userInfo.value)
  // هنا يمكن إضافة منطق إرسال البيانات إلى الخادم
  alert('تم حفظ التغييرات بنجاح!')
}
</script>
