<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Navigation -->
    <NavigationBarSection />

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Loading State -->
      <div v-if="isLoading" class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="text-center py-12">
        <div class="text-red-500 text-lg mb-4">{{ error }}</div>
        <button @click="loadCourseData" class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors">
          Try Again
        </button>
      </div>

      <!-- Course Content -->
      <div v-else class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Video Player Section -->
        <div class="lg:col-span-3">
          <!-- Current Video -->
          <div v-if="currentLesson" class="bg-white rounded-2xl shadow-lg overflow-hidden mb-6">
            <VideoPlayer
              :lessonId="currentLesson.id"
              :showInfo="true"
              @subscribe="handleSubscribe"
              @videoLoad="onVideoLoad"
              @videoError="onVideoError"
              @progress="onProgress"
            />
          </div>

          <!-- Course Description -->
          <div v-if="courseData" class="bg-white rounded-2xl shadow-lg p-6 mb-6">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">{{ courseData.name }}</h1>
            <p class="text-gray-600 mb-4">{{ courseData.description }}</p>
            
            <!-- Course Stats -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div class="bg-blue-50 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-blue-600">{{ totalLessons }}</div>
                <div class="text-sm text-gray-600">Total Lessons</div>
              </div>
              <div class="bg-green-50 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-green-600">{{ completedLessons }}</div>
                <div class="text-sm text-gray-600">Completed</div>
              </div>
              <div class="bg-purple-50 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-purple-600">{{ Math.round(progressPercentage) }}%</div>
                <div class="text-sm text-gray-600">Progress</div>
              </div>
            </div>
          </div>

          <!-- Lesson Comments/Discussion -->
          <div class="bg-white rounded-2xl shadow-lg p-6">
            <h3 class="text-xl font-bold text-gray-900 mb-4">Discussion</h3>
            <div class="text-center py-8 text-gray-500">
              <svg class="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
              </svg>
              <p>Discussion feature coming soon!</p>
            </div>
          </div>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">
          <!-- Course Progress -->
          <div class="bg-white rounded-2xl shadow-lg p-6 mb-6">
            <h3 class="text-lg font-bold text-gray-900 mb-4">Your Progress</h3>
            <div class="space-y-4">
              <div class="flex justify-between text-sm">
                <span class="text-gray-600">Completed:</span>
                <span class="font-medium">{{ completedLessons }} / {{ totalLessons }}</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-3">
                <div 
                  class="bg-blue-500 h-3 rounded-full transition-all duration-300"
                  :style="{ width: `${progressPercentage}%` }"
                ></div>
              </div>
              <div class="text-center text-sm font-medium text-blue-600">
                {{ Math.round(progressPercentage) }}% Complete
              </div>
            </div>
          </div>

          <!-- Lesson Playlist -->
          <div class="bg-white rounded-2xl shadow-lg p-6">
            <h3 class="text-lg font-bold text-gray-900 mb-4">Course Lessons</h3>
            
            <!-- Course Levels -->
            <div v-for="level in courseLevels" :key="level.id" class="mb-6">
              <h4 class="text-md font-semibold text-gray-800 mb-3 flex items-center gap-2">
                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                  {{ level.title }}
                </span>
              </h4>
              
              <!-- Lessons in this level -->
              <div class="space-y-2">
                <div 
                  v-for="lesson in getLessonsForLevel(level.id)" 
                  :key="lesson.id"
                  @click="selectLesson(lesson)"
                  :class="[
                    'flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-all',
                    currentLesson?.id === lesson.id 
                      ? 'bg-blue-50 border-2 border-blue-200' 
                      : 'hover:bg-gray-50 border border-gray-200'
                  ]"
                >
                  <!-- Lesson Number -->
                  <div class="flex-shrink-0 w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-sm font-medium">
                    {{ lesson.sequence }}
                  </div>
                  
                  <!-- Lesson Info -->
                  <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 truncate">{{ lesson.title }}</p>
                    <p class="text-xs text-gray-500">Day {{ lesson.day_number }}</p>
                  </div>
                  
                  <!-- Status Icons -->
                  <div class="flex items-center gap-1">
                    <!-- Premium Badge -->
                    <span v-if="lesson.requires_payment" class="text-yellow-500" title="Premium Content">
                      <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                      </svg>
                    </span>
                    
                    <!-- Completion Status -->
                    <span v-if="isLessonCompleted(lesson.id)" class="text-green-500" title="Completed">
                      <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                      </svg>
                    </span>
                    
                    <!-- Play Icon -->
                    <span v-if="currentLesson?.id === lesson.id" class="text-blue-500">
                      <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"/>
                      </svg>
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- No Lessons -->
            <div v-if="allLessons.length === 0" class="text-center py-8 text-gray-500">
              <svg class="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
              </svg>
              <p>No lessons available yet.</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Subscription Modal -->
    <PaymentModal 
      v-if="showSubscriptionModal"
      :isOpen="showSubscriptionModal"
      :courseLevel="selectedCourseLevel"
      @close="showSubscriptionModal = false"
      @success="handleSubscriptionSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import NavigationBarSection from '../Home/sections/NavigationBarSection/NavigationBarSection.vue'
import VideoPlayer from '../../components/VideoPlayer.vue'
import PaymentModal from '../../components/PaymentModal.vue'
import { useVideos, type VideoLesson } from '../../composables/useVideos'
import { useCourses } from '../../composables/useCourses'

const route = useRoute()
const router = useRouter()

// Composables
const { 
  videos: allLessons, 
  currentVideo: currentLesson, 
  isLoading, 
  error, 
  fetchVideoLessons 
} = useVideos()

const { 
  courses, 
  courseLevels, 
  fetchCourse, 
  fetchCourseLevels 
} = useCourses()

// State
const courseData = ref(null)
const selectedCourseLevel = ref(null)
const showSubscriptionModal = ref(false)
const completedLessonIds = ref<number[]>([])

// Computed
const courseId = computed(() => parseInt(route.params.courseId as string))

const totalLessons = computed(() => allLessons.value.length)

const completedLessons = computed(() => completedLessonIds.value.length)

const progressPercentage = computed(() => {
  if (totalLessons.value === 0) return 0
  return (completedLessons.value / totalLessons.value) * 100
})

// Methods
const loadCourseData = async () => {
  if (!courseId.value) return

  try {
    // Load course data
    const course = await fetchCourse(courseId.value)
    courseData.value = course

    // Load course levels
    await fetchCourseLevels(courseId.value)

    // Load video lessons for this course
    await fetchVideoLessons({ course_id: courseId.value })

    // Select first lesson if available
    if (allLessons.value.length > 0) {
      selectLesson(allLessons.value[0])
    }

  } catch (err) {
    console.error('Failed to load course data:', err)
  }
}

const getLessonsForLevel = (levelId: number) => {
  return allLessons.value.filter(lesson => lesson.course_level_id === levelId)
    .sort((a, b) => a.sequence - b.sequence)
}

const selectLesson = (lesson: VideoLesson) => {
  currentLesson.value = lesson
  // Update URL to reflect current lesson
  router.replace({ 
    name: 'video-course', 
    params: { courseId: courseId.value }, 
    query: { lesson: lesson.id } 
  })
}

const isLessonCompleted = (lessonId: number) => {
  return completedLessonIds.value.includes(lessonId)
}

const handleSubscribe = () => {
  showSubscriptionModal.value = true
}

const handleSubscriptionSuccess = () => {
  showSubscriptionModal.value = false
  // Refresh lesson access
  loadCourseData()
}

const onVideoLoad = (lesson: VideoLesson) => {
  console.log('Video loaded:', lesson.title)
}

const onVideoError = (error: string) => {
  console.error('Video error:', error)
}

const onProgress = (progressData: any) => {
  // Mark lesson as completed when 90% watched
  if (progressData.percentage >= 90 && currentLesson.value) {
    if (!isLessonCompleted(currentLesson.value.id)) {
      completedLessonIds.value.push(currentLesson.value.id)
      // TODO: Save progress to backend
    }
  }
}

// Initialize
onMounted(() => {
  loadCourseData()
  
  // Check for lesson query parameter
  const lessonId = route.query.lesson
  if (lessonId) {
    // Will be handled after lessons are loaded
  }
})
</script>
