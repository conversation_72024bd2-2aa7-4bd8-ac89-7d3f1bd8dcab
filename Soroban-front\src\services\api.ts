// API Service for Soroban Frontend
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  errors?: Record<string, string[]>;
}

export interface User {
  id: number;
  first_name: string;
  second_name: string;
  email: string;
  role: 'superAdmin' | 'admin' | 'teacher' | 'student' | 'guest';
  age: number;
  age_group: 'kids' | 'adults';
  user_type: 'regular' | 'company';
  account_status: 'active' | 'inactive';
  email_verified_at: string | null;
  profile?: UserProfile;
  company?: Company;
}

export interface UserProfile {
  id: number;
  user_id: number;
  phone?: string;
  birth_date?: string;
  address?: string;
  parent_phone?: string;
  profile_picture?: string;
  bio?: string;
}

export interface Company {
  id: number;
  name: string;
  email: string;
  phone: string;
  address: string;
}

export interface Course {
  id: number;
  title: string;
  description: string;
  price: number;
  duration: number;
  level: string;
  status: 'active' | 'inactive';
  image?: string;
  created_at: string;
  updated_at: string;
}

export interface CourseLevel {
  id: number;
  course_id: number;
  level_number: number;
  title: string;
  description: string;
  price: number;
  duration: number;
  status: 'active' | 'inactive';
  course?: Course;
}

export interface Lesson {
  id: number;
  course_level_id: number;
  title: string;
  description: string;
  content: string;
  video_url?: string;
  duration: number;
  order: number;
  status: 'active' | 'inactive';
  course_level?: CourseLevel;
}

export interface Quiz {
  id: number;
  lesson_id: number;
  title: string;
  description: string;
  questions: QuizQuestion[];
  time_limit: number;
  passing_score: number;
  status: 'active' | 'inactive';
}

export interface QuizQuestion {
  id: number;
  quiz_id: number;
  question: string;
  options: string[];
  correct_answer: string;
  points: number;
}

export interface Exam {
  id: number;
  course_level_id: number;
  title: string;
  description: string;
  questions: ExamQuestion[];
  time_limit: number;
  passing_score: number;
  status: 'active' | 'inactive';
}

export interface ExamQuestion {
  id: number;
  exam_id: number;
  question: string;
  options: string[];
  correct_answer: string;
  points: number;
}

export interface StudentProgress {
  id: number;
  student_id: number;
  course_level_id: number;
  lesson_id?: number;
  progress_percentage: number;
  completed_at?: string;
  status: 'in_progress' | 'completed';
}

export interface Certificate {
  id: number;
  student_id: number;
  course_level_id: number;
  certificate_number: string;
  issued_at: string;
  status: 'active' | 'revoked';
}

export interface Competition {
  id: number;
  title: string;
  description: string;
  type: 'monthly' | 'seasonal';
  start_date: string;
  end_date: string;
  prize: string;
  status: 'upcoming' | 'ongoing' | 'completed';
}

export interface Subscription {
  id: number;
  user_id: number;
  course_level_id: number;
  status: 'active' | 'inactive' | 'expired';
  subscribed_at: string;
  expires_at?: string;
}

export interface Payment {
  id: number;
  user_id: number;
  subscription_id: number;
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed';
  payment_method: string;
  transaction_id?: string;
  paid_at?: string;
}

export interface Group {
  id: number;
  name: string;
  description: string;
  whatsapp_link: string;
  max_members: number;
  current_members: number;
  status: 'active' | 'inactive';
}

export interface Notification {
  id: number;
  user_id: number;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  read_at?: string;
  created_at: string;
}

export interface LoginRequest {
  email: string;
  password: string;
  remember?: boolean;
}

export interface LoginResponse {
  user: User;
  token: string;
  token_type: string;
}

export interface RegisterRequest {
  first_name: string;
  second_name: string;
  email: string;
  password: string;
  password_confirmation: string;
  age: number;
  role: 'teacher' | 'student';
  age_group: 'kids' | 'adults';
  user_type?: 'regular' | 'company';
  company_id?: number;
}

class ApiService {
  private baseURL: string;
  private token: string | null = null;

  constructor() {
    this.baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api';
    this.token = localStorage.getItem('auth_token');
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...options.headers,
    };

    if (this.token) {
      (headers as Record<string, string>)['Authorization'] = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // Authentication methods
  async login(credentials: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    const response = await this.request<LoginResponse>('/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });

    if (response.success && response.data) {
      this.setToken(response.data.token);
      localStorage.setItem('user', JSON.stringify(response.data.user));
    }

    return response;
  }

  async register(userData: RegisterRequest): Promise<ApiResponse<LoginResponse>> {
    return this.request<LoginResponse>('/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async logout(): Promise<ApiResponse> {
    const response = await this.request('/logout', {
      method: 'POST',
    });

    if (response.success) {
      this.clearAuth();
    }

    return response;
  }

  async getUser(): Promise<ApiResponse<User>> {
    return this.request<User>('/user');
  }

  async forgotPassword(email: string): Promise<ApiResponse> {
    return this.request('/forgot-password', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });
  }

  async resetPassword(data: {
    email: string;
    password: string;
    password_confirmation: string;
    token: string;
  }): Promise<ApiResponse> {
    return this.request('/reset-password', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Course methods
  async getCourses(): Promise<ApiResponse<Course[]>> {
    return this.request('/courses');
  }

  async getCourse(id: number): Promise<ApiResponse<Course>> {
    return this.request(`/courses/${id}`);
  }

  async createCourse(courseData: Partial<Course>): Promise<ApiResponse<Course>> {
    return this.request('/courses', {
      method: 'POST',
      body: JSON.stringify(courseData),
    });
  }

  async updateCourse(id: number, courseData: Partial<Course>): Promise<ApiResponse<Course>> {
    return this.request(`/courses/${id}`, {
      method: 'PUT',
      body: JSON.stringify(courseData),
    });
  }

  async deleteCourse(id: number): Promise<ApiResponse> {
    return this.request(`/courses/${id}`, {
      method: 'DELETE',
    });
  }

  // Course Level methods
  async getCourseLevels(courseId?: number): Promise<ApiResponse<CourseLevel[]>> {
    const endpoint = courseId ? `/courses/${courseId}/levels` : '/course-levels';
    return this.request(endpoint);
  }

  async getCourseLevel(id: number): Promise<ApiResponse<CourseLevel>> {
    return this.request(`/course-levels/${id}`);
  }

  async createCourseLevel(levelData: Partial<CourseLevel>): Promise<ApiResponse<CourseLevel>> {
    return this.request('/course-levels', {
      method: 'POST',
      body: JSON.stringify(levelData),
    });
  }

  async updateCourseLevel(id: number, levelData: Partial<CourseLevel>): Promise<ApiResponse<CourseLevel>> {
    return this.request(`/course-levels/${id}`, {
      method: 'PUT',
      body: JSON.stringify(levelData),
    });
  }

  async deleteCourseLevel(id: number): Promise<ApiResponse> {
    return this.request(`/course-levels/${id}`, {
      method: 'DELETE',
    });
  }

  // Lesson methods
  async getLessons(): Promise<ApiResponse<Lesson[]>> {
    return this.request('/lessons');
  }

  async getLesson(id: number): Promise<ApiResponse<Lesson>> {
    return this.request(`/lessons/${id}`);
  }

  async getLessonsByLevel(levelId: number): Promise<ApiResponse<Lesson[]>> {
    return this.request(`/course-levels/${levelId}/lessons`);
  }

  async createLesson(lessonData: Partial<Lesson>): Promise<ApiResponse<Lesson>> {
    return this.request('/lessons', {
      method: 'POST',
      body: JSON.stringify(lessonData),
    });
  }

  async updateLesson(id: number, lessonData: Partial<Lesson>): Promise<ApiResponse<Lesson>> {
    return this.request(`/lessons/${id}`, {
      method: 'PUT',
      body: JSON.stringify(lessonData),
    });
  }

  async deleteLesson(id: number): Promise<ApiResponse> {
    return this.request(`/lessons/${id}`, {
      method: 'DELETE',
    });
  }

  // Quiz methods
  async getQuizzes(): Promise<ApiResponse<Quiz[]>> {
    return this.request('/quizzes');
  }

  async getQuiz(id: number): Promise<ApiResponse<Quiz>> {
    return this.request(`/quizzes/${id}`);
  }

  async getQuizByLesson(lessonId: number): Promise<ApiResponse<Quiz>> {
    return this.request(`/lessons/${lessonId}/quiz`);
  }

  async createQuiz(quizData: Partial<Quiz>): Promise<ApiResponse<Quiz>> {
    return this.request('/quizzes', {
      method: 'POST',
      body: JSON.stringify(quizData),
    });
  }

  async updateQuiz(id: number, quizData: Partial<Quiz>): Promise<ApiResponse<Quiz>> {
    return this.request(`/quizzes/${id}`, {
      method: 'PUT',
      body: JSON.stringify(quizData),
    });
  }

  async deleteQuiz(id: number): Promise<ApiResponse> {
    return this.request(`/quizzes/${id}`, {
      method: 'DELETE',
    });
  }

  // Exam methods
  async getExams(): Promise<ApiResponse<Exam[]>> {
    return this.request('/exams');
  }

  async getExam(id: number): Promise<ApiResponse<Exam>> {
    return this.request(`/exams/${id}`);
  }

  async getExamsByLevel(levelId: number): Promise<ApiResponse<Exam[]>> {
    return this.request(`/course-levels/${levelId}/exams`);
  }

  async createExam(examData: Partial<Exam>): Promise<ApiResponse<Exam>> {
    return this.request('/exams', {
      method: 'POST',
      body: JSON.stringify(examData),
    });
  }

  async updateExam(id: number, examData: Partial<Exam>): Promise<ApiResponse<Exam>> {
    return this.request(`/exams/${id}`, {
      method: 'PUT',
      body: JSON.stringify(examData),
    });
  }

  async deleteExam(id: number): Promise<ApiResponse> {
    return this.request(`/exams/${id}`, {
      method: 'DELETE',
    });
  }

  // Student Progress methods
  async getStudentProgress(): Promise<ApiResponse<StudentProgress[]>> {
    return this.request('/student-progress');
  }

  async getMyProgress(): Promise<ApiResponse<StudentProgress[]>> {
    return this.request('/student/my-progress');
  }

  async updateProgress(progressData: Partial<StudentProgress>): Promise<ApiResponse<StudentProgress>> {
    return this.request('/student-progress', {
      method: 'POST',
      body: JSON.stringify(progressData),
    });
  }

  // Certificate methods
  async getCertificates(): Promise<ApiResponse<Certificate[]>> {
    return this.request('/certificates');
  }

  async getMyCertificates(): Promise<ApiResponse<Certificate[]>> {
    return this.request('/student/my-certificates');
  }

  async issueCertificate(certificateData: { student_id: number; course_level_id: number }): Promise<ApiResponse<Certificate>> {
    return this.request('/certificates/issue', {
      method: 'POST',
      body: JSON.stringify(certificateData),
    });
  }

  // Competition methods
  async getCompetitions(): Promise<ApiResponse<Competition[]>> {
    return this.request('/competitions');
  }

  async getCompetition(id: number): Promise<ApiResponse<Competition>> {
    return this.request(`/competitions/${id}`);
  }

  async participateInCompetition(competitionId: number): Promise<ApiResponse> {
    return this.request(`/competitions/${competitionId}/participate`, {
      method: 'POST',
    });
  }

  async createCompetition(competitionData: Partial<Competition>): Promise<ApiResponse<Competition>> {
    return this.request('/competitions', {
      method: 'POST',
      body: JSON.stringify(competitionData),
    });
  }

  // Subscription methods
  async getSubscriptions(): Promise<ApiResponse<Subscription[]>> {
    return this.request('/subscriptions');
  }

  async getMyCourses(): Promise<ApiResponse<Subscription[]>> {
    return this.request('/student/my-courses');
  }

  async subscribe(courseLevelId: number): Promise<ApiResponse<Subscription>> {
    return this.request('/subscriptions', {
      method: 'POST',
      body: JSON.stringify({ course_level_id: courseLevelId }),
    });
  }

  async unsubscribe(subscriptionId: number): Promise<ApiResponse> {
    return this.request(`/subscriptions/${subscriptionId}`, {
      method: 'DELETE',
    });
  }

  // Payment methods
  async getPayments(filters?: {
    student_id?: number
    confirmed?: boolean
    from_date?: string
    to_date?: string
    per_page?: number
  }): Promise<ApiResponse<Payment[]>> {
    const params = new URLSearchParams()
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, value.toString())
        }
      })
    }
    const queryString = params.toString()
    return this.request(`/payments${queryString ? `?${queryString}` : ''}`)
  }

  async getPayment(paymentId: number): Promise<ApiResponse<Payment>> {
    return this.request(`/payments/${paymentId}`)
  }

  async createPayment(paymentData: {
    student_id: number
    level_id: number
    subscription_id: number
    amount: number
    transaction_code?: string
    transaction_pic?: string
    subscription_start_date?: string
    subscription_end_date?: string
  }): Promise<ApiResponse<Payment>> {
    return this.request('/payments', {
      method: 'POST',
      body: JSON.stringify(paymentData),
    })
  }

  async updatePayment(paymentId: number, paymentData: Partial<Payment>): Promise<ApiResponse<Payment>> {
    return this.request(`/payments/${paymentId}`, {
      method: 'PUT',
      body: JSON.stringify(paymentData),
    })
  }

  async deletePayment(paymentId: number): Promise<ApiResponse> {
    return this.request(`/payments/${paymentId}`, {
      method: 'DELETE',
    })
  }

  async confirmPayment(paymentId: number): Promise<ApiResponse<Payment>> {
    return this.request(`/payments/${paymentId}/confirm`, {
      method: 'POST',
    })
  }

  async getStudentPayments(studentId: number): Promise<ApiResponse<Payment[]>> {
    return this.request(`/students/${studentId}/payments`)
  }

  async getPaymentReports(): Promise<ApiResponse<any>> {
    return this.request('/admin/reports/payments')
  }

  // Notification methods
  async getNotifications(): Promise<ApiResponse<Notification[]>> {
    return this.request('/notifications');
  }

  async markNotificationAsRead(notificationId: number): Promise<ApiResponse> {
    return this.request(`/notifications/${notificationId}/mark-read`, {
      method: 'POST',
    });
  }

  async markAllNotificationsAsRead(): Promise<ApiResponse> {
    return this.request('/notifications/mark-all-read', {
      method: 'POST',
    });
  }

  // Group methods
  async getGroups(): Promise<ApiResponse<Group[]>> {
    return this.request('/groups');
  }

  async getMyGroups(): Promise<ApiResponse<Group[]>> {
    return this.request('/student/my-groups');
  }

  async joinGroup(groupId: number): Promise<ApiResponse> {
    return this.request(`/groups/${groupId}/add-member`, {
      method: 'POST',
    });
  }

  // Dashboard methods
  async getStudentDashboard(): Promise<ApiResponse<any>> {
    return this.request('/student/dashboard');
  }

  async getTeacherDashboard(): Promise<ApiResponse<any>> {
    return this.request('/teacher/dashboard');
  }

  async getAdminDashboard(): Promise<ApiResponse<any>> {
    return this.request('/admin/dashboard');
  }

  // User management methods
  async getUsers(): Promise<ApiResponse<User[]>> {
    return this.request('/users');
  }

  async createUser(userData: Partial<User>): Promise<ApiResponse<User>> {
    return this.request('/users', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async updateUser(id: number, userData: Partial<User>): Promise<ApiResponse<User>> {
    return this.request(`/users/${id}`, {
      method: 'PUT',
      body: JSON.stringify(userData),
    });
  }

  async deleteUser(id: number): Promise<ApiResponse> {
    return this.request(`/users/${id}`, {
      method: 'DELETE',
    });
  }

  async changeUserStatus(id: number, status: string): Promise<ApiResponse> {
    return this.request(`/users/${id}/change-status`, {
      method: 'POST',
      body: JSON.stringify({ status }),
    });
  }

  // Video and YouTube methods
  async getVideoLessons(filters?: {
    course_id?: number
    course_level_id?: number
    requires_payment?: boolean
  }): Promise<ApiResponse<any[]>> {
    const params = new URLSearchParams()
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, value.toString())
        }
      })
    }
    const queryString = params.toString()
    return this.request(`/videos/lessons${queryString ? `?${queryString}` : ''}`)
  }

  async getVideoLesson(lessonId: number): Promise<ApiResponse<any>> {
    return this.request(`/lessons/${lessonId}`)
  }

  async getPlaylistEmbed(lessonId: number, options?: {
    autoplay?: boolean
    start?: number
    end?: number
  }): Promise<ApiResponse<any>> {
    const params = new URLSearchParams()
    if (options) {
      Object.entries(options).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, value.toString())
        }
      })
    }
    const queryString = params.toString()
    return this.request(`/videos/lessons/${lessonId}/embed${queryString ? `?${queryString}` : ''}`)
  }

  async createVideoLesson(lessonData: any): Promise<ApiResponse<any>> {
    return this.request('/lessons', {
      method: 'POST',
      body: JSON.stringify(lessonData),
    })
  }

  async updateVideoLesson(lessonId: number, lessonData: any): Promise<ApiResponse<any>> {
    return this.request(`/lessons/${lessonId}`, {
      method: 'PUT',
      body: JSON.stringify(lessonData),
    })
  }

  async deleteVideoLesson(lessonId: number): Promise<ApiResponse> {
    return this.request(`/lessons/${lessonId}`, {
      method: 'DELETE',
    })
  }

  async checkVideoAccess(lessonId: number): Promise<ApiResponse<any>> {
    return this.request(`/videos/lessons/${lessonId}/access`)
  }

  async getMyVideoLessons(): Promise<ApiResponse<any[]>> {
    return this.request('/videos/my-lessons')
  }

  // Course management methods
  async getCoursesWithLessons(): Promise<ApiResponse<any[]>> {
    return this.request('/courses?include=levels.lessons')
  }

  async getCourseWithVideos(courseId: number): Promise<ApiResponse<any>> {
    return this.request(`/courses/${courseId}?include=levels.lessons`)
  }

  async getCourseLevelWithVideos(levelId: number): Promise<ApiResponse<any>> {
    return this.request(`/course-levels/${levelId}?include=lessons`)
  }

  async checkCourseAccess(courseLevelId: number): Promise<ApiResponse<any>> {
    return this.request(`/course-levels/${courseLevelId}/access`)
  }

  // File upload methods
  async uploadFile(file: File, type: string = 'general'): Promise<ApiResponse<any>> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);

    return this.request('/attachments/upload', {
      method: 'POST',
      body: formData,
      headers: {
        // Don't set Content-Type for FormData, let browser set it
      },
    });
  }

  // Utility methods
  setToken(token: string): void {
    this.token = token;
    localStorage.setItem('auth_token', token);
  }

  getToken(): string | null {
    return this.token || localStorage.getItem('auth_token');
  }

  clearAuth(): void {
    this.token = null;
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user');
  }

  isAuthenticated(): boolean {
    return !!this.getToken();
  }

  getCurrentUser(): User | null {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  }
}

export const apiService = new ApiService();
export default apiService;
