import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import { screenGraphPlugin } from "@animaapp/vite-plugin-screen-graph";
import path from "path";

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue(), screenGraphPlugin()],
  publicDir: "./static",
  base: "./",
  resolve: {
    alias: {
      "src": path.resolve(__dirname, "./src"),
    },
  },
  server: {
    port: 5173,
    host: true,
  },
});
