@echo off
echo Starting Soroban Development Environment...
echo.

echo Starting Laravel Backend API...
start "Laravel Backend" cmd /k "cd Soroban-Backend && php artisan serve"

echo Waiting for backend to start...
timeout /t 3 /nobreak > nul

echo Starting Vue.js Frontend...
start "Vue.js Frontend" cmd /k "cd Soroban-front && npm run dev"

echo.
echo Development servers are starting...
echo Backend API: http://localhost:8000
echo Frontend: http://localhost:5173
echo API Documentation: http://localhost:8000/api/documentation
echo.
echo Press any key to close this window...
pause > nul
